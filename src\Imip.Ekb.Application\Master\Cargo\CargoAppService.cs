using System;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using Imip.Ekb.Mapping.Mappers;
using Imip.Ekb.Master.Cargos.Dtos;
using Imip.Ekb.Models;
using Imip.Ekb.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.Ekb.Master.Cargos;

[Authorize]
public class CargoAppService :
    CrudAppService<Cargo, CargoDto, Guid, PagedAndSortedResultRequestDto, CargoCreateUpdateDto, CargoCreateUpdateDto>,
    ICargoAppService
{
    private readonly ICargoRepository _cargoRepository;
    private readonly CargoMapper _mapper;
    private readonly ILogger<CargoAppService> _logger;

    public CargoAppService(
        ICargoRepository cargoRepository,
        CargoMapper mapper,
        ILogger<CargoAppService> logger)
        : base(cargoRepository)
    {
        _cargoRepository = cargoRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public override async Task<CargoDto> CreateAsync(CargoCreateUpdateDto input)
    {
        await CheckCreatePolicyAsync();

        var entity = _mapper.CreateEntityWithId(input, Guid.NewGuid());
        entity.CreatedAt = Clock.Now;
        entity.CreatedBy = CurrentUser.UserName ?? "System";

        await _cargoRepository.InsertAsync(entity, autoSave: true);

        return _mapper.MapToDto(entity);
    }

    public override async Task<CargoDto> UpdateAsync(Guid id, CargoCreateUpdateDto input)
    {
        await CheckUpdatePolicyAsync();

        var entity = await _cargoRepository.GetAsync(id);

        // Preserve original creation info
        var originalCreatedBy = entity.CreatedBy;
        var originalCreatedAt = entity.CreatedAt;

        _mapper.MapToEntity(input, entity);

        entity.CreatedBy = originalCreatedBy;
        entity.CreatedAt = originalCreatedAt;
        entity.UpdatedAt = Clock.Now;

        await _cargoRepository.UpdateAsync(entity, autoSave: true);

        return _mapper.MapToDto(entity);
    }

    public override async Task DeleteAsync(Guid id)
    {
        await CheckDeletePolicyAsync();

        var entity = await _cargoRepository.GetAsync(id);

        // Soft delete
        entity.Status = "Deleted";
        entity.UpdatedAt = Clock.Now;

        await _cargoRepository.UpdateAsync(entity, autoSave: true);
    }

    public override async Task<CargoDto> GetAsync(Guid id)
    {
        await CheckGetPolicyAsync();

        var entity = await _cargoRepository.GetAsync(id);
        return _mapper.MapToDto(entity);
    }

    public override async Task<PagedResultDto<CargoDto>> GetListAsync(PagedAndSortedResultRequestDto input)
    {
        await CheckGetListPolicyAsync();

        var queryable = await _cargoRepository.GetOptimizedQueryableAsync();
        var sortedQueryable = queryable.OrderBy(input.Sorting.IsNullOrWhiteSpace() ? nameof(Cargo.DocEntry) : input.Sorting);

        var totalCount = await AsyncExecuter.CountAsync(queryable);

        var entities = await AsyncExecuter.ToListAsync(
            sortedQueryable.PageBy(input.SkipCount, input.MaxResultCount)
        );

        var dtos = entities.Select(_mapper.MapToDto).ToList();

        return new PagedResultDto<CargoDto>(totalCount, dtos);
    }

    public virtual async Task<PagedResultDto<CargoDto>> FilterListAsync(QueryParametersDto parameters)
    {
        // Check cache first (implement IDistributedCache)
        // var cachedResult = await _cache.GetAsync<PagedResultDto<MasterTenantDto>>(cacheKey);
        // if (cachedResult != null) return cachedResult;

        var query = await _cargoRepository.GetOptimizedQueryableAsync();

        // Apply filters before projection
        query = ApplyDynamicQuery(query, parameters);

        // Get total count efficiently (before projection)
        var totalCount = await AsyncExecuter.CountAsync(query);

        // Apply pagination before projection to reduce memory usage
        var pagedQuery = query
            .Skip(parameters.SkipCount)
            .Take(parameters.MaxResultCount);

        // Use direct projection to avoid loading full entities
        var items = await AsyncExecuter.ToListAsync(
            pagedQuery
            .Select(CargoMapper.ProjectionExpression)
        );

        var result = new PagedResultDto<CargoDto>
        {
            TotalCount = totalCount,
            Items = items
        };

        // Cache the result for 5 minutes
        // await _cache.SetAsync(cacheKey, result, TimeSpan.FromMinutes(5));

        return result;
    }

    private IQueryable<Cargo> ApplyDynamicQuery(IQueryable<Cargo> query, QueryParametersDto parameters)
    {
        if (parameters.FilterGroup?.Conditions != null && parameters.FilterGroup.Conditions.Count > 0)
        {
            query = DynamicQueryBuilder<Cargo>.ApplyFilters(query, parameters.FilterGroup);
        }
        if (parameters.Sort?.Count > 0)
        {
            query = DynamicQueryBuilder<Cargo>.ApplyMultipleSorting(query, parameters.Sort);
        }
        else if (!string.IsNullOrEmpty(parameters.Sorting))
        {
            query = query.OrderBy(parameters.Sorting);
        }
        else
        {
            query = query.OrderByDescending(x => x.CreationTime);
        }
        return query;
    }
}