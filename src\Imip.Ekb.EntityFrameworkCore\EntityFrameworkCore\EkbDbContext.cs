using Imip.Ekb.BoundedZone;
using Imip.Ekb.BoundedZone.ExportVessels;
using Imip.Ekb.BoundedZone.ImportVessels;
using Imip.Ekb.BoundedZone.LocalVessels;
using Imip.Ekb.Master.Agents;
using Imip.Ekb.Master.BcTypes;
using Imip.Ekb.Master.BusinessPartners;
using Imip.Ekb.Master.Cargos;
using Imip.Ekb.Master.DestinationPorts;
using Imip.Ekb.Master.ItemClassifications;
using Imip.Ekb.Master.Jetties;
using Imip.Ekb.Master.PortOfLoadings;
using Imip.Ekb.Master.PortServices;
using Imip.Ekb.Master.Surveyors;
using Microsoft.EntityFrameworkCore;
using Volo.Abp.AuditLogging.EntityFrameworkCore;
using Volo.Abp.BackgroundJobs.EntityFrameworkCore;
using Volo.Abp.BlobStoring.Database.EntityFrameworkCore;
using Volo.Abp.Data;
using Volo.Abp.DependencyInjection;
using Volo.Abp.EntityFrameworkCore;
using Volo.Abp.FeatureManagement.EntityFrameworkCore;
using Volo.Abp.Identity;
using Volo.Abp.Identity.EntityFrameworkCore;
using Volo.Abp.OpenIddict.EntityFrameworkCore;
using Volo.Abp.PermissionManagement.EntityFrameworkCore;
using Volo.Abp.SettingManagement.EntityFrameworkCore;
using Volo.Abp.TenantManagement;
using Volo.Abp.TenantManagement.EntityFrameworkCore;
using Imip.Ekb.EntityTypeConfigurations;
using Imip.Ekb.Attachments;
using Imip.Ekb.Master.Tenants;
using Imip.Ekb.Billing.LocalVesselBillings;
using Imip.Ekb.BoundedZone.TradingVessels.TradingInvoice;
using Imip.Ekb.BoundedZone.TradingVessels;
using Imip.Ekb.BoundedZone.ImportVessels.ZoneDetailInvoice;
using Imip.Ekb.Billing.ImportVesselBillings;
using Imip.Ekb.Billing.ExportVesselBillings;
using Imip.Ekb.Master.ExportClassifications;

namespace Imip.Ekb.EntityFrameworkCore;

[ReplaceDbContext(typeof(IIdentityDbContext))]
[ReplaceDbContext(typeof(ITenantManagementDbContext))]
[ConnectionStringName("Default")]
public class EkbDbContext :
    AbpDbContext<EkbDbContext>,
    ITenantManagementDbContext,
    IIdentityDbContext
{
    /* Add DbSet properties for your Aggregate Roots / Entities here. */
    public DbSet<Agent> Agents { get; set; }
    public DbSet<BcType> BcTypes { get; set; }
    public DbSet<BillingItem> BillingItems { get; set; }
    public DbSet<BusinessPartner> BusinessPartners { get; set; }
    public DbSet<Cargo> Cargos { get; set; }
    public DbSet<DestinationPort> DestinationPorts { get; set; }
    public DbSet<DocAttachment> DocAttachments { get; set; }
    public DbSet<ExportVesselBilling> ExportVesselBillings { get; set; }
    public DbSet<ExportVessel> ExportVessels { get; set; }
    public DbSet<ImportVesselBilling> ImportVesselBillings { get; set; }
    public DbSet<ImportVessel> ImportVessels { get; set; }
    public DbSet<ItemClassification> ItemClassifications { get; set; }
    public DbSet<Jetty> Jetties { get; set; }
    public DbSet<LocalVesselBilling> LocalVesselBillings { get; set; }
    public DbSet<LocalVessel> LocalVessels { get; set; }
    public DbSet<MasterTenant> MasterTenants { get; set; }
    public DbSet<PortOfLoading> PortOfLoadings { get; set; }
    public DbSet<PortService> PortServices { get; set; }
    public DbSet<Surveyor> Surveyors { get; set; }
    public DbSet<TenantGroup> TenantGroups { get; set; }
    public DbSet<TradingInvoice> TradingInvoices { get; set; }
    public DbSet<TradingVessel> TradingVessels { get; set; }
    public DbSet<ZoneDetail> ZoneDetails { get; set; }
    public DbSet<ZoneDetailInvoice> ZoneDetailInvoices { get; set; }
    public DbSet<ExportClassification> ExportClassifications { get; set; }

    #region Entities from the modules

    /* Notice: We only implemented IIdentityProDbContext and ISaasDbContext
     * and replaced them for this DbContext. This allows you to perform JOIN
     * queries for the entities of these modules over the repositories easily. You
     * typically don't need that for other modules. But, if you need, you can
     * implement the DbContext interface of the needed module and use ReplaceDbContext
     * attribute just like IIdentityProDbContext and ISaasDbContext.
     *
     * More info: Replacing a DbContext of a module ensures that the related module
     * uses this DbContext on runtime. Otherwise, it will use its own DbContext class.
     */

    // Identity
    public DbSet<IdentityUser> Users { get; set; }
    public DbSet<IdentityRole> Roles { get; set; }
    public DbSet<IdentityClaimType> ClaimTypes { get; set; }
    public DbSet<OrganizationUnit> OrganizationUnits { get; set; }
    public DbSet<IdentitySecurityLog> SecurityLogs { get; set; }
    public DbSet<IdentityLinkUser> LinkUsers { get; set; }
    public DbSet<IdentityUserDelegation> UserDelegations { get; set; }
    public DbSet<IdentitySession> Sessions { get; set; }

    // Tenant Management
    public DbSet<Tenant> Tenants { get; set; }
    public DbSet<TenantConnectionString> TenantConnectionStrings { get; set; }

    #endregion

    public EkbDbContext(DbContextOptions<EkbDbContext> options)
        : base(options)
    {

    }

    protected override void OnModelCreating(ModelBuilder builder)
    {
        base.OnModelCreating(builder);

        /* Include modules to your migration db context */

        builder.ConfigurePermissionManagement();
        builder.ConfigureSettingManagement();
        builder.ConfigureBackgroundJobs();
        builder.ConfigureAuditLogging();
        builder.ConfigureFeatureManagement();
        builder.ConfigureIdentity();
        builder.ConfigureOpenIddict();
        builder.ConfigureTenantManagement();
        builder.ConfigureBlobStoring();

        /* Configure your own tables/entities inside here */


        #region SilkierQuartz
        var prefix = "Quartz";
        var schema = "Silkier";

        builder.ApplyConfiguration(
          new SilkierQuartzExecutionHistoryEntityTypeConfiguration(prefix, schema));

        builder.ApplyConfiguration(
          new SilkierQuartzJobSummaryEntityTypeConfiguration(prefix, schema));
        #endregion

        #region Quartz
        var quartzPrefix = "QRTZ_";
        var quartzSchema = "Quartz";

        builder.ApplyConfiguration(new QuartzJobDetailEntityTypeConfiguration(quartzPrefix, quartzSchema));

        builder.ApplyConfiguration(new QuartzTriggerEntityTypeConfiguration(quartzPrefix, quartzSchema));

        builder.ApplyConfiguration(new QuartzSimpleTriggerEntityTypeConfiguration(quartzPrefix, quartzSchema));

        builder.ApplyConfiguration(new QuartzSimplePropertyTriggerEntityTypeConfiguration(quartzPrefix, quartzSchema));

        builder.ApplyConfiguration(new QuartzCronTriggerEntityTypeConfiguration(quartzPrefix, quartzSchema));

        builder.ApplyConfiguration(new QuartzBlobTriggerEntityTypeConfiguration(quartzPrefix, quartzSchema));

        builder.ApplyConfiguration(new QuartzCalendarEntityTypeConfiguration(quartzPrefix, quartzSchema));

        builder.ApplyConfiguration(new QuartzPausedTriggerGroupEntityTypeConfiguration(quartzPrefix, quartzSchema));

        builder.ApplyConfiguration(new QuartzFiredTriggerEntityTypeConfiguration(quartzPrefix, quartzSchema));

        builder.ApplyConfiguration(new QuartzSchedulerStateEntityTypeConfiguration(quartzPrefix, quartzSchema));

        builder.ApplyConfiguration(new QuartzLockEntityTypeConfiguration(quartzPrefix, quartzSchema));
        #endregion

        #region Ekb

        builder.ApplyConfiguration(new EkbAgentConfiguration(EkbConsts.DbTablePrefix, EkbConsts.DbSchema));
        builder.ApplyConfiguration(new EkbBcTypeConfiguration(EkbConsts.DbTablePrefix, EkbConsts.DbSchema));
        builder.ApplyConfiguration(new EkbBusinessPartnerConfiguration(EkbConsts.DbTablePrefix, EkbConsts.DbSchema));
        builder.ApplyConfiguration(new EkbCargoConfiguration(EkbConsts.DbTablePrefix, EkbConsts.DbSchema));
        builder.ApplyConfiguration(new EkbDestinationPortConfiguration(EkbConsts.DbTablePrefix, EkbConsts.DbSchema));
        builder.ApplyConfiguration(new EkbItemClassificationClassification(EkbConsts.DbTablePrefix, EkbConsts.DbSchema));
        builder.ApplyConfiguration(new EkbJettyConfiguration(EkbConsts.DbTablePrefix, EkbConsts.DbSchema));
        builder.ApplyConfiguration(new EkbPortOfLoadingConfiguration(EkbConsts.DbTablePrefix, EkbConsts.DbSchema));
        builder.ApplyConfiguration(new EkbPortServiceConfiguration(EkbConsts.DbTablePrefix, EkbConsts.DbSchema));
        builder.ApplyConfiguration(new EkbSurveyorConfiguration(EkbConsts.DbTablePrefix, EkbConsts.DbSchema));
        builder.ApplyConfiguration(new EkbMasterTenantConfiguration(EkbConsts.DbTablePrefix, EkbConsts.DbSchema));
        builder.ApplyConfiguration(new EkbTenantGroupConfiguration(EkbConsts.DbTablePrefix, EkbConsts.DbSchema));
        builder.ApplyConfiguration(new EkbExportVesselBillingConfiguration(EkbConsts.DbTablePrefix, EkbConsts.DbSchema));
        builder.ApplyConfiguration(new EkbImportVesselBillingConfiguration(EkbConsts.DbTablePrefix, EkbConsts.DbSchema));
        builder.ApplyConfiguration(new EkbLocalVesselBillingConfiguration(EkbConsts.DbTablePrefix, EkbConsts.DbSchema));
        builder.ApplyConfiguration(new EkbBillingItemConfiguration(EkbConsts.DbTablePrefix, EkbConsts.DbSchema));
        builder.ApplyConfiguration(new EkbImportVesselConfiguration(EkbConsts.DbTablePrefix, EkbConsts.DbSchema));
        builder.ApplyConfiguration(new EkbExportVesselConfiguration(EkbConsts.DbTablePrefix, EkbConsts.DbSchema));
        builder.ApplyConfiguration(new EkbLocalVesselConfiguration(EkbConsts.DbTablePrefix, EkbConsts.DbSchema));
        builder.ApplyConfiguration(new EkbTradingVesselConfiguration(EkbConsts.DbTablePrefix, EkbConsts.DbSchema));
        builder.ApplyConfiguration(new EkbTradingInvoiceConfiguration(EkbConsts.DbTablePrefix, EkbConsts.DbSchema));
        builder.ApplyConfiguration(new EkbDocAttachmentConfiguration(EkbConsts.DbTablePrefix, EkbConsts.DbSchema));
        builder.ApplyConfiguration(new EkbZoneDetailConfiguration(EkbConsts.DbTablePrefix, EkbConsts.DbSchema));
        builder.ApplyConfiguration(new EkbZoneDetailInvoiceConfiguration(EkbConsts.DbTablePrefix, EkbConsts.DbSchema));
        builder.ApplyConfiguration(new EkbExportClassificationConfiguration(EkbConsts.DbTablePrefix, EkbConsts.DbSchema));

        #endregion

    }
}
