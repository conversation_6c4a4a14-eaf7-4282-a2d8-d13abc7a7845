using System;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Entities;

namespace Imip.Ekb.Master.PortServices.Dtos;

public class PortServiceDto : AuditedEntityDto<Guid>, IHasConcurrencyStamp
{
    public string ConcurrencyStamp { get; set; }
    public int DocEntry { get; set; }
    public string? ItemCode { get; set; }
    public string? ItemName { get; set; }
    public string Active { get; set; } = null!;
    public int? CreatedBy { get; set; }
    public int? UpdatedBy { get; set; }
    public string? FrgnName { get; set; }
    public string? SapCodeS4 { get; set; }
}