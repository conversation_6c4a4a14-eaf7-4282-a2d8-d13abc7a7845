﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Volo.Abp.Domain.Entities.Auditing;

namespace Imip.Ekb.BoundedZone.TradingVessels.TradingInvoice.TradingInvoiceItem;

[Table("T_MDOC_inv_sub")]
public class TradingInvoiceItem : FullAuditedAggregateRoot<Guid>
{
    [Key]
    public int DocEntry { get; set; }

    public int DocNum { get; set; }

    [StringLength(255)]
    public string ItemCode { get; set; } = null!;

    [StringLength(255)]
    public string ItemName { get; set; } = null!;

    [Column(TypeName = "decimal(20, 4)")]
    public decimal Qty { get; set; }

    [StringLength(255)]
    public string UoM { get; set; } = null!;

    [Column(TypeName = "decimal(20, 4)")]
    public decimal UnitPrice { get; set; }

    [Column(TypeName = "decimal(20, 4)")]
    public decimal LineTotal { get; set; }

    [StringLength(255)]
    public string? HsCode { get; set; }

    [StringLength(255)]
    public string ContractNo { get; set; } = null!;

    [Column("Created_by")]
    [StringLength(255)]
    public string? CreatedBy { get; set; }

    [Column("Updated_by")]
    [StringLength(255)]
    public string? UpdatedBy { get; set; }

    [StringLength(255)]
    public string Flags { get; set; } = null!;

    [Column("isScan")]
    [StringLength(255)]
    public string? IsScan { get; set; }

    [Column("isOriginal")]
    [StringLength(255)]
    public string? IsOriginal { get; set; }

    [Column("isSend")]
    [StringLength(255)]
    public string? IsSend { get; set; }

    [Column("isFeOri")]
    [StringLength(255)]
    public string? IsFeOri { get; set; }

    [Column("isFeSend")]
    [StringLength(255)]
    public string? IsFeSend { get; set; }

    [StringLength(255)]
    public string? MatchKey { get; set; }

    public int Category { get; set; }

    [StringLength(255)]
    public string CategoryDesc { get; set; } = null!;

    [Column("created_at", TypeName = "datetime")]
    public DateTime? CreatedAt { get; set; }

    [Column("updated_at", TypeName = "datetime")]
    public DateTime? UpdatedAt { get; set; }

    public string? FrgnName { get; set; }

    [Column("ItemNameSAP")]
    public string? ItemNameSap { get; set; }

    [StringLength(10)]
    public string Deleted { get; set; } = null!;

    [StringLength(200)]
    public string? ParentKey { get; set; }

    [Column("BLKey")]
    public int? Blkey { get; set; }

    public int? InvKey { get; set; }

    public int? InvDetailKey { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? DeletedAt { get; set; }

    [StringLength(200)]
    public string? DeleteBy { get; set; }

    [Column(TypeName = "numeric(20, 4)")]
    public decimal? ActQty { get; set; }

    [Column(TypeName = "numeric(20, 4)")]
    public decimal? ActUnitPrice { get; set; }

    [Column(TypeName = "numeric(20, 4)")]
    public decimal? ActTotal { get; set; }

    [StringLength(255)]
    public string Type { get; set; } = null!;

    [StringLength(100)]
    public string? OpenDate { get; set; }

    [StringLength(100)]
    public string? UpdateDate { get; set; }

    [Column(TypeName = "decimal(20, 4)")]
    public decimal? GrossWeight { get; set; }



}
