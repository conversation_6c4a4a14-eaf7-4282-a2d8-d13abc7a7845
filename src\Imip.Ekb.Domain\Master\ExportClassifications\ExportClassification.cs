using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Imip.Ekb.BoundedZone;
using Volo.Abp.Domain.Entities.Auditing;

namespace Imip.Ekb.Master.ExportClassifications;

[Table("ExportClassification")]
public class ExportClassification : FullAuditedAggregateRoot<Guid>
{
    [StringLength(26)]
    public string? DocEntry { get; set; }

    [StringLength(255)]
    public string Name { get; set; } = null!;

    [StringLength(20)]
    public string Status { get; set; } = null!;

    public long CreatedBy { get; set; }

    [Column("created_at", TypeName = "datetime")]
    public DateTime? CreatedAt { get; set; }

    [Column("updated_at", TypeName = "datetime")]
    public DateTime? UpdatedAt { get; set; }

    public virtual ICollection<ZoneDetail>? VesselTransactions { get; set; }



}
