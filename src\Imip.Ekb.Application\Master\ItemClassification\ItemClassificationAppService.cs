using System;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using Imip.Ekb.Mapping.Mappers;
using Imip.Ekb.Master.ItemClassifications.Dtos;
using Imip.Ekb.Models;
using Imip.Ekb.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.Ekb.Master.ItemClassifications;

[Authorize]
public class ItemClassificationAppService :
    CrudAppService<ItemClassification, ItemClassificationDto, Guid, PagedAndSortedResultRequestDto, ItemClassificationCreateUpdateDto, ItemClassificationCreateUpdateDto>,
    IItemClassificationAppService
{
    private readonly IItemClassificationRepository _itemClassificationRepository;
    private readonly ItemClassificationMapper _mapper;
    private readonly ILogger<ItemClassificationAppService> _logger;

    public ItemClassificationAppService(
        IItemClassificationRepository itemClassificationRepository,
        ItemClassificationMapper mapper,
        ILogger<ItemClassificationAppService> logger)
        : base(itemClassificationRepository)
    {
        _itemClassificationRepository = itemClassificationRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public override async Task<ItemClassificationDto> CreateAsync(ItemClassificationCreateUpdateDto input)
    {
        await CheckCreatePolicyAsync();
        var entity = _mapper.CreateEntityWithId(input, Guid.NewGuid());
        entity.CreatedAt = Clock.Now;
        await _itemClassificationRepository.InsertAsync(entity, autoSave: true);
        return _mapper.MapToDto(entity);
    }

    public override async Task<ItemClassificationDto> UpdateAsync(Guid id, ItemClassificationCreateUpdateDto input)
    {
        await CheckUpdatePolicyAsync();
        var entity = await _itemClassificationRepository.GetAsync(id);
        var originalCreatedBy = entity.CreatedBy;
        var originalCreatedAt = entity.CreatedAt;
        _mapper.MapToEntity(input, entity);
        entity.CreatedBy = originalCreatedBy;
        entity.CreatedAt = originalCreatedAt;
        entity.UpdatedAt = Clock.Now;
        await _itemClassificationRepository.UpdateAsync(entity, autoSave: true);
        return _mapper.MapToDto(entity);
    }

    public override async Task DeleteAsync(Guid id)
    {
        await CheckDeletePolicyAsync();
        var entity = await _itemClassificationRepository.GetAsync(id);
        entity.Deleted = "Y";
        entity.UpdatedAt = Clock.Now;
        await _itemClassificationRepository.UpdateAsync(entity, autoSave: true);
    }

    public override async Task<ItemClassificationDto> GetAsync(Guid id)
    {
        await CheckGetPolicyAsync();
        var entity = await _itemClassificationRepository.GetAsync(id);
        return _mapper.MapToDto(entity);
    }

    public override async Task<PagedResultDto<ItemClassificationDto>> GetListAsync(PagedAndSortedResultRequestDto input)
    {
        await CheckGetListPolicyAsync();
        var queryable = await _itemClassificationRepository.GetQueryableAsync();
        var sortedQueryable = queryable.OrderBy(input.Sorting.IsNullOrWhiteSpace() ? nameof(ItemClassification.DocEntry) : input.Sorting);
        var totalCount = await AsyncExecuter.CountAsync(queryable);
        var entities = await AsyncExecuter.ToListAsync(
            sortedQueryable.PageBy(input.SkipCount, input.MaxResultCount)
        );
        var dtos = entities.Select(_mapper.MapToDto).ToList();
        return new PagedResultDto<ItemClassificationDto>(totalCount, dtos);
    }

    public virtual async Task<PagedResultDto<ItemClassificationDto>> FilterListAsync(QueryParametersDto parameters)
    {
        var query = await _itemClassificationRepository.GetQueryableAsync();
        query = ApplyDynamicQuery(query, parameters);
        var totalCount = await AsyncExecuter.CountAsync(query);
        var items = await AsyncExecuter.ToListAsync(
            query.Select(ItemClassificationMapper.ProjectionExpression)
                .Skip(parameters.SkipCount).Take(parameters.MaxResultCount)
        );
        return new PagedResultDto<ItemClassificationDto>
        {
            TotalCount = totalCount,
            Items = items
        };
    }

    private IQueryable<ItemClassification> ApplyDynamicQuery(IQueryable<ItemClassification> query, QueryParametersDto parameters)
    {
        if (parameters.FilterGroup?.Conditions != null && parameters.FilterGroup.Conditions.Count > 0)
        {
            query = DynamicQueryBuilder<ItemClassification>.ApplyFilters(query, parameters.FilterGroup);
        }
        if (parameters.Sort?.Count > 0)
        {
            query = DynamicQueryBuilder<ItemClassification>.ApplyMultipleSorting(query, parameters.Sort);
        }
        else if (!string.IsNullOrEmpty(parameters.Sorting))
        {
            query = query.OrderBy(parameters.Sorting);
        }
        else
        {
            query = query.OrderByDescending(x => x.CreationTime);
        }
        return query;
    }
}