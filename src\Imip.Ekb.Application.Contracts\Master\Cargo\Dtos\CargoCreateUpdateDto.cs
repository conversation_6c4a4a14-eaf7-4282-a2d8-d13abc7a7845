using System;
using System.ComponentModel.DataAnnotations;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Entities;

namespace Imip.Ekb.Master.Cargos.Dtos;

public class CargoCreateUpdateDto : EntityDto<Guid>, IHasConcurrencyStamp
{
    public string ConcurrencyStamp { get; set; }
    // [Required]
    // public int DocEntry { get; set; }

    [Required]
    [StringLength(255)]
    public string Name { get; set; } = string.Empty;

    [Required]
    [StringLength(255)]
    public string CreatedBy { get; set; } = string.Empty;

    [Required]
    [StringLength(255)]
    public string Status { get; set; } = string.Empty;

    [StringLength(100)]
    public string? Alias { get; set; }

    [StringLength(200)]
    public string? Flag { get; set; }

    [Required]
    public decimal GrossWeight { get; set; }

    [StringLength(255)]
    public string? Type { get; set; }

    public decimal? LoaQty { get; set; }
}