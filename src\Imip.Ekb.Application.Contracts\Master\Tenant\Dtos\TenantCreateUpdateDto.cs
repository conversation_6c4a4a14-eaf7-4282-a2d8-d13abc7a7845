using System;
using System.ComponentModel.DataAnnotations;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Entities;

namespace Imip.Ekb.Master.Tenants.Dtos;

public class TenantCreateUpdateDto : EntityDto<Guid>, IHasConcurrencyStamp
{
    public string ConcurrencyStamp { get; set; }
    // [Required]
    // public int DocEntry { get; set; }

    [Required]
    [StringLength(255)]
    public string Name { get; set; } = null!;

    [Required]
    [StringLength(255)]
    public string CreatedBy { get; set; } = null!;

    [StringLength(20)]
    public string? Flags { get; set; }

    public string? FullName { get; set; }

    [StringLength(250)]
    public string? LetterPerson { get; set; }

    [StringLength(250)]
    public string? LetterRole { get; set; }

    [StringLength(250)]
    public string? Npwp { get; set; }

    public string? Address { get; set; }

    [StringLength(200)]
    public string? Nib { get; set; }

    [StringLength(200)]
    public string? Phone { get; set; }

    [Required]
    [StringLength(255)]
    public string Status { get; set; } = null!;

    public string? NoAndDateNotaris { get; set; }
    public string? DescNotaris { get; set; }

    [StringLength(100)]
    public string? Sapcode { get; set; }

    [Required]
    [StringLength(10)]
    public string IsExternal { get; set; } = null!;

    [StringLength(100)]
    public string? Billing { get; set; }

    public decimal? BillingPrice { get; set; }

    [StringLength(255)]
    public string? EsignUserId { get; set; }

    public string? Token { get; set; }

    [StringLength(100)]
    public string? SapcodeBdt { get; set; }

    [StringLength(255)]
    public string? SapcodeUsd { get; set; }

    [StringLength(255)]
    public string? Coordinate { get; set; }

    [StringLength(255)]
    public string? Boundaries { get; set; }

    [StringLength(255)]
    public string? IsTenant { get; set; }

    [StringLength(255)]
    public string? ChannelId { get; set; }

    [Required]
    [StringLength(10)]
    public string UsePrivy { get; set; } = null!;

    [StringLength(30)]
    public string? SapcodeS4 { get; set; }

    [StringLength(10)]
    public string? Skbpph { get; set; }

    [StringLength(255)]
    public string? CompanyGroup { get; set; }

    [StringLength(255)]
    public string? FactoryLocation { get; set; }

    public long? MasterGroupId { get; set; }
}