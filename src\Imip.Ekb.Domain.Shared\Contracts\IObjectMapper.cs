﻿using System.Collections.Generic;
using System.Linq;

namespace Imip.Ekb.Contracts;
public interface IObjectMapper
{
    TDestination Map<TDestination>(object source);
    TDestination Map<TSource, TDestination>(TSource source);
    TDestination Map<TSource, TDestination>(TSource source, TDestination destination);
    List<TDestination> Map<TSource, TDestination>(IEnumerable<TSource> source);
    IQueryable<TDestination> ProjectTo<TDestination>(IQueryable source);
}