using Imip.Ekb.Models;
using System;
using System.Threading.Tasks;
using Imip.Ekb.Master.Tenants.Dtos;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.Ekb.Master.Tenants;

public interface ITenantAppService :
    ICrudAppService<MasterTenantDto, Guid, PagedAndSortedResultRequestDto, TenantCreateUpdateDto, TenantCreateUpdateDto>
{
    Task<PagedResultDto<MasterTenantDto>> FilterListAsync(QueryParametersDto parameters);
}