﻿<Project Sdk="Microsoft.NET.Sdk">

  <Import Project="..\..\common.props" />

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <RootNamespace>Imip.Ekb</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Master\Trading\**" />
    <EmbeddedResource Remove="Master\Trading\**" />
    <None Remove="Master\Trading\**" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Imip.Ekb.Domain\Imip.Ekb.Domain.csproj" />
    <ProjectReference Include="..\Imip.Ekb.Application.Contracts\Imip.Ekb.Application.Contracts.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Delta" Version="6.4.2" />
    <PackageReference Include="Microsoft.IO.RecyclableMemoryStream" Version="3.0.1" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.3.0" />
    <PackageReference Include="Volo.Abp.PermissionManagement.Application" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.FeatureManagement.Application" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.SettingManagement.Application" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.Account.Application" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.Identity.Application" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.TenantManagement.Application" Version="9.1.1" />
    <PackageReference Include="Riok.Mapperly" Version="4.2.1" />
  </ItemGroup>

</Project>
