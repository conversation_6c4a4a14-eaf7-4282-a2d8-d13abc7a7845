using System;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Entities;

namespace Imip.Ekb.Master.Surveyors.Dtos;

public class SurveyorDto : AuditedEntityDto<Guid>, IHasConcurrencyStamp
{
    public string ConcurrencyStamp { get; set; }
    public long DocEntry { get; set; }
    public string Name { get; set; } = null!;
    public string? Address { get; set; }
    public string? Npwp { get; set; }
    public string IsActive { get; set; } = null!;
    public long CreatedBy { get; set; }
    public long? UpdatedBy { get; set; }
}