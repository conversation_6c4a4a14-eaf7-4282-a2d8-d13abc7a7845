using Imip.Ekb.BoundedZone.ImportVessels;
using Imip.Ekb.BoundedZone.ImportVessels.Dtos;
using Riok.Mapperly.Abstractions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using Imip.Ekb.BoundedZone.ImportVessel;
using Imip.Ekb.Master.Jetties;
using Imip.Ekb.Master.Jetties.Dtos;
using Imip.Ekb.Master.Cargos;
using Imip.Ekb.Master.Cargos.Dtos;
using Imip.Ekb.Master.Agents;
using Imip.Ekb.Master.Agents.Dtos;
using Imip.Ekb.Master.Tradings;
using Imip.Ekb.Master.Tradings.Dtos;
using Imip.Ekb.Master.Surveyors;
using Imip.Ekb.Master.Surveyors.Dtos;
using Imip.Ekb.Master.PortOfLoadings;
using Imip.Ekb.Master.PortOfLoadings.Dtos;
using Imip.Ekb.Master.DestinationPorts;
using Imip.Ekb.Master.DestinationPorts.Dtos;
using Imip.Ekb.BoundedZone.Dtos;
using Imip.Ekb.Master.Dtos;
using Imip.Ekb.Master.Tenants;
using Imip.Ekb.Master.BcTypes;
using Imip.Ekb.Master.BusinessPartners;
using Imip.Ekb.BoundedZone;
using Imip.Ekb.BoundedZone;

namespace Imip.Ekb.Mapping.Mappers;

[Mapper]
public partial class ImportVesselMapper : IMapperlyMapper
{
    // Configure global mapping rules for nested navigation properties
    // This helps Mapperly handle navigation properties automatically while ignoring
    // properties that shouldn't be mapped (like collections and audit fields)

    // Configure mapping for Jetty navigation property
    [MapperIgnoreSource(nameof(Jetty.BillingItems))]
    [MapperIgnoreSource(nameof(Jetty.ImportVessels))]
    [MapperIgnoreSource(nameof(Jetty.ExportVessels))]
    [MapperIgnoreSource(nameof(Jetty.LocalVessels))]
    [MapperIgnoreSource(nameof(Jetty.ImportVesselBillings))]
    [MapperIgnoreSource(nameof(Jetty.ExportVesselBillings))]
    [MapperIgnoreSource(nameof(Jetty.LocalVesselBillings))]
    [MapperIgnoreSource(nameof(Jetty.IsDeleted))]
    [MapperIgnoreSource(nameof(Jetty.DeleterId))]
    [MapperIgnoreSource(nameof(Jetty.DeletionTime))]
    [MapperIgnoreSource(nameof(Jetty.ExtraProperties))]
    [MapperIgnoreSource(nameof(Jetty.ConcurrencyStamp))]
    private partial JettyDto MapJetty(Jetty jetty);

    // Configure mapping for Cargo (Vessel) navigation property
    [MapperIgnoreSource(nameof(Cargo.ImportVessels))]
    [MapperIgnoreSource(nameof(Cargo.ExportVessels))]
    [MapperIgnoreSource(nameof(Cargo.LocalVessels))]
    [MapperIgnoreSource(nameof(Cargo.LocalBarges))]
    [MapperIgnoreSource(nameof(Cargo.IsDeleted))]
    [MapperIgnoreSource(nameof(Cargo.DeleterId))]
    [MapperIgnoreSource(nameof(Cargo.DeletionTime))]
    [MapperIgnoreSource(nameof(Cargo.ExtraProperties))]
    [MapperIgnoreSource(nameof(Cargo.ConcurrencyStamp))]
    private partial CargoDto MapCargo(Cargo cargo);

    // Configure mapping for Agent navigation property
    [MapperIgnoreSource(nameof(Agent.LocalVessels))]
    [MapperIgnoreSource(nameof(Agent.ExportVessels))]
    [MapperIgnoreSource(nameof(Agent.ImportVessels))]
    [MapperIgnoreSource(nameof(Agent.VesselTransactions))]
    [MapperIgnoreSource(nameof(Agent.IsDeleted))]
    [MapperIgnoreSource(nameof(Agent.DeleterId))]
    [MapperIgnoreSource(nameof(Agent.DeletionTime))]
    [MapperIgnoreSource(nameof(Agent.ExtraProperties))]
    [MapperIgnoreSource(nameof(Agent.ConcurrencyStamp))]
    private partial AgentDto MapAgent(Agent agent);

    // Configure mapping for Trading navigation property
    [MapperIgnoreSource(nameof(Trading.LocalVessels))]
    [MapperIgnoreSource(nameof(Trading.ExportVessels))]
    [MapperIgnoreSource(nameof(Trading.ImportVessels))]
    [MapperIgnoreSource(nameof(Trading.IsDeleted))]
    [MapperIgnoreSource(nameof(Trading.DeleterId))]
    [MapperIgnoreSource(nameof(Trading.DeletionTime))]
    [MapperIgnoreSource(nameof(Trading.ExtraProperties))]
    [MapperIgnoreSource(nameof(Trading.ConcurrencyStamp))]
    private partial TradingDto MapTrading(Trading trading);

    // Configure mapping for Surveyor navigation property
    [MapperIgnoreSource(nameof(Surveyor.CreatedAt))]
    [MapperIgnoreSource(nameof(Surveyor.UpdatedAt))]
    [MapperIgnoreSource(nameof(Surveyor.LocalVessels))]
    [MapperIgnoreSource(nameof(Surveyor.ExportVessels))]
    [MapperIgnoreSource(nameof(Surveyor.ImportVessels))]
    [MapperIgnoreSource(nameof(Surveyor.IsDeleted))]
    [MapperIgnoreSource(nameof(Surveyor.DeleterId))]
    [MapperIgnoreSource(nameof(Surveyor.DeletionTime))]
    [MapperIgnoreSource(nameof(Surveyor.ExtraProperties))]
    [MapperIgnoreSource(nameof(Surveyor.ConcurrencyStamp))]
    private partial SurveyorDto MapSurveyor(Surveyor surveyor);

    // Configure mapping for PortOfLoading navigation property
    [MapperIgnoreSource(nameof(PortOfLoading.LocalVessels))]
    [MapperIgnoreSource(nameof(PortOfLoading.ExportVessels))]
    [MapperIgnoreSource(nameof(PortOfLoading.ImportVessels))]
    [MapperIgnoreSource(nameof(PortOfLoading.IsDeleted))]
    [MapperIgnoreSource(nameof(PortOfLoading.DeleterId))]
    [MapperIgnoreSource(nameof(PortOfLoading.DeletionTime))]
    [MapperIgnoreSource(nameof(PortOfLoading.ExtraProperties))]
    [MapperIgnoreSource(nameof(PortOfLoading.ConcurrencyStamp))]
    private partial PortOfLoadingDto MapPortOfLoading(PortOfLoading portOfLoading);

    // Configure mapping for DestinationPort navigation property
    [MapperIgnoreSource(nameof(DestinationPort.LocalVessels))]
    [MapperIgnoreSource(nameof(DestinationPort.ExportVessels))]
    [MapperIgnoreSource(nameof(DestinationPort.ImportVessels))]
    [MapperIgnoreSource(nameof(DestinationPort.IsDeleted))]
    [MapperIgnoreSource(nameof(DestinationPort.DeleterId))]
    [MapperIgnoreSource(nameof(DestinationPort.DeletionTime))]
    [MapperIgnoreSource(nameof(DestinationPort.ExtraProperties))]
    [MapperIgnoreSource(nameof(DestinationPort.ConcurrencyStamp))]
    private partial DestinationPortDto MapDestinationPort(DestinationPort destinationPort);

    // Entity to DTO mapping - let Mapperly handle automatic mapping for matching properties
    // Only ignore properties that should not be mapped or need special handling
    [MapperIgnoreSource(nameof(ImportVessel.Items))] // Collection handled separately
    [MapperIgnoreSource(nameof(ImportVessel.IsDeleted))] // ABP audit property not in DTO
    [MapperIgnoreSource(nameof(ImportVessel.DeleterId))] // ABP audit property not in DTO
    [MapperIgnoreSource(nameof(ImportVessel.DeletionTime))] // ABP audit property not in DTO
    [MapperIgnoreSource(nameof(ImportVessel.ExtraProperties))] // ABP property not in DTO
    public partial ImportVesselDto MapToDto(ImportVessel entity);

    // DTO to Entity mapping for updates (maps to existing entity)
    // Ignore properties that should not be overwritten during updates
    [MapperIgnoreTarget(nameof(ImportVessel.Id))] // Don't change existing Id
    [MapperIgnoreTarget(nameof(ImportVessel.DocEntry))] // Don't change existing DocEntry
    [MapperIgnoreTarget(nameof(ImportVessel.CreationTime))] // ABP audit fields
    [MapperIgnoreTarget(nameof(ImportVessel.CreatorId))]
    [MapperIgnoreTarget(nameof(ImportVessel.LastModificationTime))]
    [MapperIgnoreTarget(nameof(ImportVessel.LastModifierId))]
    [MapperIgnoreTarget(nameof(ImportVessel.IsDeleted))]
    [MapperIgnoreTarget(nameof(ImportVessel.DeleterId))]
    [MapperIgnoreTarget(nameof(ImportVessel.DeletionTime))]
    [MapperIgnoreTarget(nameof(ImportVessel.ExtraProperties))]
    [MapperIgnoreTarget(nameof(ImportVessel.MasterJetty))] // Navigation properties handled separately
    [MapperIgnoreTarget(nameof(ImportVessel.Vessel))]
    [MapperIgnoreTarget(nameof(ImportVessel.Items))]
    [MapperIgnoreTarget(nameof(ImportVessel.MasterAgent))]
    [MapperIgnoreTarget(nameof(ImportVessel.MasterTrading))]
    [MapperIgnoreTarget(nameof(ImportVessel.MasterSurveyor))]
    [MapperIgnoreTarget(nameof(ImportVessel.MasterPortOrigin))]
    [MapperIgnoreTarget(nameof(ImportVessel.MasterDestinationPort))]
    [MapperIgnoreSource(nameof(CreateUpdateImportVesselDto.Items))] // Collection handled separately
    public partial void MapToEntity(CreateUpdateImportVesselDto dto, ImportVessel entity);

    // DTO to Entity mapping for creation
    // Ignore properties that will be set by framework or should not be mapped from DTOs
    [MapperIgnoreTarget(nameof(ImportVessel.Id))] // Will be set by EF Core
    [MapperIgnoreTarget(nameof(ImportVessel.DocEntry))] // Will be set by EF Core
    [MapperIgnoreTarget(nameof(ImportVessel.CreationTime))] // ABP audit fields
    [MapperIgnoreTarget(nameof(ImportVessel.CreatorId))]
    [MapperIgnoreTarget(nameof(ImportVessel.LastModificationTime))]
    [MapperIgnoreTarget(nameof(ImportVessel.LastModifierId))]
    [MapperIgnoreTarget(nameof(ImportVessel.IsDeleted))]
    [MapperIgnoreTarget(nameof(ImportVessel.DeleterId))]
    [MapperIgnoreTarget(nameof(ImportVessel.DeletionTime))]
    [MapperIgnoreTarget(nameof(ImportVessel.ExtraProperties))]
    [MapperIgnoreTarget(nameof(ImportVessel.MasterJetty))] // Navigation properties handled separately
    [MapperIgnoreTarget(nameof(ImportVessel.Vessel))]
    [MapperIgnoreTarget(nameof(ImportVessel.Items))]
    [MapperIgnoreTarget(nameof(ImportVessel.MasterAgent))]
    [MapperIgnoreTarget(nameof(ImportVessel.MasterTrading))]
    [MapperIgnoreTarget(nameof(ImportVessel.MasterSurveyor))]
    [MapperIgnoreTarget(nameof(ImportVessel.MasterPortOrigin))]
    [MapperIgnoreTarget(nameof(ImportVessel.MasterDestinationPort))]
    [MapperIgnoreSource(nameof(CreateUpdateImportVesselDto.Items))] // Collection handled separately
    public partial ImportVessel MapToEntity(CreateUpdateImportVesselDto dto);

    // Collection mapping methods - let Mapperly handle automatic collection mapping
    public partial List<ImportVesselDto> MapToDtoList(List<ImportVessel> entities);
    public partial IEnumerable<ImportVesselDto> MapToDtoEnumerable(IEnumerable<ImportVessel> entities);

    // Collection mapping methods for WithItems DTOs
    public partial List<ImportVesselWithItemsDto> MapToWithItemsDtoList(List<ImportVessel> entities);
    public partial IEnumerable<ImportVesselWithItemsDto> MapToWithItemsDtoEnumerable(IEnumerable<ImportVessel> entities);

    // Collection mapping methods for VesselItemDto
    public List<VesselItemDto> MapZoneDetailsToVesselItemDtos(ICollection<ZoneDetail>? zoneDetails)
    {
        if (zoneDetails == null || !zoneDetails.Any())
            return new List<VesselItemDto>();

        return zoneDetails.Select(MapZoneDetailToVesselItemDto).Where(dto => dto != null).ToList();
    }

    // Async collection mapping methods for better performance with large collections
    public async Task<List<ImportVesselDto>> MapToDtoListAsync(IEnumerable<ImportVessel> entities)
    {
        return await Task.Run(() => MapToDtoList(entities.ToList()));
    }

    public async Task<List<ImportVesselWithItemsDto>> MapToWithItemsDtoListAsync(IEnumerable<ImportVessel> entities)
    {
        return await Task.Run(() => MapToWithItemsDtoList(entities.ToList()));
    }

    // Map with items included - use Mapperly's nested mapping capabilities
    // Configure proper mapping for the Items collection from ZoneDetail to VesselItemDto
    [MapperIgnoreSource(nameof(ImportVessel.IsDeleted))] // ABP audit property not in DTO
    [MapperIgnoreSource(nameof(ImportVessel.DeleterId))] // ABP audit property not in DTO
    [MapperIgnoreSource(nameof(ImportVessel.DeletionTime))] // ABP audit property not in DTO
    [MapperIgnoreSource(nameof(ImportVessel.ExtraProperties))] // ABP property not in DTO
    public partial ImportVesselWithItemsDto MapToWithItemsDto(ImportVessel entity);

    // Custom method to map entity with separate items list (used by app service)
    public ImportVesselWithItemsDto MapToDtoWithItems(ImportVessel entity, List<VesselItemDto> items)
    {
        var dto = MapToWithItemsDto(entity);
        dto.Items = items;
        return dto;
    }

    // Custom mapping method for ZoneDetail to VesselItemDto with complex business logic
    private VesselItemDto MapZoneDetailToVesselItemDto(ZoneDetail zoneDetail)
    {
        if (zoneDetail == null) return null!;

        return new VesselItemDto
        {
            Id = zoneDetail.Id,
            ConcurrencyStamp = zoneDetail.ConcurrencyStamp,
            DocEntry = zoneDetail.DocEntry,
            DocNum = zoneDetail.DocNum ?? 0,
            TenantName = zoneDetail.Tenant?.Name,
            ItemName = zoneDetail.ItemName,
            ItemQty = zoneDetail.ItemQty,
            UnitQty = zoneDetail.UnitQty,
            Cargo = zoneDetail.Cargo,
            Shipment = zoneDetail.Shipment,
            Remarks = zoneDetail.Remarks,
            NoBl = zoneDetail.NoBl,
            DateBl = zoneDetail.DateBl,
            AjuNo = zoneDetail.AjuNo,
            RegNo = zoneDetail.RegNo,
            RegDate = zoneDetail.RegDate,
            SppbNo = zoneDetail.SppbNo,
            SppbDate = zoneDetail.SppbDate,
            SppdNo = zoneDetail.SppdNo,
            SppdDate = zoneDetail.SppdDate,
            GrossWeight = zoneDetail.GrossWeight,
            UnitWeight = zoneDetail.UnitWeight,
            HeaderId = zoneDetail.HeaderId,
            LetterNo = zoneDetail.LetterNo,
            DocType = zoneDetail.DocType,
            VesselType = "Import", // Set based on context
            ShippingInstructionNo = zoneDetail.ShippingInstructionNo,
            RegType = zoneDetail.RegType,
            Status = zoneDetail.Status,
            ShippingInstructionDate = zoneDetail.ShippingInstructionDate,
            LetterDate = zoneDetail.LetterDate,
            TenantId = zoneDetail.TenantId,
            BcTypeId = zoneDetail.BcTypeId,
            BusinessPartnerId = zoneDetail.BusinessPartnerId,
            AgentId = zoneDetail.AgentId,
            MasterExportClassificationId = zoneDetail.MasterExportClassificationId,
            Item = zoneDetail.Item,
            CreationTime = zoneDetail.CreationTime,
            CreatorId = zoneDetail.CreatorId,
            LastModificationTime = zoneDetail.LastModificationTime,
            LastModifierId = zoneDetail.LastModifierId,
            // Map navigation properties
            Tenant = zoneDetail.Tenant == null ? null : MapTenantToShortDto(zoneDetail.Tenant),
            BcType = zoneDetail.BcType == null ? null : MapBcTypeToShortDto(zoneDetail.BcType),
            MasterAgent = zoneDetail.MasterAgent == null ? null : MapAgentToShortDto(zoneDetail.MasterAgent),
            BusinessPartner = zoneDetail.BusinessPartner == null ? null : MapBusinessPartnerToShortDto(zoneDetail.BusinessPartner)
        };
    }

    // Custom mapping methods for navigation properties to short DTOs
    private TenantShortDto MapTenantToShortDto(MasterTenant tenant)
    {
        return new TenantShortDto
        {
            Id = tenant.Id,
            DocEntry = tenant.DocEntry,
            Name = tenant.Name,
            FullName = tenant.FullName,
            Npwp = tenant.Npwp,
            Address = tenant.Address,
            Nib = tenant.Nib,
            Phone = tenant.Phone,
            NoAndDateNotaris = tenant.NoAndDateNotaris,
            DescNotaris = tenant.DescNotaris
        };
    }

    private BcTypeShortDto MapBcTypeToShortDto(BcType bcType)
    {
        return new BcTypeShortDto
        {
            Id = bcType.Id,
            DocEntry = bcType.DocEntry,
            Type = bcType.Type,
            TransName = bcType.TransName
        };
    }

    private AgentShortDto MapAgentToShortDto(Agent agent)
    {
        return new AgentShortDto
        {
            Id = agent.Id,
            DocEntry = agent.DocEntry,
            Name = agent.Name,
            Type = agent.Type
        };
    }

    private BusinessPartnerShortDto MapBusinessPartnerToShortDto(BusinessPartner businessPartner)
    {
        return new BusinessPartnerShortDto
        {
            Id = businessPartner.Id,
            DocEntry = businessPartner.DocEntry,
            Name = businessPartner.Name,
            Npwp = businessPartner.Npwp,
            Nitku = businessPartner.Nitku
        };
    }

    // Projection mapping for efficient database queries (lightweight DTO)
    // This method uses Mapperly's automatic mapping for better performance and maintainability
    [MapperIgnoreSource(nameof(ImportVessel.Items))] // Collection not needed for projection
    [MapperIgnoreSource(nameof(ImportVessel.IsDeleted))] // ABP audit property not in DTO
    [MapperIgnoreSource(nameof(ImportVessel.DeleterId))] // ABP audit property not in DTO
    [MapperIgnoreSource(nameof(ImportVessel.DeletionTime))] // ABP audit property not in DTO
    [MapperIgnoreSource(nameof(ImportVessel.ExtraProperties))] // ABP property not in DTO
    public partial ImportVesselProjectionDto MapToProjectionDto(ImportVessel entity);

    // Collection projection mapping for lists
    public partial List<ImportVesselProjectionDto> MapToProjectionList(List<ImportVessel> entities);
    public partial IEnumerable<ImportVesselProjectionDto> MapToProjectionEnumerable(IEnumerable<ImportVessel> entities);

    // Complex business logic mapping methods

    // Map entity with custom business rules for different vessel types
    public ImportVesselWithItemsDto MapToWithItemsDtoWithBusinessRules(ImportVessel entity, string vesselType = "Import")
    {
        if (entity == null) return null!;

        var dto = MapToWithItemsDto(entity);

        // Apply business rules based on vessel type
        if (dto.Items != null)
        {
            foreach (var item in dto.Items)
            {
                item.VesselType = vesselType;

                // Apply business logic for status determination
                if (string.IsNullOrEmpty(item.Status))
                {
                    item.Status = DetermineItemStatus(item);
                }
            }
        }

        return dto;
    }

    // Map entity with filtering for specific business scenarios
    public ImportVesselWithItemsDto MapToWithItemsDtoFiltered(ImportVessel entity, Func<ZoneDetail, bool>? itemFilter = null)
    {
        if (entity == null) return null!;

        var dto = MapToDto(entity) as ImportVesselWithItemsDto ?? new ImportVesselWithItemsDto();

        // Copy all properties from base DTO
        CopyBaseProperties(entity, dto);

        // Apply filtering to items if specified
        if (entity.Items != null)
        {
            var filteredItems = itemFilter != null
                ? entity.Items.Where(itemFilter)
                : entity.Items;

            dto.Items = MapZoneDetailsToVesselItemDtos(filteredItems.ToList());
        }

        return dto;
    }

    // Batch mapping with error handling for large datasets
    public List<ImportVesselDto> MapToDtoListWithErrorHandling(IEnumerable<ImportVessel> entities,
        Action<Exception, ImportVessel>? onError = null)
    {
        var results = new List<ImportVesselDto>();

        foreach (var entity in entities)
        {
            try
            {
                var dto = MapToDto(entity);
                if (dto != null)
                {
                    results.Add(dto);
                }
            }
            catch (Exception ex)
            {
                onError?.Invoke(ex, entity);
                // Continue processing other entities
            }
        }

        return results;
    }

    // Helper method to determine item status based on business rules
    private string DetermineItemStatus(VesselItemDto item)
    {
        if (item.SppbDate.HasValue && item.SppdDate.HasValue)
            return "Completed";
        else if (item.SppbDate.HasValue)
            return "In Progress";
        else if (item.RegDate.HasValue)
            return "Registered";
        else
            return "Draft";
    }

    // Helper method to copy base properties
    private void CopyBaseProperties(ImportVessel entity, ImportVesselWithItemsDto dto)
    {
        var baseDto = MapToDto(entity);

        // Copy all properties from base DTO to WithItems DTO
        dto.Id = baseDto.Id;
        dto.ConcurrencyStamp = baseDto.ConcurrencyStamp;
        dto.DocEntry = baseDto.DocEntry;
        dto.DocNum = baseDto.DocNum;
        dto.Bp = baseDto.Bp;
        dto.VesselName = baseDto.VesselName;
        dto.Shipment = baseDto.Shipment;
        dto.ShipmentNo = baseDto.ShipmentNo;
        dto.VesselArrival = baseDto.VesselArrival;
        dto.CreatedBy = baseDto.CreatedBy;
        dto.UpdatedBy = baseDto.UpdatedBy;
        dto.CreatedAt = baseDto.CreatedAt;
        dto.UpdatedAt = baseDto.UpdatedAt;
        dto.PostingDate = baseDto.PostingDate;
        dto.Color = baseDto.Color;
        dto.Flags = baseDto.Flags;
        dto.Remarks = baseDto.Remarks;
        dto.Status = baseDto.Status;
        dto.IsLocked = baseDto.IsLocked;
        dto.IsChange = baseDto.IsChange;
        dto.TransType = baseDto.TransType;
        dto.DocType = baseDto.DocType;
        dto.BcType = baseDto.BcType;
        dto.PortOrigin = baseDto.PortOrigin;
        dto.EmailToPpjk = baseDto.EmailToPpjk;
        dto.MatchKey = baseDto.MatchKey;
        dto.Voyage = baseDto.Voyage;
        dto.Deleted = baseDto.Deleted;
        dto.DocStatus = baseDto.DocStatus;
        dto.GrossWeight = baseDto.GrossWeight;
        dto.VesselFlag = baseDto.VesselFlag;
        dto.VesselDeparture = baseDto.VesselDeparture;
        dto.VesselStatus = baseDto.VesselStatus;
        dto.Jetty = baseDto.Jetty;
        dto.DestinationPort = baseDto.DestinationPort;
        dto.BerthingDate = baseDto.BerthingDate;
        dto.AnchorageDate = baseDto.AnchorageDate;
        dto.Type = baseDto.Type;
        dto.JettyUpdate = baseDto.JettyUpdate;
        dto.ReportDate = baseDto.ReportDate;
        dto.UnloadingDate = baseDto.UnloadingDate;
        dto.FinishUnloadingDate = baseDto.FinishUnloadingDate;
        dto.GrtWeight = baseDto.GrtWeight;
        dto.InvoiceStatus = baseDto.InvoiceStatus;
        dto.AgentId = baseDto.AgentId;
        dto.AgentName = baseDto.AgentName;
        dto.StatusBms = baseDto.StatusBms;
        dto.SurveyorId = baseDto.SurveyorId;
        dto.TradingId = baseDto.TradingId;
        dto.JettyId = baseDto.JettyId;
        dto.VesselId = baseDto.VesselId;
        dto.MasterAgentId = baseDto.MasterAgentId;
        dto.MasterTradingId = baseDto.MasterTradingId;
        dto.MasterSurveyorId = baseDto.MasterSurveyorId;
        dto.AsideDate = baseDto.AsideDate;
        dto.CastOfDate = baseDto.CastOfDate;
        dto.PortOriginId = baseDto.PortOriginId;
        dto.DestinationPortId = baseDto.DestinationPortId;
        dto.CreationTime = baseDto.CreationTime;
        dto.CreatorId = baseDto.CreatorId;
        dto.LastModificationTime = baseDto.LastModificationTime;
        dto.LastModifierId = baseDto.LastModifierId;

        // Copy navigation properties
        dto.MasterJetty = baseDto.MasterJetty;
        dto.Vessel = baseDto.Vessel;
        dto.MasterAgent = baseDto.MasterAgent;
        dto.MasterTrading = baseDto.MasterTrading;
        dto.MasterSurveyor = baseDto.MasterSurveyor;
        dto.MasterPortOrigin = baseDto.MasterPortOrigin;
        dto.MasterDestinationPort = baseDto.MasterDestinationPort;
    }

    // Instance property to access the projection expression for LINQ queries
    // This allows the app service to use the mapper's projection expression
    public static Expression<Func<ImportVessel, ImportVesselProjectionDto>> ProjectionExpression => ProjectToProjectionDto;

    // Projection expressions for EF Core compatibility - these enable efficient database queries
    // by translating to SQL SELECT statements that only fetch required columns
    public static Expression<Func<ImportVessel, ImportVesselProjectionDto>> ProjectToProjectionDto =>
        entity => new ImportVesselProjectionDto
        {
            Id = entity.Id,
            ConcurrencyStamp = entity.ConcurrencyStamp,
            DocEntry = entity.DocEntry,
            DocNum = entity.DocNum,
            PostingDate = entity.PostingDate,
            VesselName = entity.Vessel != null ? entity.Vessel.Name : null,
            Shipment = entity.Shipment,
            VesselArrival = entity.VesselArrival,
            VesselDeparture = entity.VesselDeparture,
            PortOrigin = entity.PortOrigin,
            DestinationPort = entity.DestinationPort,
            Voyage = entity.Voyage,
            GrossWeight = entity.GrossWeight,
            DocStatus = entity.DocStatus,
            Status = entity.Status,
            Remarks = entity.Remarks,
            DocType = entity.DocType,
            TransType = entity.TransType,
            BerthingDate = entity.BerthingDate,
            AnchorageDate = entity.AnchorageDate,
            ReportDate = entity.ReportDate,
            UnloadingDate = entity.UnloadingDate,
            FinishUnloadingDate = entity.FinishUnloadingDate,
            GrtWeight = entity.GrtWeight,
            InvoiceStatus = entity.InvoiceStatus,
            StatusBms = entity.StatusBms,
            JettyId = entity.JettyId,
            VesselId = entity.VesselId,
            MasterAgentId = entity.MasterAgentId,
            MasterTradingId = entity.MasterTradingId,
            MasterSurveyorId = entity.MasterSurveyorId,
            AsideDate = entity.AsideDate,
            CastOfDate = entity.CastOfDate,
            PortOriginId = entity.PortOriginId,
            DestinationPortId = entity.DestinationPortId,
            CreationTime = entity.CreationTime,
            CreatorId = entity.CreatorId,
            LastModificationTime = entity.LastModificationTime,
            LastModifierId = entity.LastModifierId,
            // Navigation properties with efficient projections
            MasterAgent = entity.MasterAgent == null ? null : new AgentProjectionDto
            {
                Id = entity.MasterAgent.Id,
                DocEntry = entity.MasterAgent.DocEntry,
                Name = entity.MasterAgent.Name,
                Status = entity.MasterAgent.Status,
                Type = entity.MasterAgent.Type,
                NpwpNo = entity.MasterAgent.NpwpNo,
                BdmSapcode = entity.MasterAgent.BdmSapcode,
                TaxCode = entity.MasterAgent.TaxCode,
                AddressNpwp = entity.MasterAgent.AddressNpwp,
                Address = entity.MasterAgent.Address,
                SapcodeS4 = entity.MasterAgent.SapcodeS4,
                CreationTime = entity.MasterAgent.CreationTime,
                CreatorId = entity.MasterAgent.CreatorId,
                LastModificationTime = entity.MasterAgent.LastModificationTime,
                LastModifierId = entity.MasterAgent.LastModifierId
            },
            MasterTrading = entity.MasterTrading == null ? null : new TradingProjectionDto
            {
                Id = entity.MasterTrading.Id,
                DocEntry = (int)entity.MasterTrading.DocEntry,
                Name = entity.MasterTrading.Name,
                Address = entity.MasterTrading.Address,
                Npwp = entity.MasterTrading.Npwp,
                IsActive = entity.MasterTrading.IsActive,
                CreationTime = entity.MasterTrading.CreationTime,
                CreatorId = entity.MasterTrading.CreatorId,
                LastModificationTime = entity.MasterTrading.LastModificationTime,
                LastModifierId = entity.MasterTrading.LastModifierId
            },
            MasterSurveyor = entity.MasterSurveyor == null ? null : new SurveyorProjectionDto
            {
                Id = entity.MasterSurveyor.Id,
                DocEntry = (int)entity.MasterSurveyor.DocEntry,
                Name = entity.MasterSurveyor.Name,
                Address = entity.MasterSurveyor.Address,
                Npwp = entity.MasterSurveyor.Npwp,
                IsActive = entity.MasterSurveyor.IsActive,
                CreationTime = entity.MasterSurveyor.CreationTime,
                CreatorId = entity.MasterSurveyor.CreatorId,
                LastModificationTime = entity.MasterSurveyor.LastModificationTime,
                LastModifierId = entity.MasterSurveyor.LastModifierId
            },
            MasterJetty = entity.MasterJetty == null ? null : new JettyProjectionDto
            {
                Id = entity.MasterJetty.Id,
                DocEntry = entity.MasterJetty.DocEntry,
                Name = entity.MasterJetty.Name,
                Alias = entity.MasterJetty.Alias,
                Max = entity.MasterJetty.Max,
                Port = entity.MasterJetty.Port,
                IsCustomArea = entity.MasterJetty.IsCustomArea,
                Deleted = entity.MasterJetty.Deleted,
                CreationTime = entity.MasterJetty.CreationTime,
                CreatorId = entity.MasterJetty.CreatorId,
                LastModificationTime = entity.MasterJetty.LastModificationTime,
                LastModifierId = entity.MasterJetty.LastModifierId
            },
            Vessel = entity.Vessel == null ? null : new CargoProjectionDto
            {
                Id = entity.Vessel.Id,
                DocEntry = entity.Vessel.DocEntry,
                Name = entity.Vessel.Name,
                Alias = entity.Vessel.Alias,
                Flag = entity.Vessel.Flag,
                GrossWeight = entity.Vessel.GrossWeight,
                Type = entity.Vessel.Type,
                LoaQty = entity.Vessel.LoaQty,
                Status = entity.Vessel.Status,
                CreationTime = entity.Vessel.CreationTime,
                CreatorId = entity.Vessel.CreatorId,
                LastModificationTime = entity.Vessel.LastModificationTime,
                LastModifierId = entity.Vessel.LastModifierId
            },
            MasterPortOrigin = entity.MasterPortOrigin == null ? null : new PortOfOriginProjectionDto
            {
                Id = entity.MasterPortOrigin.Id,
                DocEntry = entity.MasterPortOrigin.DocEntry,
                Name = entity.MasterPortOrigin.Name,
                DocType = entity.MasterPortOrigin.DocType,
                CreationTime = entity.MasterPortOrigin.CreationTime,
                CreatorId = entity.MasterPortOrigin.CreatorId,
                LastModificationTime = entity.MasterPortOrigin.LastModificationTime,
                LastModifierId = entity.MasterPortOrigin.LastModifierId
            },
            MasterDestinationPort = entity.MasterDestinationPort == null ? null : new DestinationPortProjectionDto
            {
                Id = entity.MasterDestinationPort.Id,
                DocEntry = entity.MasterDestinationPort.DocEntry,
                Name = entity.MasterDestinationPort.Name,
                DocType = entity.MasterDestinationPort.DocType,
                CreationTime = entity.MasterDestinationPort.CreationTime,
                CreatorId = entity.MasterDestinationPort.CreatorId,
                LastModificationTime = entity.MasterDestinationPort.LastModificationTime,
                LastModifierId = entity.MasterDestinationPort.LastModifierId
            }
        };

    // Legacy projection expression for backward compatibility (maps to ImportVesselDto)
    public static Expression<Func<ImportVessel, ImportVesselDto>> ProjectToDto =>
        entity => new ImportVesselDto
        {
            Id = entity.Id,
            ConcurrencyStamp = entity.ConcurrencyStamp,
            DocEntry = entity.DocEntry,
            DocNum = entity.DocNum,
            Bp = entity.Bp,
            VesselName = entity.Vessel != null ? entity.Vessel.Name : null,
            Shipment = entity.Shipment,
            ShipmentNo = entity.ShipmentNo,
            VesselArrival = entity.VesselArrival,
            CreatedBy = entity.CreatedBy,
            UpdatedBy = entity.UpdatedBy,
            CreatedAt = entity.CreatedAt,
            UpdatedAt = entity.UpdatedAt,
            PostingDate = entity.PostingDate,
            Color = entity.Color,
            Flags = entity.Flags,
            Remarks = entity.Remarks,
            Status = entity.Status,
            IsLocked = entity.IsLocked,
            IsChange = entity.IsChange,
            TransType = entity.TransType,
            DocType = entity.DocType,
            BcType = entity.BcType,
            PortOrigin = entity.PortOrigin,
            EmailToPpjk = entity.EmailToPpjk,
            MatchKey = entity.MatchKey,
            Voyage = entity.Voyage,
            Deleted = entity.Deleted,
            DocStatus = entity.DocStatus,
            GrossWeight = entity.GrossWeight,
            VesselFlag = entity.VesselFlag,
            VesselDeparture = entity.VesselDeparture,
            VesselStatus = entity.VesselStatus,
            Jetty = entity.Jetty,
            DestinationPort = entity.DestinationPort,
            BerthingDate = entity.BerthingDate,
            AnchorageDate = entity.AnchorageDate,
            Type = entity.Type,
            JettyUpdate = entity.JettyUpdate,
            ReportDate = entity.ReportDate,
            UnloadingDate = entity.UnloadingDate,
            FinishUnloadingDate = entity.FinishUnloadingDate,
            GrtWeight = entity.GrtWeight,
            InvoiceStatus = entity.InvoiceStatus,
            AgentId = entity.AgentId,
            AgentName = entity.AgentName,
            StatusBms = entity.StatusBms,
            SurveyorId = entity.SurveyorId,
            TradingId = entity.TradingId,
            JettyId = entity.JettyId,
            VesselId = entity.VesselId,
            MasterAgentId = entity.MasterAgentId,
            MasterTradingId = entity.MasterTradingId,
            MasterSurveyorId = entity.MasterSurveyorId,
            AsideDate = entity.AsideDate,
            CastOfDate = entity.CastOfDate,
            PortOriginId = entity.PortOriginId,
            DestinationPortId = entity.DestinationPortId,
            CreationTime = entity.CreationTime,
            CreatorId = entity.CreatorId,
            LastModificationTime = entity.LastModificationTime,
            LastModifierId = entity.LastModifierId,
            // Navigation properties
            MasterJetty = entity.MasterJetty == null ? null : new JettyDto
            {
                Id = entity.MasterJetty.Id,
                Name = entity.MasterJetty.Name,
                Alias = entity.MasterJetty.Alias,
                Max = entity.MasterJetty.Max,
                Port = entity.MasterJetty.Port,
                IsCustomArea = entity.MasterJetty.IsCustomArea,
                Deleted = entity.MasterJetty.Deleted,
                DocEntry = entity.MasterJetty.DocEntry,
                CreationTime = entity.MasterJetty.CreationTime,
                CreatorId = entity.MasterJetty.CreatorId,
                LastModificationTime = entity.MasterJetty.LastModificationTime,
                LastModifierId = entity.MasterJetty.LastModifierId,
            },
            Vessel = entity.Vessel == null ? null : new CargoDto
            {
                Id = entity.Vessel.Id,
                Name = entity.Vessel.Name,
                Alias = entity.Vessel.Alias,
                Flag = entity.Vessel.Flag,
                GrossWeight = entity.Vessel.GrossWeight,
                Type = entity.Vessel.Type,
                LoaQty = entity.Vessel.LoaQty,
                Status = entity.Vessel.Status,
                DocEntry = entity.Vessel.DocEntry,
                CreationTime = entity.Vessel.CreationTime,
                CreatorId = entity.Vessel.CreatorId,
                LastModificationTime = entity.Vessel.LastModificationTime,
                LastModifierId = entity.Vessel.LastModifierId,
            },
            MasterAgent = entity.MasterAgent == null ? null : new AgentDto
            {
                Id = entity.MasterAgent.Id,
                Name = entity.MasterAgent.Name,
                Status = entity.MasterAgent.Status,
                Type = entity.MasterAgent.Type,
                NpwpNo = entity.MasterAgent.NpwpNo,
                BdmSapcode = entity.MasterAgent.BdmSapcode,
                TaxCode = entity.MasterAgent.TaxCode,
                AddressNpwp = entity.MasterAgent.AddressNpwp,
                Address = entity.MasterAgent.Address,
                SapcodeS4 = entity.MasterAgent.SapcodeS4,
                CreationTime = entity.MasterAgent.CreationTime,
                CreatorId = entity.MasterAgent.CreatorId,
                LastModificationTime = entity.MasterAgent.LastModificationTime,
                LastModifierId = entity.MasterAgent.LastModifierId,
            },
            MasterTrading = entity.MasterTrading == null ? null : new TradingDto
            {
                Id = entity.MasterTrading.Id,
                Name = entity.MasterTrading.Name,
                Address = entity.MasterTrading.Address,
                Npwp = entity.MasterTrading.Npwp,
                IsActive = entity.MasterTrading.IsActive,
                CreationTime = entity.MasterTrading.CreationTime,
                CreatorId = entity.MasterTrading.CreatorId,
                LastModificationTime = entity.MasterTrading.LastModificationTime,
                LastModifierId = entity.MasterTrading.LastModifierId,
            },
            MasterSurveyor = entity.MasterSurveyor == null ? null : new SurveyorDto
            {
                Id = entity.MasterSurveyor.Id,
                Name = entity.MasterSurveyor.Name,
                Address = entity.MasterSurveyor.Address,
                Npwp = entity.MasterSurveyor.Npwp,
                IsActive = entity.MasterSurveyor.IsActive,
                CreationTime = entity.MasterSurveyor.CreationTime,
                CreatorId = entity.MasterSurveyor.CreatorId,
                LastModificationTime = entity.MasterSurveyor.LastModificationTime,
                LastModifierId = entity.MasterSurveyor.LastModifierId,
            },
            MasterPortOrigin = entity.MasterPortOrigin == null ? null : new PortOfLoadingDto
            {
                Id = entity.MasterPortOrigin.Id,
                Name = entity.MasterPortOrigin.Name,
                DocType = entity.MasterPortOrigin.DocType,
                CreationTime = entity.MasterPortOrigin.CreationTime,
                CreatorId = entity.MasterPortOrigin.CreatorId,
                LastModificationTime = entity.MasterPortOrigin.LastModificationTime,
                LastModifierId = entity.MasterPortOrigin.LastModifierId,
            },
            MasterDestinationPort = entity.MasterDestinationPort == null ? null : new DestinationPortDto
            {
                Id = entity.MasterDestinationPort.Id,
                Name = entity.MasterDestinationPort.Name,
                DocType = entity.MasterDestinationPort.DocType,
                CreationTime = entity.MasterDestinationPort.CreationTime,
                CreatorId = entity.MasterDestinationPort.CreatorId,
                LastModificationTime = entity.MasterDestinationPort.LastModificationTime,
                LastModifierId = entity.MasterDestinationPort.LastModifierId,
            }
        };

    // Custom mapping methods for complex scenarios
    public ImportVessel CreateEntityWithId(CreateUpdateImportVesselDto dto, Guid id)
    {
        // Create entity using reflection since constructor is protected
        var entity = (ImportVessel)Activator.CreateInstance(typeof(ImportVessel), true)!;

        // Set the ID using reflection
        var idProperty = typeof(ImportVessel).GetProperty("Id");
        idProperty?.SetValue(entity, id);

        // Map properties using automatic mapping
        MapToEntity(dto, entity);

        return entity;
    }
}