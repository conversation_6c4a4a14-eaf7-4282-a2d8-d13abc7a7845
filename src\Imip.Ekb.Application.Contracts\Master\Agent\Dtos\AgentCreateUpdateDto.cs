using System;
using System.ComponentModel.DataAnnotations;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Entities;

namespace Imip.Ekb.Master.Agents.Dtos;

public class AgentCreateUpdateDto : EntityDto<Guid>, IHasConcurrencyStamp
{
    public string ConcurrencyStamp { get; set; }
    [Required]
    [StringLength(255)]
    public string Name { get; set; } = string.Empty;

    [Required]
    [StringLength(10)]
    public string Status { get; set; } = string.Empty;

    [StringLength(255)]
    public string? Type { get; set; }

    public int? CreatedBy { get; set; }
    public int? UpdatedBy { get; set; }

    [StringLength(100)]
    public string? NpwpNo { get; set; }

    [StringLength(255)]
    public string? BdmSapcode { get; set; }

    [StringLength(255)]
    public string? TaxCode { get; set; }

    public string? AddressNpwp { get; set; }
    public string? Address { get; set; }

    [StringLength(255)]
    public string? SapcodeS4 { get; set; }
}