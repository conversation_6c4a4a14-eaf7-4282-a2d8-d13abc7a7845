using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Imip.Ekb.BoundedZone.ImportVessels;
using Imip.Ekb.BoundedZone.ImportVessels.Dtos;
using Imip.Ekb.BoundedZone.ImportVessel;
using Imip.Ekb.Mapping.Mappers;
using Shouldly;
using Volo.Abp.Domain.Repositories;
using Xunit;

namespace Imip.Ekb.Mapping
{
    public class ImportVesselMapperProjectionTests : EkbApplicationTestBase<EkbApplicationTestModule>
    {
        private readonly ImportVesselMapper _mapper;
        private readonly IRepository<ImportVessel, Guid> _repository;

        public ImportVesselMapperProjectionTests()
        {
            _mapper = GetRequiredService<ImportVesselMapper>();
            _repository = GetRequiredService<IRepository<ImportVessel, Guid>>();
        }

        [Fact]
        public void ProjectionExpression_ShouldBeEFCoreCompatible()
        {
            // Arrange & Act - Get the projection expression
            var projectionExpression = ImportVesselMapper.ProjectToProjectionDto;
            
            // Assert - The expression should be compilable and not null
            projectionExpression.ShouldNotBeNull();
            projectionExpression.Compile().ShouldNotBeNull();
            
            // Verify it's a proper LINQ expression that EF Core can translate
            projectionExpression.Body.ShouldNotBeNull();
            projectionExpression.Parameters.Count.ShouldBe(1);
        }

        [Fact]
        public void LegacyProjectionExpression_ShouldBeEFCoreCompatible()
        {
            // Arrange & Act - Get the legacy projection expression
            var projectionExpression = ImportVesselMapper.ProjectToDto;
            
            // Assert - The expression should be compilable and not null
            projectionExpression.ShouldNotBeNull();
            projectionExpression.Compile().ShouldNotBeNull();
            
            // Verify it's a proper LINQ expression that EF Core can translate
            projectionExpression.Body.ShouldNotBeNull();
            projectionExpression.Parameters.Count.ShouldBe(1);
        }

        [Fact]
        public void ProjectionExpression_ShouldHaveCorrectReturnType()
        {
            // Arrange & Act - Get the projection expression
            var projectionExpression = ImportVesselMapper.ProjectToProjectionDto;
            
            // Assert - Verify the expression has the correct types
            projectionExpression.Parameters[0].Type.ShouldBe(typeof(ImportVessel));
            projectionExpression.ReturnType.ShouldBe(typeof(ImportVesselProjectionDto));
        }

        [Fact]
        public void LegacyProjectionExpression_ShouldHaveCorrectReturnType()
        {
            // Arrange & Act - Get the legacy projection expression
            var projectionExpression = ImportVesselMapper.ProjectToDto;
            
            // Assert - Verify the expression has the correct types
            projectionExpression.Parameters[0].Type.ShouldBe(typeof(ImportVessel));
            projectionExpression.ReturnType.ShouldBe(typeof(ImportVesselDto));
        }

        [Fact]
        public void ProjectionExpression_ShouldBeStatic()
        {
            // Arrange & Act - Verify we can access the projection expression without an instance
            var projectionExpression = ImportVesselMapper.ProjectToProjectionDto;
            var legacyProjectionExpression = ImportVesselMapper.ProjectToDto;
            
            // Assert - Both expressions should be accessible as static members
            projectionExpression.ShouldNotBeNull();
            legacyProjectionExpression.ShouldNotBeNull();
        }

        [Fact]
        public void MapToProjectionDto_ShouldMapCorrectly()
        {
            // Arrange - Create a test entity with navigation properties
            var entity = CreateTestImportVessel();
            
            // Act - Map using the Mapperly-generated method
            var dto = _mapper.MapToProjectionDto(entity);
            
            // Assert - Verify basic properties are mapped
            dto.ShouldNotBeNull();
            dto.Id.ShouldBe(entity.Id);
            dto.DocEntry.ShouldBe(entity.DocEntry);
            dto.DocNum.ShouldBe(entity.DocNum);
            dto.VesselName.ShouldBe(entity.Vessel?.Name);
            dto.Shipment.ShouldBe(entity.Shipment);
            dto.Status.ShouldBe(entity.Status);
            dto.DocStatus.ShouldBe(entity.DocStatus);
        }

        [Fact]
        public void MapToProjectionDto_WithNullNavigationProperties_ShouldHandleGracefully()
        {
            // Arrange - Create entity with null navigation properties using the mapper's CreateEntityWithId method
            var testId = Guid.NewGuid();
            var createDto = new CreateUpdateImportVesselDto
            {
                DocNum = 67890,
                Shipment = "TEST-SHIPMENT",
                Status = "Active",
                DocStatus = "Open"
            };
            
            var entity = _mapper.CreateEntityWithId(createDto, testId);
            // Navigation properties are null by default
            
            // Act - Map using the Mapperly-generated method
            var dto = _mapper.MapToProjectionDto(entity);
            
            // Assert - Verify null navigation properties are handled correctly
            dto.ShouldNotBeNull();
            dto.Id.ShouldBe(testId);
            dto.VesselName.ShouldBeNull();
            dto.MasterAgent.ShouldBeNull();
            dto.MasterJetty.ShouldBeNull();
        }

        [Fact]
        public void ProjectionExpression_StaticProperty_ShouldMatchProjectToProjectionDto()
        {
            // Arrange & Act - Get both static projection expressions
            var staticExpression = ImportVesselMapper.ProjectToProjectionDto;
            var projectionExpression = ImportVesselMapper.ProjectionExpression;
            
            // Assert - Both should reference the same expression
            staticExpression.ShouldNotBeNull();
            projectionExpression.ShouldNotBeNull();
            projectionExpression.ShouldBe(staticExpression);
        }

        private ImportVessel CreateTestImportVessel()
        {
            var testId = Guid.NewGuid();
            var createDto = new CreateUpdateImportVesselDto
            {
                DocNum = 67890,
                Shipment = "TEST-SHIPMENT",
                Status = "Active",
                DocStatus = "Open",
                PostingDate = DateOnly.FromDateTime(DateTime.Now),
                VesselArrival = DateTime.Now.AddDays(-1),
                VesselDeparture = DateTime.Now.AddDays(1),
                Voyage = "V001",
                GrossWeight = 1000.50m,
                Remarks = "Test remarks",
                DocType = "Import",
                TransType = "Import"
            };
            
            var entity = _mapper.CreateEntityWithId(createDto, testId);
            
            // Set up test navigation properties
            entity.Vessel = new Master.Cargos.Cargo
            {
                Name = "Test Vessel",
                DocEntry = 1001,
                Status = "Active",
                GrossWeight = 5000.75m
            };
            
            return entity;
        }
    }
}