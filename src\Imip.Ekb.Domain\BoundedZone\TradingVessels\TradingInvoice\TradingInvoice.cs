﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Volo.Abp.Domain.Entities.Auditing;

namespace Imip.Ekb.BoundedZone.TradingVessels.TradingInvoice;

[Table("R_Inv")]
public class TradingInvoice : FullAuditedAggregateRoot<Guid>
{
    [Key]
    public int DocEntry { get; set; }

    public long DocNum { get; set; }

    [Column("P_InvNo")]
    [StringLength(255)]
    public string? PInvNo { get; set; }

    [Column("P_InvDate")]
    public DateOnly? PInvDate { get; set; }

    [StringLength(255)]
    public string? InvNo { get; set; }

    public DateOnly? InvDate { get; set; }

    [StringLength(255)]
    public string? SuratJalan { get; set; }

    [StringLength(255)]
    public string? PackingList { get; set; }

    [StringLength(255)]
    public string? Faktur { get; set; }

    public long CreatedBy { get; set; }

    public long UpdatedBy { get; set; }

    [Column("created_at", TypeName = "datetime")]
    public DateTime? CreatedAt { get; set; }

    [Column("updated_at", TypeName = "datetime")]
    public DateTime? UpdatedAt { get; set; }

    [Column("deleted")]
    [StringLength(1)]
    public string Deleted { get; set; } = null!;

    [Column("status")]
    [StringLength(255)]
    public string? Status { get; set; }

    public Guid? ZoneDetailId { get; set; }

    // Navigation property
    public virtual ZoneDetail? ZoneDetail { get; set; }



}
