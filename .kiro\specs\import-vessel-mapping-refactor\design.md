# Design Document

## Overview

This design outlines the refactoring of ImportVessel mapping and service layer to eliminate code duplication, improve maintainability, and leverage Mapperly's capabilities more effectively. The current implementation suffers from excessive manual mapping, redundant property assignments, and poor separation of concerns between the mapper and service layers.

## Architecture

### Current Problems Identified

1. **Excessive Manual Property Mapping**: The current mapper uses 50+ explicit `[MapProperty]` attributes when Mapperly can handle most mappings automatically
2. **Code Duplication**: The `GetWithItemsAsync` method manually assigns 80+ properties, duplicating logic that should be in the mapper
3. **Manual LINQ Projections**: The `FilterListAsync` method contains a large manual projection expression that could be generated by Mapperly
4. **Inconsistent Mapping Patterns**: Some mappings use Mapperly while others use manual assignment
5. **Poor Performance**: Manual property assignment is slower than Mapperly's generated code

### Proposed Architecture

```mermaid
graph TB
    A[ImportVesselAppService] --> B[ImportVesselMapper]
    B --> C[Mapperly Generated Code]
    B --> D[Custom Mapping Methods]
    
    E[Entity] --> B
    F[DTOs] --> B
    
    B --> G[ImportVesselDto]
    B --> H[ImportVesselWithItemsDto]
    B --> I[ImportVesselProjectionDto]
    
    C --> J[Compile-time Optimized Mappings]
    D --> K[Complex Business Logic Mappings]
```

## Components and Interfaces

### 1. Enhanced ImportVesselMapper

The mapper will be restructured to leverage Mapperly's automatic mapping capabilities:

```csharp
[Mapper]
public partial class ImportVesselMapper : IMapperlyMapper
{
    // Automatic mappings - remove explicit MapProperty attributes
    public partial ImportVesselDto MapToDto(ImportVessel entity);
    public partial ImportVesselWithItemsDto MapToWithItemsDto(ImportVessel entity);
    public partial ImportVesselProjectionDto MapToProjectionDto(ImportVessel entity);
    
    // Collection mappings
    public partial List<ImportVesselDto> MapToDtoList(List<ImportVessel> entities);
    public partial List<ImportVesselProjectionDto> MapToProjectionList(List<ImportVessel> entities);
    
    // Projection expressions for EF Core
    public Expression<Func<ImportVessel, ImportVesselProjectionDto>> ProjectToDto => 
        entity => MapToProjectionDto(entity);
    
    // Custom mappings for complex scenarios
    public partial ImportVessel MapToEntity(CreateUpdateImportVesselDto dto);
    public partial void MapToEntity(CreateUpdateImportVesselDto dto, ImportVessel entity);
}
```

### 2. Mapping Configuration Strategy

#### Automatic Property Mapping
- Remove all explicit `[MapProperty]` attributes for properties with matching names
- Let Mapperly handle standard property-to-property mappings automatically
- Use `[MapperIgnore]` only for properties that should never be mapped

#### Navigation Property Handling
- Configure automatic mapping for navigation properties
- Use custom mapping methods for complex nested object scenarios
- Leverage Mapperly's nested mapping capabilities

#### Performance Optimization
- Use projection expressions for list queries to enable EF Core query translation
- Generate compile-time optimized mapping code
- Eliminate runtime reflection-based mapping

### 3. Service Layer Simplification

The `ImportVesselAppService` will be simplified to focus on business logic:

```csharp
public class ImportVesselAppService
{
    private readonly ImportVesselMapper _mapper;
    
    public async Task<ImportVesselWithItemsDto> GetWithItemsAsync(Guid id)
    {
        var entity = await _repository.GetQueryableWithItemsSplitAsync(id);
        if (entity == null)
            throw new UserFriendlyException("ImportVessel not found");
            
        return _mapper.MapToWithItemsDto(entity);
    }
    
    public async Task<PagedResultDto<ImportVesselProjectionDto>> FilterListAsync(QueryParametersDto parameters)
    {
        var query = await _repository.GetQueryableWithIncludesAsync();
        query = ApplyDynamicQuery(query, parameters);
        
        var projectedQuery = query.Select(_mapper.ProjectToDto);
        
        var totalCount = await AsyncExecuter.CountAsync(projectedQuery);
        var items = await AsyncExecuter.ToListAsync(
            projectedQuery.Skip(parameters.SkipCount).Take(parameters.MaxResultCount)
        );
        
        return new PagedResultDto<ImportVesselProjectionDto>
        {
            TotalCount = totalCount,
            Items = items
        };
    }
}
```

## Data Models

### Mapping Scenarios

1. **Entity to DTO**: `ImportVessel` → `ImportVesselDto`
2. **Entity to WithItems DTO**: `ImportVessel` → `ImportVesselWithItemsDto`
3. **Entity to Projection DTO**: `ImportVessel` → `ImportVesselProjectionDto`
4. **DTO to Entity (Create)**: `CreateUpdateImportVesselDto` → `ImportVessel`
5. **DTO to Entity (Update)**: `CreateUpdateImportVesselDto` → existing `ImportVessel`

### Navigation Property Mapping Strategy

```csharp
// Automatic nested mapping configuration
[MapProperty(nameof(ImportVessel.Vessel.Name), nameof(ImportVesselDto.VesselName))]
[MapProperty(nameof(ImportVessel.MasterJetty), nameof(ImportVesselDto.MasterJetty))]
public partial ImportVesselDto MapToDto(ImportVessel entity);

// Custom mapping for complex scenarios
private JettyDto MapJetty(Jetty jetty) => jetty == null ? null : new JettyDto
{
    Id = jetty.Id,
    Name = jetty.Name,
    // ... other properties
};
```

## Error Handling

### Mapping Error Scenarios

1. **Null Reference Handling**: Mapperly will generate null-safe mapping code
2. **Type Conversion Errors**: Use custom converters for complex type mappings
3. **Missing Property Mapping**: Configure explicit mappings for properties that don't match by name
4. **Circular Reference Prevention**: Use appropriate mapping depth limits

### Error Recovery Strategies

```csharp
// Custom error handling for complex mappings
public ImportVesselWithItemsDto MapToWithItemsDto(ImportVessel entity)
{
    try
    {
        return MapToWithItemsDtoCore(entity);
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "Error mapping ImportVessel {Id} to WithItemsDto", entity.Id);
        throw new BusinessException("Mapping error occurred");
    }
}
```

## Testing Strategy

### Unit Testing Approach

1. **Mapping Accuracy Tests**: Verify all properties are mapped correctly
2. **Performance Tests**: Compare performance before and after refactoring
3. **Null Safety Tests**: Ensure mappings handle null values gracefully
4. **Complex Scenario Tests**: Test nested object and collection mappings

### Test Structure

```csharp
public class ImportVesselMapperTests
{
    [Fact]
    public void MapToDto_ShouldMapAllProperties()
    {
        // Arrange
        var entity = CreateTestImportVessel();
        
        // Act
        var dto = _mapper.MapToDto(entity);
        
        // Assert
        Assert.Equal(entity.Id, dto.Id);
        Assert.Equal(entity.VesselName, dto.VesselName);
        // ... verify all properties
    }
    
    [Fact]
    public void MapToDto_WithNullNavigation_ShouldHandleGracefully()
    {
        // Test null navigation property handling
    }
    
    [Fact]
    public void MapToProjectionDto_ShouldBeEFCoreCompatible()
    {
        // Test that projection expressions work with EF Core
    }
}
```

### Performance Testing

1. **Benchmark existing vs new implementation**
2. **Memory allocation comparison**
3. **Query performance with projections**
4. **Mapping throughput tests**

## Implementation Phases

### Phase 1: Mapper Refactoring
- Remove unnecessary `[MapProperty]` attributes
- Configure automatic mappings
- Add projection expressions
- Implement custom mapping methods for complex scenarios

### Phase 2: Service Layer Cleanup
- Replace manual property assignments with mapper calls
- Simplify `GetWithItemsAsync` method
- Update `FilterListAsync` to use projection expressions
- Remove duplicate mapping logic

### Phase 3: Testing and Validation
- Create comprehensive test suite
- Performance benchmarking
- Integration testing
- Backward compatibility verification

### Phase 4: Documentation and Cleanup
- Update code documentation
- Remove unused code
- Clean up imports and dependencies
- Create usage examples