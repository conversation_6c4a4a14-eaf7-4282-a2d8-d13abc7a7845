﻿using Imip.Ekb.Master.ItemClassifications;
using Imip.Ekb.Master.ItemClassifications.Dtos;
using Riok.Mapperly.Abstractions;
using System;
using System.Collections.Generic;

namespace Imip.Ekb.Mapping.Mappers;

[Mapper]
public partial class ItemClassificationMapper : IMapperlyMapper
{// Entity to DTO mapping
    public partial ItemClassificationDto MapToDto(ItemClassification entity);

    // DTO to Entity mapping for updates (maps to existing entity)
    [MapperIgnoreTarget(nameof(ItemClassification.Id))] // Don't change existing Id
    [MapperIgnoreTarget(nameof(ItemClassification.CreatedBy))] // Don't overwrite audit fields
    [MapperIgnoreTarget(nameof(ItemClassification.CreatedAt))]
    public partial void MapToEntity(ItemClassificationCreateUpdateDto dto, ItemClassification entity);

    // Custom mapping methods for complex scenarios
    public ItemClassification CreateEntityWithId(ItemClassificationCreateUpdateDto dto, Guid id)
    {
        // Create entity using reflection since constructor is protected
        var entity = (ItemClassification)Activator.CreateInstance(typeof(ItemClassification), true)!;

        // Map properties manually
        MapToEntity(dto, entity);

        return entity;
    }

    // Map list of entities to DTOs
    public partial List<ItemClassificationDto> MapToDtoList(List<ItemClassification> entities);

    // Map IEnumerable for LINQ scenarios
    public partial IEnumerable<ItemClassificationDto> MapToDtoEnumerable(IEnumerable<ItemClassification> entities);

    // Projection expression for efficient querying
    public static readonly System.Linq.Expressions.Expression<Func<ItemClassification, ItemClassificationDto>> ProjectionExpression = entity => new ItemClassificationDto
    {
        Id = entity.Id,
        DocEntry = entity.DocEntry,
        ConcurrencyStamp = entity.ConcurrencyStamp,
        Name = entity.Name,
        ReportType = entity.ReportType,
        Deleted = entity.Deleted,
        CreatedBy = entity.CreatedBy,
        UpdatedBy = entity.UpdatedBy,
        CreatedAt = entity.CreatedAt,
        UpdatedAt = entity.UpdatedAt,
        Category = entity.Category
    };
}