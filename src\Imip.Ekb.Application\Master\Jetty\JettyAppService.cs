﻿using System;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using Imip.Ekb.Mapping.Mappers;
using Imip.Ekb.Master.Jetties.Dtos;
using Imip.Ekb.Models;
using Imip.Ekb.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.Ekb.Master.Jetties;

[Authorize]
public class JettyAppService :
    CrudAppService<Jetty, JettyDto, Guid, PagedAndSortedResultRequestDto, JettyCreateUpdateDto, JettyCreateUpdateDto>,
    IJettyAppService
{
    private readonly IJettyRepository _jettyRepository;
    private readonly JettyMapper _mapper;
    private readonly ILogger<JettyAppService> _logger;

    public JettyAppService(
        IJettyRepository jettyRepository,
        JettyMapper mapper,
        ILogger<JettyAppService> logger)
        : base(jettyRepository)
    {
        _jettyRepository = jettyRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public override async Task<JettyDto> CreateAsync(JettyCreateUpdateDto input)
    {
        await CheckCreatePolicyAsync();
        var entity = _mapper.CreateEntityWithId(input, Guid.NewGuid());
        entity.CreatedAt = Clock.Now;
        await _jettyRepository.InsertAsync(entity, autoSave: true);
        return _mapper.MapToDto(entity);
    }

    public override async Task<JettyDto> UpdateAsync(Guid id, JettyCreateUpdateDto input)
    {
        await CheckUpdatePolicyAsync();
        var entity = await _jettyRepository.GetAsync(id);
        var originalCreatedBy = entity.CreatedBy;
        var originalCreatedAt = entity.CreatedAt;
        _mapper.MapToEntity(input, entity);
        entity.CreatedBy = originalCreatedBy;
        entity.CreatedAt = originalCreatedAt;
        entity.UpdatedAt = Clock.Now;
        await _jettyRepository.UpdateAsync(entity, autoSave: true);
        return _mapper.MapToDto(entity);
    }

    public override async Task DeleteAsync(Guid id)
    {
        await CheckDeletePolicyAsync();
        var entity = await _jettyRepository.GetAsync(id);
        entity.Deleted = "Y";
        entity.UpdatedAt = Clock.Now;
        await _jettyRepository.UpdateAsync(entity, autoSave: true);
    }

    public override async Task<JettyDto> GetAsync(Guid id)
    {
        await CheckGetPolicyAsync();
        var entity = await _jettyRepository.GetAsync(id);
        return _mapper.MapToDto(entity);
    }

    public override async Task<PagedResultDto<JettyDto>> GetListAsync(PagedAndSortedResultRequestDto input)
    {
        await CheckGetListPolicyAsync();
        var queryable = await _jettyRepository.GetQueryableAsync();
        var sortedQueryable = queryable.OrderBy(input.Sorting.IsNullOrWhiteSpace() ? nameof(Jetty.DocEntry) : input.Sorting);
        var totalCount = await AsyncExecuter.CountAsync(queryable);
        var entities = await AsyncExecuter.ToListAsync(
            sortedQueryable.PageBy(input.SkipCount, input.MaxResultCount)
        );
        var dtos = entities.Select(_mapper.MapToDto).ToList();
        return new PagedResultDto<JettyDto>(totalCount, dtos);
    }

    public virtual async Task<PagedResultDto<JettyDto>> FilterListAsync(QueryParametersDto parameters)
    {
        var query = await _jettyRepository.GetQueryableAsync();
        query = ApplyDynamicQuery(query, parameters);
        var totalCount = await AsyncExecuter.CountAsync(query);
        var items = await AsyncExecuter.ToListAsync(
            query.Select(JettyMapper.ProjectionExpression)
                .Skip(parameters.SkipCount).Take(parameters.MaxResultCount)
        );
        return new PagedResultDto<JettyDto>
        {
            TotalCount = totalCount,
            Items = items
        };
    }

    private IQueryable<Jetty> ApplyDynamicQuery(IQueryable<Jetty> query, QueryParametersDto parameters)
    {
        if (parameters.FilterGroup?.Conditions != null && parameters.FilterGroup.Conditions.Count > 0)
        {
            query = DynamicQueryBuilder<Jetty>.ApplyFilters(query, parameters.FilterGroup);
        }
        if (parameters.Sort?.Count > 0)
        {
            query = DynamicQueryBuilder<Jetty>.ApplyMultipleSorting(query, parameters.Sort);
        }
        else if (!string.IsNullOrEmpty(parameters.Sorting))
        {
            query = query.OrderBy(parameters.Sorting);
        }
        else
        {
            query = query.OrderByDescending(x => x.CreationTime);
        }
        return query;
    }
}