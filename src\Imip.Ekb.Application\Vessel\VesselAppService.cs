using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Imip.Ekb.Attachments;
using Imip.Ekb.BoundedZone;
using Imip.Ekb.BoundedZone.Dtos;
using Imip.Ekb.BoundedZone.ExportVessels;
using Imip.Ekb.BoundedZone.ImportVessels;
using Imip.Ekb.BoundedZone.LocalVessels;
using Imip.Ekb.Mapping.Mappers;
using Imip.Ekb.Master.Cargos;
using Imip.Ekb.Master.Jetties;
using Imip.Ekb.Master.Tenants;
using Imip.Ekb.Models;
using Imip.Ekb.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;


namespace Imip.Ekb.Vessel;

[Authorize]
public class VesselAppService : ApplicationService, IVesselAppService
{
    private readonly IImportVesselRepository _importVesselRepository;
    private readonly IExportVesselRepository _exportVesselRepository;
    private readonly ILocalVesselRepository _localVesselRepository;
    private readonly IZoneDetailRepository _zoneDetailRepository;
    private readonly VesselMapper _mapper;
    private readonly ILogger<VesselAppService> _logger;
    private readonly IConfiguration _configuration;

    public VesselAppService(
        IImportVesselRepository importVesselRepository,
        IExportVesselRepository exportVesselRepository,
        ILocalVesselRepository localVesselRepository,
        IZoneDetailRepository zoneDetailRepository,
        VesselMapper mapper,
        ILogger<VesselAppService> logger,
        IConfiguration configuration)
    {
        _importVesselRepository = importVesselRepository;
        _exportVesselRepository = exportVesselRepository;
        _localVesselRepository = localVesselRepository;
        _zoneDetailRepository = zoneDetailRepository;
        _mapper = mapper;
        _logger = logger;
        _configuration = configuration;
    }

    public async Task<PagedResultDto<VesselHeaderDto>> VesselHeadersAsync(VesselListRequestDto input)
    {
        var vesselHeaders = new List<VesselHeaderDto>();
        var totalCount = 0;

        // Use filterGroup from input, or create a new one if null
        var filterGroup = input.FilterGroup ?? new FilterGroup
        {
            Operator = LogicalOperator.And,
            Conditions = new List<FilterCondition>()
        };

        // Split filterGroup into header and item filters
        var (headerFilterGroup, itemFilterGroup) = SplitHeaderAndItemFilters(filterGroup);

        // If there are item filters, find matching DocEntry values
        List<int> matchingDocEntries = null;
        if (itemFilterGroup.Conditions.Any())
        {
            var zoneDetailQuery = await _zoneDetailRepository.GetQueryableWithIncludesAsync();
            zoneDetailQuery = DynamicQueryBuilder<ZoneDetail>.ApplyFilters(zoneDetailQuery, itemFilterGroup);
            matchingDocEntries = await AsyncExecuter.ToListAsync(zoneDetailQuery.Select(z => z.DocNum.Value).Distinct());
        }

        // Remove vesselType filter for header queries (handled by repo selection)
        headerFilterGroup.Conditions = headerFilterGroup.Conditions
            .Where(c => !string.Equals(c.FieldName, "vesselType", StringComparison.OrdinalIgnoreCase))
            .ToList();

        var parameters = new QueryParametersDto
        {
            MaxResultCount = input.MaxResultCount,
            SkipCount = input.SkipCount,
            Sorting = input.Sorting,
            Sort = new List<SortInfo>(),
            FilterGroup = headerFilterGroup
        };

        // Import
        if (string.IsNullOrEmpty(input.VesselType) || input.VesselType == "Import")
        {
            var importQuery = await _importVesselRepository.GetQueryableWithIncludesAsync();
            importQuery = ApplyDynamicQuery(importQuery, parameters);
            if (matchingDocEntries != null)
                importQuery = importQuery.Where(v => matchingDocEntries.Contains(v.DocEntry));
            totalCount = await AsyncExecuter.CountAsync(importQuery);

            var importVessels = await AsyncExecuter.ToListAsync(
                importQuery.Skip(input.SkipCount).Take(input.MaxResultCount)
            );

            foreach (var vessel in importVessels)
            {
                var header = _mapper.MapImportVesselToHeaderDtoWithType(vessel);

                // EF Core will automatically populate navigation properties if included
                if (vessel.MasterJetty != null)
                {
                    header.Jetty = _mapper.MapJettyToJettyShortDto(vessel.MasterJetty);
                }

                var allItems = await GetVesselItemsForHeaderAsync(vessel.DocEntry, "Import");
                header.Items = itemFilterGroup.Conditions.Any()
                    ? allItems.Where(item => ItemMatchesFilter(item, itemFilterGroup)).ToList()
                    : allItems;
                vesselHeaders.Add(header);
            }
        }

        // Export
        if (string.IsNullOrEmpty(input.VesselType) || input.VesselType == "Export")
        {
            var exportQuery = await _exportVesselRepository.GetQueryableWithIncludesAsync();
            exportQuery = ApplyDynamicQuery(exportQuery, parameters);
            if (matchingDocEntries != null)
                exportQuery = exportQuery.Where(v => matchingDocEntries.Contains(v.DocEntry));
            totalCount = await AsyncExecuter.CountAsync(exportQuery);

            var exportVessels = await AsyncExecuter.ToListAsync(
                exportQuery.Skip(input.SkipCount).Take(input.MaxResultCount)
            );

            foreach (var vessel in exportVessels)
            {
                var header = _mapper.MapExportVesselToHeaderDtoWithType(vessel);

                // Use navigation properties instead of manual joins
                if (vessel.Vessel != null)
                {
                    header.VesselName = vessel.Vessel.Name;
                }

                if (vessel.MasterJetty != null)
                {
                    header.Jetty = _mapper.MapJettyToJettyShortDto(vessel.MasterJetty);
                }

                var allItems = await GetVesselItemsForHeaderAsync(vessel.DocEntry, "Export");
                header.Items = itemFilterGroup.Conditions.Any()
                    ? allItems.Where(item => ItemMatchesFilter(item, itemFilterGroup)).ToList()
                    : allItems;
                vesselHeaders.Add(header);
            }
        }

        // Local
        if (string.IsNullOrEmpty(input.VesselType) || input.VesselType == "LocalIn" || input.VesselType == "LocalOut")
        {
            var localQuery = await _localVesselRepository.GetQueryableWithIncludesAsync();
            if (input.VesselType == "LocalIn")
                localQuery = localQuery.Where(v => v.TransType == "IN");
            else if (input.VesselType == "LocalOut")
                localQuery = localQuery.Where(v => v.TransType == "OUT");
            localQuery = ApplyDynamicQuery(localQuery, parameters);
            if (matchingDocEntries != null)
                localQuery = localQuery.Where(v => matchingDocEntries.Contains(v.DocEntry));
            totalCount = await AsyncExecuter.CountAsync(localQuery);

            var localVessels = await AsyncExecuter.ToListAsync(
                localQuery.Skip(input.SkipCount).Take(input.MaxResultCount)
            );

            foreach (var vessel in localVessels)
            {
                var header = _mapper.MapLocalVesselToHeaderDtoWithType(vessel);

                // Use navigation properties instead of manual joins
                if (vessel.Vessel != null)
                {
                    header.VesselName = vessel.Vessel.Name;
                }

                if (vessel.Barge != null)
                {
                    header.Barge = _mapper.MapCargoToCargoShortDto(vessel.Barge);
                }

                if (vessel.MasterJetty != null)
                {
                    header.Jetty = _mapper.MapJettyToJettyShortDto(vessel.MasterJetty);
                }

                var vesselType = vessel.VesselType == "IN" ? "LocalIn" : "LocalOut";
                var allItems = await GetVesselItemsForHeaderAsync(vessel.DocEntry, vesselType);
                header.Items = itemFilterGroup.Conditions.Any()
                    ? allItems.Where(item => ItemMatchesFilter(item, itemFilterGroup)).ToList()
                    : allItems;
                vesselHeaders.Add(header);
            }
        }

        return new PagedResultDto<VesselHeaderDto>(totalCount, vesselHeaders);
    }

    public async Task<PagedResultDto<VesselItemDto>> VesselItemsAsync(VesselListRequestDto input)
    {
        // Convert VesselListRequestDto to QueryParametersDto for DynamicQueryBuilder
        var parameters = new QueryParametersDto
        {
            MaxResultCount = input.MaxResultCount,
            SkipCount = input.SkipCount,
            Sorting = input.Sorting,
            Sort = new List<SortInfo>(),
            FilterGroup = new FilterGroup
            {
                Operator = LogicalOperator.And,
                Conditions = new List<FilterCondition>()
            }
        };

        // Add date filters if specified
        if (input.FromDate.HasValue)
        {
            parameters.FilterGroup.Conditions.Add(new FilterCondition
            {
                FieldName = "CreationTime",
                Operator = FilterOperator.GreaterThanOrEqual,
                Value = input.FromDate.Value
            });
        }

        if (input.ToDate.HasValue)
        {
            parameters.FilterGroup.Conditions.Add(new FilterCondition
            {
                FieldName = "CreationTime",
                Operator = FilterOperator.LessThanOrEqual,
                Value = input.ToDate.Value
            });
        }

        // Get ZoneDetail items with tenant information using navigation properties
        var zoneDetailQuery = await _zoneDetailRepository.GetQueryableWithIncludesAsync();
        zoneDetailQuery = ApplyDynamicQuery(zoneDetailQuery, parameters);

        // Get total count
        var totalCount = await AsyncExecuter.CountAsync(zoneDetailQuery);

        // Get ZoneDetail items
        var zoneDetails = await AsyncExecuter.ToListAsync(
            zoneDetailQuery.Skip(input.SkipCount).Take(input.MaxResultCount)
        );

        var vesselItems = new List<VesselItemDto>();

        // Map to DTOs with vessel type determination and tenant information
        foreach (var zoneDetail in zoneDetails)
        {
            var vesselType = DetermineVesselTypeFromZoneDetail(zoneDetail);
            var itemDto = _mapper.MapZoneDetailToItemDtoWithType(zoneDetail, vesselType);

            // Use navigation property instead of manual lookup
            if (zoneDetail.Tenant != null)
            {
                itemDto.Tenant = _mapper.MapTenantToTenantShortDto(zoneDetail.Tenant);
            }

            // Map DocAttachments if available
            if (zoneDetail.DocAttachment != null && zoneDetail.DocAttachment.Any())
            {
                itemDto.Attachments = zoneDetail.DocAttachment.Select(att => MapDocAttachmentWithUrl(att, zoneDetail)).ToList();
            }
            else
            {
                itemDto.Attachments = new List<DocAttachmentSortDto>();
            }

            vesselItems.Add(itemDto);
        }

        return new PagedResultDto<VesselItemDto>(totalCount, vesselItems);
    }

    public async Task<VesselHeaderDto> VesselHeaderAsync(Guid id, string vesselType)
    {
        var header = vesselType switch
        {
            "Import" => await GetImportVesselHeaderAsync(id),
            "Export" => await GetExportVesselHeaderAsync(id),
            "LocalIn" or "LocalOut" => await GetLocalVesselHeaderAsync(id),
            _ => throw new ArgumentException($"Invalid vessel type: {vesselType}")
        };

        // Get the DocEntry from the header to fetch related items
        var docEntry = header.DocEntry;
        header.Items = await GetVesselItemsForHeaderAsync(docEntry, vesselType);

        return header;
    }

    public async Task<VesselItemDto> VesselItemAsync(Guid id, string vesselType)
    {
        var zoneDetail = await _zoneDetailRepository.GetAsync(id);
        var itemDto = _mapper.MapZoneDetailToItemDtoWithType(zoneDetail, vesselType);

        // Use navigation property instead of manual lookup
        if (zoneDetail.Tenant != null)
        {
            itemDto.Tenant = _mapper.MapTenantToTenantShortDto(zoneDetail.Tenant);
        }

        // Map DocAttachments if available
        if (zoneDetail.DocAttachment != null && zoneDetail.DocAttachment.Any())
        {
            itemDto.Attachments = zoneDetail.DocAttachment.Select(att => MapDocAttachmentWithUrl(att, zoneDetail)).ToList();
        }
        else
        {
            itemDto.Attachments = new List<DocAttachmentSortDto>();
        }

        return itemDto;
    }

    private async Task<List<VesselItemDto>> GetVesselItemsForHeaderAsync(int docEntry, string vesselType)
    {
        // Use the new HeaderId approach for loading vessel items with headers
        return await GetVesselItemsWithHeaderIdAsync(docEntry, vesselType);
    }

    private async Task<VesselHeaderDto> GetImportVesselHeaderAsync(Guid id)
    {
        var entity = await _importVesselRepository.GetAsync(id);
        var header = _mapper.MapImportVesselToHeaderDtoWithType(entity);

        // Use navigation property instead of manual lookup
        if (entity.MasterJetty != null)
        {
            header.Jetty = _mapper.MapJettyToJettyShortDto(entity.MasterJetty);
        }

        return header;
    }

    private async Task<VesselHeaderDto> GetExportVesselHeaderAsync(Guid id)
    {
        var entity = await _exportVesselRepository.GetAsync(id);
        var header = _mapper.MapExportVesselToHeaderDtoWithType(entity);

        // Use navigation properties instead of manual lookups
        if (entity.Vessel != null)
        {
            header.VesselName = entity.Vessel.Name;
        }

        if (entity.MasterJetty != null)
        {
            header.Jetty = _mapper.MapJettyToJettyShortDto(entity.MasterJetty);
        }

        return header;
    }

    private async Task<VesselHeaderDto> GetLocalVesselHeaderAsync(Guid id)
    {
        var entity = await _localVesselRepository.GetAsync(id);
        var header = _mapper.MapLocalVesselToHeaderDtoWithType(entity);

        // Use navigation properties instead of manual lookups
        if (entity.Vessel != null)
        {
            header.VesselName = entity.Vessel.Name;
        }

        if (entity.Barge != null)
        {
            header.Barge = _mapper.MapCargoToCargoShortDto(entity.Barge);
        }

        if (entity.MasterJetty != null)
        {
            header.Jetty = _mapper.MapJettyToJettyShortDto(entity.MasterJetty);
        }

        return header;
    }

    private IQueryable<T> ApplyDynamicQuery<T>(IQueryable<T> query, QueryParametersDto parameters) where T : class
    {
        if (parameters.FilterGroup?.Conditions != null && parameters.FilterGroup.Conditions.Count > 0)
        {
            query = DynamicQueryBuilder<T>.ApplyFilters(query, parameters.FilterGroup);
        }

        if (parameters.Sort?.Count > 0)
        {
            query = DynamicQueryBuilder<T>.ApplyMultipleSorting(query, parameters.Sort);
        }
        else if (!string.IsNullOrEmpty(parameters.Sorting))
        {
            // Parse sorting direction from the sorting string
            var sortingParts = parameters.Sorting.Trim().Split(' ', StringSplitOptions.RemoveEmptyEntries);
            var sortField = sortingParts[0];
            var isDescending = sortingParts.Length > 1 && sortingParts[1].ToLowerInvariant() == "desc";

            query = DynamicQueryBuilder<T>.ApplySorting(query, sortField, isDescending);
        }
        else
        {
            // Default sorting by DocEntry descending
            query = DynamicQueryBuilder<T>.ApplySorting(query, "DocEntry", true);
        }

        return query;
    }

    private string DetermineVesselTypeFromZoneDetail(ZoneDetail zoneDetail)
    {
        // This is a simplified logic - you might need to implement more sophisticated logic
        // based on your business rules to determine vessel type from ZoneDetail
        if (!string.IsNullOrEmpty(zoneDetail.DocType))
        {
            return zoneDetail.DocType.ToUpper() switch
            {
                "IMPORT" => "Import",
                "EXPORT" => "Export",
                "LOCAL" => "LocalIn", // Default to LocalIn, you might need more logic
                _ => "Import" // Default fallback
            };
        }

        return "Import"; // Default fallback
    }

    /// <summary>
    /// Loads the appropriate vessel header based on DocType and HeaderId
    /// This handles the polymorphic relationship between ZoneDetail and vessel headers
    /// </summary>
    private async Task<VesselHeaderDto?> LoadVesselHeaderAsync(string docType, Guid? headerId)
    {
        if (!headerId.HasValue)
            return null;

        return docType?.ToUpper() switch
        {
            "IMPORT" => await GetImportVesselHeaderAsync(headerId.Value),
            "EXPORT" => await GetExportVesselHeaderAsync(headerId.Value),
            "LOCAL" => await GetLocalVesselHeaderAsync(headerId.Value),
            _ => null
        };
    }

    /// <summary>
    /// Loads vessel items with their corresponding headers using HeaderId relationships
    /// This uses the new HeaderId (GUID) relationships with navigation properties
    /// </summary>
    private async Task<List<VesselItemDto>> GetVesselItemsWithHeaderIdAsync(int docEntry, string vesselType)
    {
        var zoneDetailQuery = await _zoneDetailRepository.GetQueryableWithVesselHeadersAsync();

        // Determine the DocType filter based on vesselType
        string? docTypeFilter = vesselType.ToUpper() switch
        {
            "IMPORT" => "Import",
            "EXPORT" => "Export",
            "LOCALIN" or "LOCALOUT" => "Local",
            _ => null
        };

        // Build the query with DocNum and DocType filters
        var query = zoneDetailQuery.Where(z => z.DocNum == docEntry);

        // Add DocType filter if specified
        if (!string.IsNullOrEmpty(docTypeFilter))
        {
            query = query.Where(z => z.DocType == docTypeFilter);
        }

        var docNumItems = await AsyncExecuter.ToListAsync(query);

        var items = new List<VesselItemDto>();
        foreach (var zoneDetail in docNumItems)
        {
            var itemDto = _mapper.MapZoneDetailToItemDtoWithType(zoneDetail, vesselType);

            // Use navigation property for Tenant
            if (zoneDetail.Tenant != null)
            {
                itemDto.Tenant = _mapper.MapTenantToTenantShortDto(zoneDetail.Tenant);
            }

            // Load vessel header using navigation properties (new HeaderId approach)
            var header = docTypeFilter switch
            {
                "Import" => zoneDetail.ImportVessel != null ? _mapper.MapImportVesselToHeaderDtoWithType(zoneDetail.ImportVessel) : null,
                "Export" => zoneDetail.ExportVessel != null ? _mapper.MapExportVesselToHeaderDtoWithType(zoneDetail.ExportVessel) : null,
                "Local" => zoneDetail.LocalVessel != null ? _mapper.MapLocalVesselToHeaderDtoWithType(zoneDetail.LocalVessel) : null,
                _ => null
            };

            // Map DocAttachments if available
            if (zoneDetail.DocAttachment != null && zoneDetail.DocAttachment.Any())
            {
                itemDto.Attachments = zoneDetail.DocAttachment.Select(att => MapDocAttachmentWithUrl(att, zoneDetail)).ToList();
            }
            else
            {
                itemDto.Attachments = new List<DocAttachmentSortDto>();
            }

            items.Add(itemDto);
        }

        return items;
    }

    /// <summary>
    /// Loads vessel items with their corresponding headers using service methods (legacy DocEntry approach)
    /// This uses the existing DocEntry relationships for backward compatibility
    /// </summary>
    private async Task<List<VesselItemDto>> GetVesselItemsWithServiceHeadersAsync(int docEntry, string vesselType)
    {
        var zoneDetailQuery = await _zoneDetailRepository.GetQueryableWithIncludesAsync();

        // Determine the DocType filter based on vesselType
        string? docTypeFilter = vesselType.ToUpper() switch
        {
            "IMPORT" => "Import",
            "EXPORT" => "Export",
            "LOCALIN" or "LOCALOUT" => "Local",
            _ => null
        };

        // Build the query with DocNum and DocType filters
        var query = zoneDetailQuery.Where(z => z.DocNum == docEntry);

        // Add DocType filter if specified
        if (!string.IsNullOrEmpty(docTypeFilter))
        {
            query = query.Where(z => z.DocType == docTypeFilter);
        }

        var docNumItems = await AsyncExecuter.ToListAsync(query);

        var items = new List<VesselItemDto>();
        foreach (var zoneDetail in docNumItems)
        {
            var itemDto = _mapper.MapZoneDetailToItemDtoWithType(zoneDetail, vesselType);

            // Use navigation property for Tenant
            if (zoneDetail.Tenant != null)
            {
                itemDto.Tenant = _mapper.MapTenantToTenantShortDto(zoneDetail.Tenant);
            }

            // Load vessel header using service methods (legacy approach)
            // if (zoneDetail.HeaderId.HasValue)
            // {
            //     var header = await LoadVesselHeaderAsync(zoneDetail.DocType, zoneDetail.HeaderId);
            //     if (header != null)
            //     {
            //         itemDto.VesselHeader = header;
            //     }
            // }

            // Map DocAttachments if available
            if (zoneDetail.DocAttachment != null && zoneDetail.DocAttachment.Any())
            {
                itemDto.Attachments = zoneDetail.DocAttachment.Select(att => MapDocAttachmentWithUrl(att, zoneDetail)).ToList();
            }
            else
            {
                itemDto.Attachments = new List<DocAttachmentSortDto>();
            }

            items.Add(itemDto);
        }

        return items;
    }

    // Helper: Split filterGroup into header and item filters
    private (FilterGroup header, FilterGroup item) SplitHeaderAndItemFilters(FilterGroup filterGroup)
    {
        var headerConditions = new List<FilterCondition>();
        var itemConditions = new List<FilterCondition>();
        foreach (var cond in filterGroup.Conditions)
        {
            if (cond.FieldName.StartsWith("items.", StringComparison.OrdinalIgnoreCase))
                itemConditions.Add(new FilterCondition
                {
                    FieldName = cond.FieldName.Substring(6), // remove 'items.'
                    Operator = cond.Operator,
                    Value = cond.Value
                });
            else
                headerConditions.Add(cond);
        }
        return (
            new FilterGroup { Operator = filterGroup.Operator, Conditions = headerConditions },
            new FilterGroup { Operator = filterGroup.Operator, Conditions = itemConditions }
        );
    }

    // Helper: Check if a VesselItemDto matches the item filter group
    private bool ItemMatchesFilter(VesselItemDto item, FilterGroup filterGroup)
    {
        foreach (var cond in filterGroup.Conditions)
        {
            var value = cond.FieldName.ToLower() switch
            {
                "tenant.name" => item.Tenant?.Name,
                "itemname" => item.ItemName,
                "remarks" => item.Remarks,
                "cargo" => item.Cargo,
                "shipment" => item.Shipment,
                _ => null
            };
            if (value == null) return false;
            if (cond.Operator == FilterOperator.Contains && !value.Contains(cond.Value?.ToString(), StringComparison.OrdinalIgnoreCase))
                return false;
            if (cond.Operator == FilterOperator.Equals && value != cond.Value?.ToString())
                return false;
        }
        return true;
    }

    private DocAttachmentSortDto MapDocAttachmentWithUrl(DocAttachment att, ZoneDetail zoneDetail)
    {
        var itemDto = _mapper.MapDocAttachmentToDto(att);

        // Generate SFTP path for Import vessel type only
        if (zoneDetail.DocType == "Import" && zoneDetail.DocNum.HasValue && !string.IsNullOrEmpty(zoneDetail.NoBl))
        {
            var docNum = zoneDetail.ImportVessel.DocNum.Value.ToString();
            var blNo = zoneDetail.NoBl.ToUpper().Replace(" ", "-").Replace("_", "-");
            var fileName = att.FileName;

            // Generate the correct SFTP path that matches the actual file structure on SFTP
            // Based on the actual SFTP path: /ekb/docs/IMPORT/...
            var filePath = $"ekb/docs/IMPORT/{docNum}/{blNo}/{fileName}";

            // Generate full URL for streaming using SFTP path
            var baseUrl = GetBaseUrl();
            itemDto.StreamUrl = $"{baseUrl}/api/ekb/files/stream/{filePath}";
        }

        return itemDto;
    }

    private string GetBaseUrl()
    {
        // Get the base URL from configuration or request
        var baseUrl = _configuration["App:SelfUrl"];
        if (string.IsNullOrEmpty(baseUrl))
        {
            // Fallback to localhost if not configured
            baseUrl = "https://localhost:44300";
        }

        // Remove trailing slash if present
        return baseUrl.TrimEnd('/');
    }
}