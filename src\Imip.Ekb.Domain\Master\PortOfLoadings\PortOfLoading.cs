﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Imip.Ekb.BoundedZone.ExportVessels;
using Imip.Ekb.BoundedZone.ImportVessels;
using Imip.Ekb.BoundedZone.LocalVessels;
using Volo.Abp.Domain.Entities.Auditing;

namespace Imip.Ekb.Master.PortOfLoadings;

[Table("M_PortOfLoading")]
public class PortOfLoading : FullAuditedAggregateRoot<Guid>
{
    [Key]
    public int DocEntry { get; set; }

    [StringLength(255)]
    public string Name { get; set; } = null!;

    [StringLength(4)]
    public string Deleted { get; set; } = null!;

    public int? CreatedBy { get; set; }

    public int? UpdatedBy { get; set; }

    [Column("created_at", TypeName = "datetime")]
    public DateTime? CreatedAt { get; set; }

    [Column("updated_at", TypeName = "datetime")]
    public DateTime? UpdatedAt { get; set; }

    [StringLength(255)]
    public string? Country { get; set; }

    [StringLength(100)]
    public string DocType { get; set; } = null!;

    public virtual ICollection<LocalVessel>? LocalVessels { get; set; }
    public virtual ICollection<ExportVessel>? ExportVessels { get; set; }
    public virtual ICollection<ImportVessel>? ImportVessels { get; set; }



}
