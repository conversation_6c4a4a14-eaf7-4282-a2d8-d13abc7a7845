using System;
using System.Threading.Tasks;
using Imip.Ekb.Master.Cargos.Dtos;
using Imip.Ekb.Models;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.Ekb.Master.Cargos;

public interface ICargoAppService :
    ICrudAppService<CargoDto, Guid, PagedAndSortedResultRequestDto, CargoCreateUpdateDto, CargoCreateUpdateDto>
{
    Task<PagedResultDto<CargoDto>> FilterListAsync(QueryParametersDto parameters);
}