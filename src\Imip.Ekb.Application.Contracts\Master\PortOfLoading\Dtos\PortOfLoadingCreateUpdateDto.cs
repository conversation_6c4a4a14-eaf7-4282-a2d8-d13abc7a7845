using System;
using System.ComponentModel.DataAnnotations;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Entities;

namespace Imip.Ekb.Master.PortOfLoadings.Dtos;

public class PortOfLoadingCreateUpdateDto : EntityDto<Guid>, IHasConcurrencyStamp
{
    public string ConcurrencyStamp { get; set; }
    // [Required]
    // public int DocEntry { get; set; }

    [Required]
    [StringLength(255)]
    public string Name { get; set; } = string.Empty;

    [Required]
    [StringLength(4)]
    public string Deleted { get; set; } = string.Empty;

    public int? CreatedBy { get; set; }
    public int? UpdatedBy { get; set; }

    [StringLength(255)]
    public string? Country { get; set; }

    [Required]
    [StringLength(100)]
    public string DocType { get; set; } = string.Empty;
}