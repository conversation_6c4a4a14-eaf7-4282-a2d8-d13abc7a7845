using System;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Entities;

namespace Imip.Ekb.Master.Agents.Dtos;

public class AgentDto : AuditedEntityDto<Guid>, IHasConcurrencyStamp
{
    public string ConcurrencyStamp { get; set; }
    public int DocEntry { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public string? Type { get; set; }
    public int? CreatedBy { get; set; }
    public int? UpdatedBy { get; set; }
    public DateTime? CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public string? NpwpNo { get; set; }
    public string? BdmSapcode { get; set; }
    public string? TaxCode { get; set; }
    public string? AddressNpwp { get; set; }
    public string? Address { get; set; }
    public string? SapcodeS4 { get; set; }
}