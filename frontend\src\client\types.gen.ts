// This file is auto-generated by @hey-api/openapi-ts

export type AbpLoginResult = {
    result?: LoginResultType;
    readonly description?: string | null;
};

export type AgentDto = {
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    docEntry?: number;
    name?: string | null;
    status?: string | null;
    type?: string | null;
    createdBy?: number | null;
    updatedBy?: number | null;
    createdAt?: string | null;
    updatedAt?: string | null;
    npwpNo?: string | null;
    bdmSapcode?: string | null;
    taxCode?: string | null;
    addressNpwp?: string | null;
    address?: string | null;
    sapcodeS4?: string | null;
};

export type ApplicationAuthConfigurationDto = {
    grantedPolicies?: {
        [key: string]: boolean;
    } | null;
};

export type ApplicationConfigurationDto = {
    localization?: ApplicationLocalizationConfigurationDto;
    auth?: ApplicationAuthConfigurationDto;
    setting?: ApplicationSettingConfigurationDto;
    currentUser?: CurrentUserDto;
    features?: ApplicationFeatureConfigurationDto;
    globalFeatures?: ApplicationGlobalFeatureConfigurationDto;
    multiTenancy?: MultiTenancyInfoDto;
    currentTenant?: CurrentTenantDto;
    timing?: TimingDto;
    clock?: ClockDto;
    objectExtensions?: ObjectExtensionsDto;
    extraProperties?: {
        [key: string]: unknown;
    } | null;
};

export type ApplicationFeatureConfigurationDto = {
    values?: {
        [key: string]: string | null;
    } | null;
};

export type ApplicationGlobalFeatureConfigurationDto = {
    enabledFeatures?: Array<string> | null;
};

export type ApplicationLocalizationConfigurationDto = {
    values?: {
        [key: string]: {
            [key: string]: string;
        };
    } | null;
    resources?: {
        [key: string]: ApplicationLocalizationResourceDto;
    } | null;
    languages?: Array<LanguageInfo> | null;
    currentCulture?: CurrentCultureDto;
    defaultResourceName?: string | null;
    languagesMap?: {
        [key: string]: Array<NameValue>;
    } | null;
    languageFilesMap?: {
        [key: string]: Array<NameValue>;
    } | null;
};

export type ApplicationLocalizationResourceDto = {
    texts?: {
        [key: string]: string;
    } | null;
    baseResources?: Array<string> | null;
};

export type ApplicationSettingConfigurationDto = {
    values?: {
        [key: string]: string | null;
    } | null;
};

export type BcTypeCreateUpdateDto = {
    docEntry?: number;
    type?: string | null;
    createdBy?: string | null;
    transNo?: number | null;
    transName?: string | null;
    status?: string | null;
};

export type BcTypeDto = {
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    docEntry?: number;
    type?: string | null;
    createdBy?: string | null;
    createdAt?: string | null;
    updatedAt?: string | null;
    transNo?: number | null;
    transName?: string | null;
    status?: string | null;
};

export type BusinessPartnerCreateUpdateDto = {
    name: string;
    createdBy: string;
    status: string;
    alias?: string | null;
    image?: string | null;
    direction?: string | null;
    regionType: string;
    address?: string | null;
    tenant?: string | null;
    npwp1?: string | null;
    npwp2?: string | null;
};

export type BusinessPartnerDto = {
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    name?: string | null;
    createdBy?: string | null;
    createdAt?: string | null;
    updatedAt?: string | null;
    status?: string | null;
    alias?: string | null;
    image?: string | null;
    direction?: string | null;
    regionType?: string | null;
    address?: string | null;
    tenant?: string | null;
    npwp1?: string | null;
    npwp2?: string | null;
};

export type CargoCreateUpdateDto = {
    docEntry: number;
    name: string;
    createdBy: string;
    status: string;
    alias?: string | null;
    flag?: string | null;
    grossWeight: number;
    type?: string | null;
    loaQty?: number | null;
};

export type CargoDto = {
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    docEntry?: number;
    name?: string | null;
    createdBy?: string | null;
    createdAt?: string | null;
    updatedAt?: string | null;
    status?: string | null;
    alias?: string | null;
    flag?: string | null;
    grossWeight?: number;
    type?: string | null;
    loaQty?: number | null;
};

export type CargoShortDto = {
    docEntry?: number;
    id?: string;
    name?: string | null;
    alias?: string | null;
    type?: string | null;
    grossWeight?: number | null;
};

export type ChangePasswordInput = {
    currentPassword?: string | null;
    newPassword: string;
};

export type ClockDto = {
    kind?: string | null;
};

export type CurrentCultureDto = {
    displayName?: string | null;
    englishName?: string | null;
    threeLetterIsoLanguageName?: string | null;
    twoLetterIsoLanguageName?: string | null;
    isRightToLeft?: boolean;
    cultureName?: string | null;
    name?: string | null;
    nativeName?: string | null;
    dateTimeFormat?: DateTimeFormatDto;
};

export type CurrentTenantDto = {
    id?: string | null;
    name?: string | null;
    isAvailable?: boolean;
};

export type CurrentUserDto = {
    isAuthenticated?: boolean;
    id?: string | null;
    tenantId?: string | null;
    impersonatorUserId?: string | null;
    impersonatorTenantId?: string | null;
    impersonatorUserName?: string | null;
    impersonatorTenantName?: string | null;
    userName?: string | null;
    name?: string | null;
    surName?: string | null;
    email?: string | null;
    emailVerified?: boolean;
    phoneNumber?: string | null;
    phoneNumberVerified?: boolean;
    roles?: Array<string> | null;
    sessionId?: string | null;
};

export type DateTimeFormatDto = {
    calendarAlgorithmType?: string | null;
    dateTimeFormatLong?: string | null;
    shortDatePattern?: string | null;
    fullDateTimePattern?: string | null;
    dateSeparator?: string | null;
    shortTimePattern?: string | null;
    longTimePattern?: string | null;
};

export type DestinationPortCreateUpdateDto = {
    docEntry: number;
    name: string;
    deleted: string;
    createdBy?: number | null;
    updatedBy?: number | null;
    country?: string | null;
    docType: string;
};

export type DestinationPortDto = {
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    docEntry?: number;
    name?: string | null;
    deleted?: string | null;
    createdBy?: number | null;
    updatedBy?: number | null;
    createdAt?: string | null;
    updatedAt?: string | null;
    country?: string | null;
    docType?: string | null;
};

export type EntityExtensionDto = {
    properties?: {
        [key: string]: ExtensionPropertyDto;
    } | null;
    configuration?: {
        [key: string]: unknown;
    } | null;
};

export type ExtensionEnumDto = {
    fields?: Array<ExtensionEnumFieldDto> | null;
    localizationResource?: string | null;
};

export type ExtensionEnumFieldDto = {
    name?: string | null;
    value?: unknown;
};

export type ExtensionPropertyApiCreateDto = {
    isAvailable?: boolean;
};

export type ExtensionPropertyApiDto = {
    onGet?: ExtensionPropertyApiGetDto;
    onCreate?: ExtensionPropertyApiCreateDto;
    onUpdate?: ExtensionPropertyApiUpdateDto;
};

export type ExtensionPropertyApiGetDto = {
    isAvailable?: boolean;
};

export type ExtensionPropertyApiUpdateDto = {
    isAvailable?: boolean;
};

export type ExtensionPropertyAttributeDto = {
    typeSimple?: string | null;
    config?: {
        [key: string]: unknown;
    } | null;
};

export type ExtensionPropertyDto = {
    type?: string | null;
    typeSimple?: string | null;
    displayName?: LocalizableStringDto;
    api?: ExtensionPropertyApiDto;
    ui?: ExtensionPropertyUiDto;
    policy?: ExtensionPropertyPolicyDto;
    attributes?: Array<ExtensionPropertyAttributeDto> | null;
    configuration?: {
        [key: string]: unknown;
    } | null;
    defaultValue?: unknown;
};

export type ExtensionPropertyFeaturePolicyDto = {
    features?: Array<string> | null;
    requiresAll?: boolean;
};

export type ExtensionPropertyGlobalFeaturePolicyDto = {
    features?: Array<string> | null;
    requiresAll?: boolean;
};

export type ExtensionPropertyPermissionPolicyDto = {
    permissionNames?: Array<string> | null;
    requiresAll?: boolean;
};

export type ExtensionPropertyPolicyDto = {
    globalFeatures?: ExtensionPropertyGlobalFeaturePolicyDto;
    features?: ExtensionPropertyFeaturePolicyDto;
    permissions?: ExtensionPropertyPermissionPolicyDto;
};

export type ExtensionPropertyUiDto = {
    onTable?: ExtensionPropertyUiTableDto;
    onCreateForm?: ExtensionPropertyUiFormDto;
    onEditForm?: ExtensionPropertyUiFormDto;
    lookup?: ExtensionPropertyUiLookupDto;
};

export type ExtensionPropertyUiFormDto = {
    isVisible?: boolean;
};

export type ExtensionPropertyUiLookupDto = {
    url?: string | null;
    resultListPropertyName?: string | null;
    displayPropertyName?: string | null;
    valuePropertyName?: string | null;
    filterParamName?: string | null;
};

export type ExtensionPropertyUiTableDto = {
    isVisible?: boolean;
};

export type FilterCondition = {
    fieldName: string;
    operator: FilterOperator;
    value: unknown;
};

export type FilterGroup = {
    operator: LogicalOperator;
    conditions: Array<FilterCondition>;
};

export type FilterOperator = 'Equals' | 'NotEquals' | 'Contains' | 'StartsWith' | 'EndsWith' | 'GreaterThan' | 'GreaterThanOrEqual' | 'LessThan' | 'LessThanOrEqual' | 'In' | 'NotIn' | 'Between' | 'NotBetween' | 'IsNull' | 'IsNotNull' | 'IsEmpty' | 'IsNotEmpty' | 'IsTrue' | 'IsFalse' | 'IsNullOrEmpty' | 'IsNotNullOrEmpty' | 'IsNullOrWhiteSpace' | 'IsNotNullOrWhiteSpace' | 'IsNumeric' | 'IsAlpha' | 'IsAlphaNumeric' | 'IsEmail' | 'IsUrl' | 'IsIp' | 'IsIpv4' | 'IsIpv6' | 'IsGuid' | 'IsGuidEmpty' | 'IsGuidNotEmpty' | 'IsGuidNull' | 'IsGuidNotNull' | 'IsGuidNullOrEmpty' | 'IsGuidNotNullOrEmpty' | 'IsGuidNullOrWhiteSpace' | 'IsGuidNotNullOrWhiteSpace' | 'IsGuidNumeric' | 'IsGuidAlpha' | 'IsGuidAlphaNumeric';

export type FindTenantResultDto = {
    success?: boolean;
    tenantId?: string | null;
    name?: string | null;
    normalizedName?: string | null;
    isActive?: boolean;
};

export type IanaTimeZone = {
    timeZoneName?: string | null;
};

export type IdentityUserDto = {
    readonly extraProperties?: {
        [key: string]: unknown;
    } | null;
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    isDeleted?: boolean;
    deleterId?: string | null;
    deletionTime?: string | null;
    tenantId?: string | null;
    userName?: string | null;
    name?: string | null;
    surname?: string | null;
    email?: string | null;
    emailConfirmed?: boolean;
    phoneNumber?: string | null;
    phoneNumberConfirmed?: boolean;
    isActive?: boolean;
    lockoutEnabled?: boolean;
    accessFailedCount?: number;
    lockoutEnd?: string | null;
    concurrencyStamp?: string | null;
    entityVersion?: number;
    lastPasswordChangeTime?: string | null;
};

export type ItemClassificationCreateUpdateDto = {
    docEntry?: number;
    name?: string | null;
    reportType?: string | null;
    deleted?: string | null;
    createdBy?: number | null;
    updatedBy?: number | null;
    category?: number | null;
};

export type ItemClassificationDto = {
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    docEntry?: number;
    name?: string | null;
    reportType?: string | null;
    deleted?: string | null;
    createdBy?: number | null;
    updatedBy?: number | null;
    createdAt?: string | null;
    updatedAt?: string | null;
    category?: number | null;
};

export type JettyCreateUpdateDto = {
    docEntry: number;
    name: string;
    alias: string;
    max: number;
    deleted: string;
    createdBy: number;
    updatedBy: number;
    port?: string | null;
};

export type JettyDto = {
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    docEntry?: number;
    name?: string | null;
    alias?: string | null;
    max?: number;
    deleted?: string | null;
    createdBy?: number;
    updatedBy?: number;
    createdAt?: string | null;
    updatedAt?: string | null;
    port?: string | null;
};

export type JettyShortDto = {
    docEntry?: number;
    id?: string;
    name?: string | null;
    alias?: string | null;
    port?: string | null;
    max?: number | null;
};

export type LanguageInfo = {
    cultureName?: string | null;
    uiCultureName?: string | null;
    displayName?: string | null;
    readonly twoLetterISOLanguageName?: string | null;
};

export type LocalizableStringDto = {
    name?: string | null;
    resource?: string | null;
};

export type LogicalOperator = 'And' | 'Or';

export type LoginResultType = 1 | 2 | 3 | 4 | 5;

export type MasterTenantDto = {
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    docEntry?: number;
    name?: string | null;
    createdBy?: string | null;
    createdAt?: string | null;
    updatedAt?: string | null;
    flags?: string | null;
    fullName?: string | null;
    letterPerson?: string | null;
    letterRole?: string | null;
    npwp?: string | null;
    address?: string | null;
    nib?: string | null;
    phone?: string | null;
    status?: string | null;
    noAndDateNotaris?: string | null;
    descNotaris?: string | null;
    sapcode?: string | null;
    isExternal?: string | null;
    billing?: string | null;
    billingPrice?: number | null;
    esignUserId?: string | null;
    token?: string | null;
    sapcodeBdt?: string | null;
    sapcodeUsd?: string | null;
    coordinate?: string | null;
    boundaries?: string | null;
    isTenant?: string | null;
    channelId?: string | null;
    usePrivy?: string | null;
    sapcodeS4?: string | null;
    skbpph?: string | null;
    companyGroup?: string | null;
    factoryLocation?: string | null;
    masterGroupId?: number | null;
};

export type ModuleExtensionDto = {
    entities?: {
        [key: string]: EntityExtensionDto;
    } | null;
    configuration?: {
        [key: string]: unknown;
    } | null;
};

export type MultiTenancyInfoDto = {
    isEnabled?: boolean;
};

export type NameValue = {
    name?: string | null;
    value?: string | null;
};

export type ObjectExtensionsDto = {
    modules?: {
        [key: string]: ModuleExtensionDto;
    } | null;
    enums?: {
        [key: string]: ExtensionEnumDto;
    } | null;
};

export type PagedResultDtoOfAgentDto = {
    items?: Array<AgentDto> | null;
    totalCount?: number;
};

export type PagedResultDtoOfBcTypeDto = {
    items?: Array<BcTypeDto> | null;
    totalCount?: number;
};

export type PagedResultDtoOfBusinessPartnerDto = {
    items?: Array<BusinessPartnerDto> | null;
    totalCount?: number;
};

export type PagedResultDtoOfCargoDto = {
    items?: Array<CargoDto> | null;
    totalCount?: number;
};

export type PagedResultDtoOfDestinationPortDto = {
    items?: Array<DestinationPortDto> | null;
    totalCount?: number;
};

export type PagedResultDtoOfItemClassificationDto = {
    items?: Array<ItemClassificationDto> | null;
    totalCount?: number;
};

export type PagedResultDtoOfJettyDto = {
    items?: Array<JettyDto> | null;
    totalCount?: number;
};

export type PagedResultDtoOfMasterTenantDto = {
    items?: Array<MasterTenantDto> | null;
    totalCount?: number;
};

export type PagedResultDtoOfPortOfLoadingDto = {
    items?: Array<PortOfLoadingDto> | null;
    totalCount?: number;
};

export type PagedResultDtoOfPortServiceDto = {
    items?: Array<PortServiceDto> | null;
    totalCount?: number;
};

export type PagedResultDtoOfSurveyorDto = {
    items?: Array<SurveyorDto> | null;
    totalCount?: number;
};

export type PagedResultDtoOfTenantDto = {
    items?: Array<TenantDto> | null;
    totalCount?: number;
};

export type PagedResultDtoOfVesselHeaderDto = {
    items?: Array<VesselHeaderDto> | null;
    totalCount?: number;
};

export type PagedResultDtoOfVesselItemDto = {
    items?: Array<VesselItemDto> | null;
    totalCount?: number;
};

export type PagedResultDtoOfZoneDetailDto = {
    items?: Array<ZoneDetailDto> | null;
    totalCount?: number;
};

export type PortOfLoadingCreateUpdateDto = {
    docEntry: number;
    name: string;
    deleted: string;
    createdBy?: number | null;
    updatedBy?: number | null;
    country?: string | null;
    docType: string;
};

export type PortOfLoadingDto = {
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    docEntry?: number;
    name?: string | null;
    deleted?: string | null;
    createdBy?: number | null;
    updatedBy?: number | null;
    createdAt?: string | null;
    updatedAt?: string | null;
    country?: string | null;
    docType?: string | null;
};

export type PortServiceCreateUpdateDto = {
    docEntry?: number;
    itemCode?: string | null;
    itemName?: string | null;
    active?: string | null;
    createdBy?: number | null;
    updatedBy?: number | null;
    frgnName?: string | null;
    sapCodeS4?: string | null;
};

export type PortServiceDto = {
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    docEntry?: number;
    itemCode?: string | null;
    itemName?: string | null;
    active?: string | null;
    createdBy?: number | null;
    updatedBy?: number | null;
    frgnName?: string | null;
    sapCodeS4?: string | null;
};

export type ProfileDto = {
    readonly extraProperties?: {
        [key: string]: unknown;
    } | null;
    userName?: string | null;
    email?: string | null;
    name?: string | null;
    surname?: string | null;
    phoneNumber?: string | null;
    isExternal?: boolean;
    hasPassword?: boolean;
    concurrencyStamp?: string | null;
};

export type QueryParametersDto = {
    sorting?: string | null;
    page?: number;
    sort?: Array<SortInfo> | null;
    filterGroup?: FilterGroup;
    readonly skipCount?: number;
    maxResultCount?: number;
};

export type RegisterDto = {
    readonly extraProperties?: {
        [key: string]: unknown;
    } | null;
    userName: string;
    emailAddress: string;
    password: string;
    appName: string;
};

export type RemoteServiceErrorInfo = {
    code?: string | null;
    message?: string | null;
    details?: string | null;
    data?: {
        [key: string]: unknown;
    } | null;
    validationErrors?: Array<RemoteServiceValidationErrorInfo> | null;
};

export type RemoteServiceErrorResponse = {
    error?: RemoteServiceErrorInfo;
};

export type RemoteServiceValidationErrorInfo = {
    message?: string | null;
    members?: Array<string> | null;
};

export type ResetPasswordDto = {
    userId?: string;
    resetToken: string;
    password: string;
};

export type SendPasswordResetCodeDto = {
    email: string;
    appName: string;
    returnUrl?: string | null;
    returnUrlHash?: string | null;
};

export type SortInfo = {
    field?: string | null;
    desc?: boolean;
};

export type SurveyorCreateUpdateDto = {
    docEntry: number;
    name: string;
    address?: string | null;
    npwp?: string | null;
    isActive: string;
    createdBy: number;
    updatedBy?: number | null;
};

export type SurveyorDto = {
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    docEntry?: number;
    name?: string | null;
    address?: string | null;
    npwp?: string | null;
    isActive?: string | null;
    createdBy?: number;
    updatedBy?: number | null;
};

export type TenantCreateDto = {
    readonly extraProperties?: {
        [key: string]: unknown;
    } | null;
    name: string;
    adminEmailAddress: string;
    adminPassword: string;
};

export type TenantCreateUpdateDto = {
    docEntry: number;
    name: string;
    createdBy: string;
    flags?: string | null;
    fullName?: string | null;
    letterPerson?: string | null;
    letterRole?: string | null;
    npwp?: string | null;
    address?: string | null;
    nib?: string | null;
    phone?: string | null;
    status: string;
    noAndDateNotaris?: string | null;
    descNotaris?: string | null;
    sapcode?: string | null;
    isExternal: string;
    billing?: string | null;
    billingPrice?: number | null;
    esignUserId?: string | null;
    token?: string | null;
    sapcodeBdt?: string | null;
    sapcodeUsd?: string | null;
    coordinate?: string | null;
    boundaries?: string | null;
    isTenant?: string | null;
    channelId?: string | null;
    usePrivy: string;
    sapcodeS4?: string | null;
    skbpph?: string | null;
    companyGroup?: string | null;
    factoryLocation?: string | null;
    masterGroupId?: number | null;
};

export type TenantDto = {
    readonly extraProperties?: {
        [key: string]: unknown;
    } | null;
    id?: string;
    name?: string | null;
    concurrencyStamp?: string | null;
};

export type TenantShortDto = {
    docEntry?: number;
    id?: string;
    name?: string | null;
    fullName?: string | null;
};

export type TenantUpdateDto = {
    readonly extraProperties?: {
        [key: string]: unknown;
    } | null;
    name: string;
    concurrencyStamp?: string | null;
};

export type TimeZone = {
    iana?: IanaTimeZone;
    windows?: WindowsTimeZone;
};

export type TimingDto = {
    timeZone?: TimeZone;
};

export type UpdateProfileDto = {
    readonly extraProperties?: {
        [key: string]: unknown;
    } | null;
    userName?: string | null;
    email?: string | null;
    name?: string | null;
    surname?: string | null;
    phoneNumber?: string | null;
    concurrencyStamp?: string | null;
};

export type UserLoginInfo = {
    userNameOrEmailAddress: string;
    password: string;
    rememberMe?: boolean;
};

export type VerifyPasswordResetTokenInput = {
    userId?: string;
    resetToken: string;
};

export type VesselHeaderDto = {
    id?: string;
    docEntry?: number;
    vesselName?: string | null;
    voyage?: string | null;
    vesselArrival?: string | null;
    vesselDeparture?: string | null;
    vesselType?: string | null;
    items?: Array<VesselItemDto> | null;
    cargo?: CargoShortDto;
    barge?: CargoShortDto;
    jetty?: JettyShortDto;
    portOrigin?: string | null;
    destinationPort?: string | null;
    berthingDate?: string | null;
    anchorageDate?: string | null;
    unloadingDate?: string | null;
    finishUnloadingDate?: string | null;
    grtWeight?: number | null;
    agentName?: string | null;
};

export type VesselItemDto = {
    id?: string;
    docEntry?: number;
    docNum?: number;
    tenantName?: string | null;
    itemName?: string | null;
    itemQty?: number | null;
    unitQty?: string | null;
    cargo?: string | null;
    shipment?: string | null;
    remarks?: string | null;
    vesselType?: string | null;
    tenant?: TenantShortDto;
};

export type VesselListRequestDto = {
    maxResultCount?: number;
    skipCount?: number;
    sorting?: string | null;
    vesselType?: string | null;
    fromDate?: string | null;
    toDate?: string | null;
    vesselName?: string | null;
    voyage?: string | null;
    tenantName?: string | null;
    filterGroup?: FilterGroup;
};

export type WindowsTimeZone = {
    timeZoneId?: string | null;
};

export type ZoneDetailCreateUpdateDto = {
    docEntry: number;
    bcTypeKey?: number | null;
    tenantKey?: number | null;
    bp?: string | null;
    cargo?: string | null;
    weight?: number | null;
    blNo?: string | null;
    blDate?: string | null;
    ajuNo?: string | null;
    regNo?: string | null;
    regDate?: string | null;
    sppbNo?: string | null;
    sppbDate?: string | null;
    sppdNo?: string | null;
    sppdDate?: string | null;
    shipment?: string | null;
    remarks?: string | null;
    createdBy: string;
    createdId?: number | null;
    updatedBy?: string | null;
    updatedId?: number | null;
    color?: string | null;
    sapKbEntry?: number | null;
    noBl?: string | null;
    dateBl?: string | null;
    noInv?: string | null;
    dateInv?: string | null;
    shipmentNo?: string | null;
    ebillingDate?: string | null;
    skep?: string | null;
    skepDate?: string | null;
    pibNo?: string | null;
    pibDate?: string | null;
    vesselArrive?: string | null;
    expiredDate?: string | null;
    item?: string | null;
    qty?: number | null;
    amount?: number | null;
    status?: string | null;
    siteStatus?: string | null;
    docNum?: number | null;
    flags?: string | null;
    oceanFreight?: string | null;
    currency?: string | null;
    ocean?: string | null;
    cbmb?: string | null;
    freightValue?: number | null;
    attachment?: string | null;
    postDate?: string | null;
    docType: string;
    isScan: string;
    isOriginal: string;
    isSend: string;
    isFeOri: string;
    isFeSend: string;
    secretKey?: string | null;
    ppjk?: number | null;
    ppjkcodeTemp?: string | null;
    portOfLoading?: string | null;
    emailToPpjk?: string | null;
    letterNo?: string | null;
    itemName?: string | null;
    itemQty?: number | null;
    unitQty?: string | null;
    grossWeight?: number | null;
    unitWeight?: string | null;
    matchKey?: string | null;
    bpnum?: number | null;
    cargoNum?: number | null;
    lineNum?: number | null;
    isChange: string;
    deleted: string;
    sppbUpdateDate?: string | null;
    sppbNoUpdate?: string | null;
    sppbDateUpdate?: string | null;
    sppdNoUpdate?: string | null;
    sppdDateUpdate?: string | null;
    deleteBy?: string | null;
    eBillingNo?: string | null;
    contractNo?: string | null;
    openDate?: string | null;
    updateDate?: string | null;
    internalCode?: string | null;
    contractDate?: string | null;
    regType?: string | null;
    id?: number | null;
    cbm?: number | null;
    notification?: string | null;
    sppbstatus?: string | null;
    agent?: number | null;
    billingId?: number | null;
    insuranceCurrency?: string | null;
    insuranceValue?: number | null;
    destinationPortId?: number | null;
    netWeight?: number | null;
    unitPrice?: number | null;
    totalInv?: number | null;
    qtyEstimate?: number | null;
    priceEstimate?: number | null;
    billingType?: string | null;
    chargeTo?: string | null;
    qtyRevised?: number | null;
    priceRevised?: number | null;
    noNota?: string | null;
    totalEstimate?: number | null;
    totalRevised?: number | null;
    serialNumber?: string | null;
    serialNumber1?: string | null;
    serialNumber2?: string | null;
    serialNumber3?: string | null;
    serialNumber4?: string | null;
    serialNumber5?: string | null;
    serialNumber6?: string | null;
    isParent?: string | null;
    grtVessel?: number | null;
    npwpBp?: string | null;
    esignDecimal?: number | null;
    cargoId?: number | null;
    bargeId?: number | null;
    voyage?: string | null;
    vesselName?: string | null;
    processName?: string | null;
    rate?: number | null;
    bm?: number | null;
    ppn?: number | null;
    pph?: number | null;
    bmad?: number | null;
    bmtp?: number | null;
    formType?: string | null;
    billingDate?: string | null;
    isUrgent: string;
    surveyorId?: number | null;
    surveyorName?: string | null;
    emailToBcDate?: string | null;
    container?: number | null;
    exportClassificationId?: string | null;
    warehouseId?: number | null;
    increaseValue?: number | null;
    decreaseValue?: number | null;
    increaseValuePpn?: number | null;
    decreaseValuePpn?: number | null;
    increaseValuePph?: number | null;
    decreaseValuePph?: number | null;
    repairLocation?: string | null;
    representativeId?: number | null;
    invoiceDetailId?: number | null;
    costOfRepair?: number | null;
    itemCategoryCode?: string | null;
    itemCategoryDescription?: string | null;
    sapBillingStatus?: string | null;
};

export type ZoneDetailDto = {
    id?: string;
    docEntry?: number;
    bcTypeKey?: number | null;
    tenantKey?: number | null;
    bp?: string | null;
    cargo?: string | null;
    weight?: number | null;
    blNo?: string | null;
    blDate?: string | null;
    ajuNo?: string | null;
    regNo?: string | null;
    regDate?: string | null;
    sppbNo?: string | null;
    sppbDate?: string | null;
    sppdNo?: string | null;
    sppdDate?: string | null;
    shipment?: string | null;
    remarks?: string | null;
    createdBy?: string | null;
    createdAt?: string | null;
    updatedAt?: string | null;
    createdId?: number | null;
    updatedBy?: string | null;
    updatedId?: number | null;
    color?: string | null;
    sapKbEntry?: number | null;
    noBl?: string | null;
    dateBl?: string | null;
    noInv?: string | null;
    dateInv?: string | null;
    shipmentNo?: string | null;
    ebillingDate?: string | null;
    skep?: string | null;
    skepDate?: string | null;
    pibNo?: string | null;
    pibDate?: string | null;
    vesselArrive?: string | null;
    expiredDate?: string | null;
    item?: string | null;
    qty?: number | null;
    amount?: number | null;
    status?: string | null;
    siteStatus?: string | null;
    docNum?: number | null;
    flags?: string | null;
    oceanFreight?: string | null;
    currency?: string | null;
    ocean?: string | null;
    cbmb?: string | null;
    freightValue?: number | null;
    attachment?: string | null;
    postDate?: string | null;
    docType?: string | null;
    isScan?: string | null;
    isOriginal?: string | null;
    isSend?: string | null;
    isFeOri?: string | null;
    isFeSend?: string | null;
    secretKey?: string | null;
    ppjk?: number | null;
    ppjkcodeTemp?: string | null;
    portOfLoading?: string | null;
    emailToPpjk?: string | null;
    letterNo?: string | null;
    itemName?: string | null;
    itemQty?: number | null;
    unitQty?: string | null;
    grossWeight?: number | null;
    unitWeight?: string | null;
    matchKey?: string | null;
    bpnum?: number | null;
    cargoNum?: number | null;
    lineNum?: number | null;
    isChange?: string | null;
    deleted?: string | null;
    sppbUpdateDate?: string | null;
    sppbNoUpdate?: string | null;
    sppbDateUpdate?: string | null;
    sppdNoUpdate?: string | null;
    sppdDateUpdate?: string | null;
    deletedAt?: string | null;
    deleteBy?: string | null;
    eBillingNo?: string | null;
    contractNo?: string | null;
    openDate?: string | null;
    updateDate?: string | null;
    internalCode?: string | null;
    contractDate?: string | null;
    regType?: string | null;
    cbm?: number | null;
    notification?: string | null;
    sppbstatus?: string | null;
    agent?: number | null;
    billingId?: number | null;
    insuranceCurrency?: string | null;
    insuranceValue?: number | null;
    destinationPortId?: number | null;
    netWeight?: number | null;
    unitPrice?: number | null;
    totalInv?: number | null;
    qtyEstimate?: number | null;
    priceEstimate?: number | null;
    billingType?: string | null;
    chargeTo?: string | null;
    qtyRevised?: number | null;
    priceRevised?: number | null;
    noNota?: string | null;
    totalEstimate?: number | null;
    totalRevised?: number | null;
    serialNumber?: string | null;
    serialNumber1?: string | null;
    serialNumber2?: string | null;
    serialNumber3?: string | null;
    serialNumber4?: string | null;
    serialNumber5?: string | null;
    serialNumber6?: string | null;
    isParent?: string | null;
    grtVessel?: number | null;
    npwpBp?: string | null;
    esignDecimal?: number | null;
    cargoId?: number | null;
    bargeId?: number | null;
    voyage?: string | null;
    vesselName?: string | null;
    processName?: string | null;
    rate?: number | null;
    bm?: number | null;
    ppn?: number | null;
    pph?: number | null;
    bmad?: number | null;
    bmtp?: number | null;
    formType?: string | null;
    billingDate?: string | null;
    isUrgent?: string | null;
    surveyorId?: number | null;
    surveyorName?: string | null;
    emailToBcDate?: string | null;
    container?: number | null;
    exportClassificationId?: string | null;
    warehouseId?: number | null;
    increaseValue?: number | null;
    decreaseValue?: number | null;
    increaseValuePpn?: number | null;
    decreaseValuePpn?: number | null;
    increaseValuePph?: number | null;
    decreaseValuePph?: number | null;
    repairLocation?: string | null;
    representativeId?: number | null;
    invoiceDetailId?: number | null;
    costOfRepair?: number | null;
    itemCategoryCode?: string | null;
    itemCategoryDescription?: string | null;
    sapBillingStatus?: string | null;
};

export type GetApiAbpApplicationConfigurationData = {
    body?: never;
    path?: never;
    query?: {
        IncludeLocalizationResources?: boolean;
    };
    url: '/api/abp/application-configuration';
};

export type GetApiAbpApplicationConfigurationErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiAbpApplicationConfigurationError = GetApiAbpApplicationConfigurationErrors[keyof GetApiAbpApplicationConfigurationErrors];

export type GetApiAbpApplicationConfigurationResponses = {
    /**
     * OK
     */
    200: ApplicationConfigurationDto;
};

export type GetApiAbpApplicationConfigurationResponse = GetApiAbpApplicationConfigurationResponses[keyof GetApiAbpApplicationConfigurationResponses];

export type GetApiAbpMultiTenancyTenantsByNameByNameData = {
    body?: never;
    path: {
        name: string;
    };
    query?: never;
    url: '/api/abp/multi-tenancy/tenants/by-name/{name}';
};

export type GetApiAbpMultiTenancyTenantsByNameByNameErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiAbpMultiTenancyTenantsByNameByNameError = GetApiAbpMultiTenancyTenantsByNameByNameErrors[keyof GetApiAbpMultiTenancyTenantsByNameByNameErrors];

export type GetApiAbpMultiTenancyTenantsByNameByNameResponses = {
    /**
     * OK
     */
    200: FindTenantResultDto;
};

export type GetApiAbpMultiTenancyTenantsByNameByNameResponse = GetApiAbpMultiTenancyTenantsByNameByNameResponses[keyof GetApiAbpMultiTenancyTenantsByNameByNameResponses];

export type GetApiAbpMultiTenancyTenantsByIdByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/abp/multi-tenancy/tenants/by-id/{id}';
};

export type GetApiAbpMultiTenancyTenantsByIdByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiAbpMultiTenancyTenantsByIdByIdError = GetApiAbpMultiTenancyTenantsByIdByIdErrors[keyof GetApiAbpMultiTenancyTenantsByIdByIdErrors];

export type GetApiAbpMultiTenancyTenantsByIdByIdResponses = {
    /**
     * OK
     */
    200: FindTenantResultDto;
};

export type GetApiAbpMultiTenancyTenantsByIdByIdResponse = GetApiAbpMultiTenancyTenantsByIdByIdResponses[keyof GetApiAbpMultiTenancyTenantsByIdByIdResponses];

export type PostApiAccountRegisterData = {
    body?: RegisterDto;
    path?: never;
    query?: never;
    url: '/api/account/register';
};

export type PostApiAccountRegisterErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiAccountRegisterError = PostApiAccountRegisterErrors[keyof PostApiAccountRegisterErrors];

export type PostApiAccountRegisterResponses = {
    /**
     * OK
     */
    200: IdentityUserDto;
};

export type PostApiAccountRegisterResponse = PostApiAccountRegisterResponses[keyof PostApiAccountRegisterResponses];

export type PostApiAccountSendPasswordResetCodeData = {
    body?: SendPasswordResetCodeDto;
    path?: never;
    query?: never;
    url: '/api/account/send-password-reset-code';
};

export type PostApiAccountSendPasswordResetCodeErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiAccountSendPasswordResetCodeError = PostApiAccountSendPasswordResetCodeErrors[keyof PostApiAccountSendPasswordResetCodeErrors];

export type PostApiAccountSendPasswordResetCodeResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type PostApiAccountVerifyPasswordResetTokenData = {
    body?: VerifyPasswordResetTokenInput;
    path?: never;
    query?: never;
    url: '/api/account/verify-password-reset-token';
};

export type PostApiAccountVerifyPasswordResetTokenErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiAccountVerifyPasswordResetTokenError = PostApiAccountVerifyPasswordResetTokenErrors[keyof PostApiAccountVerifyPasswordResetTokenErrors];

export type PostApiAccountVerifyPasswordResetTokenResponses = {
    /**
     * OK
     */
    200: boolean;
};

export type PostApiAccountVerifyPasswordResetTokenResponse = PostApiAccountVerifyPasswordResetTokenResponses[keyof PostApiAccountVerifyPasswordResetTokenResponses];

export type PostApiAccountResetPasswordData = {
    body?: ResetPasswordDto;
    path?: never;
    query?: never;
    url: '/api/account/reset-password';
};

export type PostApiAccountResetPasswordErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiAccountResetPasswordError = PostApiAccountResetPasswordErrors[keyof PostApiAccountResetPasswordErrors];

export type PostApiAccountResetPasswordResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type PostApiMasterAgentFilterListData = {
    body?: never;
    path?: never;
    query: {
        Page?: number;
        Sort?: Array<SortInfo>;
        'FilterGroup.Operator': LogicalOperator;
        'FilterGroup.Conditions': Array<FilterCondition>;
        SkipCount?: number;
        MaxResultCount?: number;
        Sorting?: string;
    };
    url: '/api/master/agent/filter-list';
};

export type PostApiMasterAgentFilterListResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfAgentDto;
};

export type PostApiMasterAgentFilterListResponse = PostApiMasterAgentFilterListResponses[keyof PostApiMasterAgentFilterListResponses];

export type GetApiMasterAgentData = {
    body?: never;
    path?: never;
    query?: {
        Sorting?: string;
        SkipCount?: number;
        MaxResultCount?: number;
    };
    url: '/api/master/agent';
};

export type GetApiMasterAgentResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfAgentDto;
};

export type GetApiMasterAgentResponse = GetApiMasterAgentResponses[keyof GetApiMasterAgentResponses];

export type PostApiMasterAgentData = {
    body?: never;
    path?: never;
    query: {
        Name: string;
        Status: string;
        Type?: string;
        CreatedBy?: number;
        UpdatedBy?: number;
        NpwpNo?: string;
        BdmSapcode?: string;
        TaxCode?: string;
        AddressNpwp?: string;
        Address?: string;
        SapcodeS4?: string;
    };
    url: '/api/master/agent';
};

export type PostApiMasterAgentResponses = {
    /**
     * OK
     */
    200: AgentDto;
};

export type PostApiMasterAgentResponse = PostApiMasterAgentResponses[keyof PostApiMasterAgentResponses];

export type DeleteApiMasterAgentByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/master/agent/{id}';
};

export type DeleteApiMasterAgentByIdResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiMasterAgentByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/master/agent/{id}';
};

export type GetApiMasterAgentByIdResponses = {
    /**
     * OK
     */
    200: AgentDto;
};

export type GetApiMasterAgentByIdResponse = GetApiMasterAgentByIdResponses[keyof GetApiMasterAgentByIdResponses];

export type PutApiMasterAgentByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query: {
        Name: string;
        Status: string;
        Type?: string;
        CreatedBy?: number;
        UpdatedBy?: number;
        NpwpNo?: string;
        BdmSapcode?: string;
        TaxCode?: string;
        AddressNpwp?: string;
        Address?: string;
        SapcodeS4?: string;
    };
    url: '/api/master/agent/{id}';
};

export type PutApiMasterAgentByIdResponses = {
    /**
     * OK
     */
    200: AgentDto;
};

export type PutApiMasterAgentByIdResponse = PutApiMasterAgentByIdResponses[keyof PutApiMasterAgentByIdResponses];

export type GetApiAuthLogoutData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/Auth/logout';
};

export type GetApiAuthLogoutResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiEkbBcTypeData = {
    body?: never;
    path?: never;
    query?: {
        Sorting?: string;
        SkipCount?: number;
        MaxResultCount?: number;
    };
    url: '/api/ekb/bc-type';
};

export type GetApiEkbBcTypeErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbBcTypeError = GetApiEkbBcTypeErrors[keyof GetApiEkbBcTypeErrors];

export type GetApiEkbBcTypeResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfBcTypeDto;
};

export type GetApiEkbBcTypeResponse = GetApiEkbBcTypeResponses[keyof GetApiEkbBcTypeResponses];

export type PostApiEkbBcTypeData = {
    body?: BcTypeCreateUpdateDto;
    path?: never;
    query?: never;
    url: '/api/ekb/bc-type';
};

export type PostApiEkbBcTypeErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbBcTypeError = PostApiEkbBcTypeErrors[keyof PostApiEkbBcTypeErrors];

export type PostApiEkbBcTypeResponses = {
    /**
     * OK
     */
    200: BcTypeDto;
};

export type PostApiEkbBcTypeResponse = PostApiEkbBcTypeResponses[keyof PostApiEkbBcTypeResponses];

export type DeleteApiEkbBcTypeByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/bc-type/{id}';
};

export type DeleteApiEkbBcTypeByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type DeleteApiEkbBcTypeByIdError = DeleteApiEkbBcTypeByIdErrors[keyof DeleteApiEkbBcTypeByIdErrors];

export type DeleteApiEkbBcTypeByIdResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiEkbBcTypeByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/bc-type/{id}';
};

export type GetApiEkbBcTypeByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbBcTypeByIdError = GetApiEkbBcTypeByIdErrors[keyof GetApiEkbBcTypeByIdErrors];

export type GetApiEkbBcTypeByIdResponses = {
    /**
     * OK
     */
    200: BcTypeDto;
};

export type GetApiEkbBcTypeByIdResponse = GetApiEkbBcTypeByIdResponses[keyof GetApiEkbBcTypeByIdResponses];

export type PutApiEkbBcTypeByIdData = {
    body?: BcTypeCreateUpdateDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/bc-type/{id}';
};

export type PutApiEkbBcTypeByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PutApiEkbBcTypeByIdError = PutApiEkbBcTypeByIdErrors[keyof PutApiEkbBcTypeByIdErrors];

export type PutApiEkbBcTypeByIdResponses = {
    /**
     * OK
     */
    200: BcTypeDto;
};

export type PutApiEkbBcTypeByIdResponse = PutApiEkbBcTypeByIdResponses[keyof PutApiEkbBcTypeByIdResponses];

export type PostApiEkbBcTypeFilterListData = {
    body?: QueryParametersDto;
    path?: never;
    query?: never;
    url: '/api/ekb/bc-type/filter-list';
};

export type PostApiEkbBcTypeFilterListErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbBcTypeFilterListError = PostApiEkbBcTypeFilterListErrors[keyof PostApiEkbBcTypeFilterListErrors];

export type PostApiEkbBcTypeFilterListResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfBcTypeDto;
};

export type PostApiEkbBcTypeFilterListResponse = PostApiEkbBcTypeFilterListResponses[keyof PostApiEkbBcTypeFilterListResponses];

export type PostApiEkbBoundedZoneListData = {
    body?: QueryParametersDto;
    path?: never;
    query?: never;
    url: '/api/ekb/bounded-zone/list';
};

export type PostApiEkbBoundedZoneListErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbBoundedZoneListError = PostApiEkbBoundedZoneListErrors[keyof PostApiEkbBoundedZoneListErrors];

export type PostApiEkbBoundedZoneListResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfZoneDetailDto;
};

export type PostApiEkbBoundedZoneListResponse = PostApiEkbBoundedZoneListResponses[keyof PostApiEkbBoundedZoneListResponses];

export type GetApiEkbBoundedZoneData = {
    body?: never;
    path?: never;
    query?: {
        Sorting?: string;
        SkipCount?: number;
        MaxResultCount?: number;
    };
    url: '/api/ekb/bounded-zone';
};

export type GetApiEkbBoundedZoneErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbBoundedZoneError = GetApiEkbBoundedZoneErrors[keyof GetApiEkbBoundedZoneErrors];

export type GetApiEkbBoundedZoneResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfZoneDetailDto;
};

export type GetApiEkbBoundedZoneResponse = GetApiEkbBoundedZoneResponses[keyof GetApiEkbBoundedZoneResponses];

export type PostApiEkbBoundedZoneData = {
    body?: ZoneDetailCreateUpdateDto;
    path?: never;
    query?: never;
    url: '/api/ekb/bounded-zone';
};

export type PostApiEkbBoundedZoneErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbBoundedZoneError = PostApiEkbBoundedZoneErrors[keyof PostApiEkbBoundedZoneErrors];

export type PostApiEkbBoundedZoneResponses = {
    /**
     * OK
     */
    200: ZoneDetailDto;
};

export type PostApiEkbBoundedZoneResponse = PostApiEkbBoundedZoneResponses[keyof PostApiEkbBoundedZoneResponses];

export type DeleteApiEkbBoundedZoneByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/bounded-zone/{id}';
};

export type DeleteApiEkbBoundedZoneByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type DeleteApiEkbBoundedZoneByIdError = DeleteApiEkbBoundedZoneByIdErrors[keyof DeleteApiEkbBoundedZoneByIdErrors];

export type DeleteApiEkbBoundedZoneByIdResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiEkbBoundedZoneByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/bounded-zone/{id}';
};

export type GetApiEkbBoundedZoneByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbBoundedZoneByIdError = GetApiEkbBoundedZoneByIdErrors[keyof GetApiEkbBoundedZoneByIdErrors];

export type GetApiEkbBoundedZoneByIdResponses = {
    /**
     * OK
     */
    200: ZoneDetailDto;
};

export type GetApiEkbBoundedZoneByIdResponse = GetApiEkbBoundedZoneByIdResponses[keyof GetApiEkbBoundedZoneByIdResponses];

export type PutApiEkbBoundedZoneByIdData = {
    body?: ZoneDetailCreateUpdateDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/bounded-zone/{id}';
};

export type PutApiEkbBoundedZoneByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PutApiEkbBoundedZoneByIdError = PutApiEkbBoundedZoneByIdErrors[keyof PutApiEkbBoundedZoneByIdErrors];

export type PutApiEkbBoundedZoneByIdResponses = {
    /**
     * OK
     */
    200: ZoneDetailDto;
};

export type PutApiEkbBoundedZoneByIdResponse = PutApiEkbBoundedZoneByIdResponses[keyof PutApiEkbBoundedZoneByIdResponses];

export type PostApiEkbBoundedZoneFilterListData = {
    body?: QueryParametersDto;
    path?: never;
    query?: never;
    url: '/api/ekb/bounded-zone/filter-list';
};

export type PostApiEkbBoundedZoneFilterListErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbBoundedZoneFilterListError = PostApiEkbBoundedZoneFilterListErrors[keyof PostApiEkbBoundedZoneFilterListErrors];

export type PostApiEkbBoundedZoneFilterListResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfZoneDetailDto;
};

export type PostApiEkbBoundedZoneFilterListResponse = PostApiEkbBoundedZoneFilterListResponses[keyof PostApiEkbBoundedZoneFilterListResponses];

export type GetApiEkbBusinessPartnerData = {
    body?: never;
    path?: never;
    query?: {
        Sorting?: string;
        SkipCount?: number;
        MaxResultCount?: number;
    };
    url: '/api/ekb/business-partner';
};

export type GetApiEkbBusinessPartnerErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbBusinessPartnerError = GetApiEkbBusinessPartnerErrors[keyof GetApiEkbBusinessPartnerErrors];

export type GetApiEkbBusinessPartnerResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfBusinessPartnerDto;
};

export type GetApiEkbBusinessPartnerResponse = GetApiEkbBusinessPartnerResponses[keyof GetApiEkbBusinessPartnerResponses];

export type PostApiEkbBusinessPartnerData = {
    body?: BusinessPartnerCreateUpdateDto;
    path?: never;
    query?: never;
    url: '/api/ekb/business-partner';
};

export type PostApiEkbBusinessPartnerErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbBusinessPartnerError = PostApiEkbBusinessPartnerErrors[keyof PostApiEkbBusinessPartnerErrors];

export type PostApiEkbBusinessPartnerResponses = {
    /**
     * OK
     */
    200: BusinessPartnerDto;
};

export type PostApiEkbBusinessPartnerResponse = PostApiEkbBusinessPartnerResponses[keyof PostApiEkbBusinessPartnerResponses];

export type DeleteApiEkbBusinessPartnerByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/business-partner/{id}';
};

export type DeleteApiEkbBusinessPartnerByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type DeleteApiEkbBusinessPartnerByIdError = DeleteApiEkbBusinessPartnerByIdErrors[keyof DeleteApiEkbBusinessPartnerByIdErrors];

export type DeleteApiEkbBusinessPartnerByIdResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiEkbBusinessPartnerByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/business-partner/{id}';
};

export type GetApiEkbBusinessPartnerByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbBusinessPartnerByIdError = GetApiEkbBusinessPartnerByIdErrors[keyof GetApiEkbBusinessPartnerByIdErrors];

export type GetApiEkbBusinessPartnerByIdResponses = {
    /**
     * OK
     */
    200: BusinessPartnerDto;
};

export type GetApiEkbBusinessPartnerByIdResponse = GetApiEkbBusinessPartnerByIdResponses[keyof GetApiEkbBusinessPartnerByIdResponses];

export type PutApiEkbBusinessPartnerByIdData = {
    body?: BusinessPartnerCreateUpdateDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/business-partner/{id}';
};

export type PutApiEkbBusinessPartnerByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PutApiEkbBusinessPartnerByIdError = PutApiEkbBusinessPartnerByIdErrors[keyof PutApiEkbBusinessPartnerByIdErrors];

export type PutApiEkbBusinessPartnerByIdResponses = {
    /**
     * OK
     */
    200: BusinessPartnerDto;
};

export type PutApiEkbBusinessPartnerByIdResponse = PutApiEkbBusinessPartnerByIdResponses[keyof PutApiEkbBusinessPartnerByIdResponses];

export type PostApiEkbBusinessPartnerFilterListData = {
    body?: QueryParametersDto;
    path?: never;
    query?: never;
    url: '/api/ekb/business-partner/filter-list';
};

export type PostApiEkbBusinessPartnerFilterListErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbBusinessPartnerFilterListError = PostApiEkbBusinessPartnerFilterListErrors[keyof PostApiEkbBusinessPartnerFilterListErrors];

export type PostApiEkbBusinessPartnerFilterListResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfBusinessPartnerDto;
};

export type PostApiEkbBusinessPartnerFilterListResponse = PostApiEkbBusinessPartnerFilterListResponses[keyof PostApiEkbBusinessPartnerFilterListResponses];

export type GetApiEkbCargoData = {
    body?: never;
    path?: never;
    query?: {
        Sorting?: string;
        SkipCount?: number;
        MaxResultCount?: number;
    };
    url: '/api/ekb/cargo';
};

export type GetApiEkbCargoErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbCargoError = GetApiEkbCargoErrors[keyof GetApiEkbCargoErrors];

export type GetApiEkbCargoResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfCargoDto;
};

export type GetApiEkbCargoResponse = GetApiEkbCargoResponses[keyof GetApiEkbCargoResponses];

export type PostApiEkbCargoData = {
    body?: CargoCreateUpdateDto;
    path?: never;
    query?: never;
    url: '/api/ekb/cargo';
};

export type PostApiEkbCargoErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbCargoError = PostApiEkbCargoErrors[keyof PostApiEkbCargoErrors];

export type PostApiEkbCargoResponses = {
    /**
     * OK
     */
    200: CargoDto;
};

export type PostApiEkbCargoResponse = PostApiEkbCargoResponses[keyof PostApiEkbCargoResponses];

export type DeleteApiEkbCargoByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/cargo/{id}';
};

export type DeleteApiEkbCargoByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type DeleteApiEkbCargoByIdError = DeleteApiEkbCargoByIdErrors[keyof DeleteApiEkbCargoByIdErrors];

export type DeleteApiEkbCargoByIdResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiEkbCargoByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/cargo/{id}';
};

export type GetApiEkbCargoByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbCargoByIdError = GetApiEkbCargoByIdErrors[keyof GetApiEkbCargoByIdErrors];

export type GetApiEkbCargoByIdResponses = {
    /**
     * OK
     */
    200: CargoDto;
};

export type GetApiEkbCargoByIdResponse = GetApiEkbCargoByIdResponses[keyof GetApiEkbCargoByIdResponses];

export type PutApiEkbCargoByIdData = {
    body?: CargoCreateUpdateDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/cargo/{id}';
};

export type PutApiEkbCargoByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PutApiEkbCargoByIdError = PutApiEkbCargoByIdErrors[keyof PutApiEkbCargoByIdErrors];

export type PutApiEkbCargoByIdResponses = {
    /**
     * OK
     */
    200: CargoDto;
};

export type PutApiEkbCargoByIdResponse = PutApiEkbCargoByIdResponses[keyof PutApiEkbCargoByIdResponses];

export type PostApiEkbCargoFilterListData = {
    body?: QueryParametersDto;
    path?: never;
    query?: never;
    url: '/api/ekb/cargo/filter-list';
};

export type PostApiEkbCargoFilterListErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbCargoFilterListError = PostApiEkbCargoFilterListErrors[keyof PostApiEkbCargoFilterListErrors];

export type PostApiEkbCargoFilterListResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfCargoDto;
};

export type PostApiEkbCargoFilterListResponse = PostApiEkbCargoFilterListResponses[keyof PostApiEkbCargoFilterListResponses];

export type GetApiEkbDestinationPortData = {
    body?: never;
    path?: never;
    query?: {
        Sorting?: string;
        SkipCount?: number;
        MaxResultCount?: number;
    };
    url: '/api/ekb/destination-port';
};

export type GetApiEkbDestinationPortErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbDestinationPortError = GetApiEkbDestinationPortErrors[keyof GetApiEkbDestinationPortErrors];

export type GetApiEkbDestinationPortResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfDestinationPortDto;
};

export type GetApiEkbDestinationPortResponse = GetApiEkbDestinationPortResponses[keyof GetApiEkbDestinationPortResponses];

export type PostApiEkbDestinationPortData = {
    body?: DestinationPortCreateUpdateDto;
    path?: never;
    query?: never;
    url: '/api/ekb/destination-port';
};

export type PostApiEkbDestinationPortErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbDestinationPortError = PostApiEkbDestinationPortErrors[keyof PostApiEkbDestinationPortErrors];

export type PostApiEkbDestinationPortResponses = {
    /**
     * OK
     */
    200: DestinationPortDto;
};

export type PostApiEkbDestinationPortResponse = PostApiEkbDestinationPortResponses[keyof PostApiEkbDestinationPortResponses];

export type DeleteApiEkbDestinationPortByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/destination-port/{id}';
};

export type DeleteApiEkbDestinationPortByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type DeleteApiEkbDestinationPortByIdError = DeleteApiEkbDestinationPortByIdErrors[keyof DeleteApiEkbDestinationPortByIdErrors];

export type DeleteApiEkbDestinationPortByIdResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiEkbDestinationPortByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/destination-port/{id}';
};

export type GetApiEkbDestinationPortByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbDestinationPortByIdError = GetApiEkbDestinationPortByIdErrors[keyof GetApiEkbDestinationPortByIdErrors];

export type GetApiEkbDestinationPortByIdResponses = {
    /**
     * OK
     */
    200: DestinationPortDto;
};

export type GetApiEkbDestinationPortByIdResponse = GetApiEkbDestinationPortByIdResponses[keyof GetApiEkbDestinationPortByIdResponses];

export type PutApiEkbDestinationPortByIdData = {
    body?: DestinationPortCreateUpdateDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/destination-port/{id}';
};

export type PutApiEkbDestinationPortByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PutApiEkbDestinationPortByIdError = PutApiEkbDestinationPortByIdErrors[keyof PutApiEkbDestinationPortByIdErrors];

export type PutApiEkbDestinationPortByIdResponses = {
    /**
     * OK
     */
    200: DestinationPortDto;
};

export type PutApiEkbDestinationPortByIdResponse = PutApiEkbDestinationPortByIdResponses[keyof PutApiEkbDestinationPortByIdResponses];

export type PostApiEkbDestinationPortFilterListData = {
    body?: QueryParametersDto;
    path?: never;
    query?: never;
    url: '/api/ekb/destination-port/filter-list';
};

export type PostApiEkbDestinationPortFilterListErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbDestinationPortFilterListError = PostApiEkbDestinationPortFilterListErrors[keyof PostApiEkbDestinationPortFilterListErrors];

export type PostApiEkbDestinationPortFilterListResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfDestinationPortDto;
};

export type PostApiEkbDestinationPortFilterListResponse = PostApiEkbDestinationPortFilterListResponses[keyof PostApiEkbDestinationPortFilterListResponses];

export type PostApiAccountDynamicClaimsRefreshData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/account/dynamic-claims/refresh';
};

export type PostApiAccountDynamicClaimsRefreshErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiAccountDynamicClaimsRefreshError = PostApiAccountDynamicClaimsRefreshErrors[keyof PostApiAccountDynamicClaimsRefreshErrors];

export type PostApiAccountDynamicClaimsRefreshResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiHealthKubernetesData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/health/kubernetes';
};

export type GetApiHealthKubernetesResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiEkbItemClassificationData = {
    body?: never;
    path?: never;
    query?: {
        Sorting?: string;
        SkipCount?: number;
        MaxResultCount?: number;
    };
    url: '/api/ekb/item-classification';
};

export type GetApiEkbItemClassificationErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbItemClassificationError = GetApiEkbItemClassificationErrors[keyof GetApiEkbItemClassificationErrors];

export type GetApiEkbItemClassificationResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfItemClassificationDto;
};

export type GetApiEkbItemClassificationResponse = GetApiEkbItemClassificationResponses[keyof GetApiEkbItemClassificationResponses];

export type PostApiEkbItemClassificationData = {
    body?: ItemClassificationCreateUpdateDto;
    path?: never;
    query?: never;
    url: '/api/ekb/item-classification';
};

export type PostApiEkbItemClassificationErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbItemClassificationError = PostApiEkbItemClassificationErrors[keyof PostApiEkbItemClassificationErrors];

export type PostApiEkbItemClassificationResponses = {
    /**
     * OK
     */
    200: ItemClassificationDto;
};

export type PostApiEkbItemClassificationResponse = PostApiEkbItemClassificationResponses[keyof PostApiEkbItemClassificationResponses];

export type DeleteApiEkbItemClassificationByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/item-classification/{id}';
};

export type DeleteApiEkbItemClassificationByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type DeleteApiEkbItemClassificationByIdError = DeleteApiEkbItemClassificationByIdErrors[keyof DeleteApiEkbItemClassificationByIdErrors];

export type DeleteApiEkbItemClassificationByIdResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiEkbItemClassificationByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/item-classification/{id}';
};

export type GetApiEkbItemClassificationByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbItemClassificationByIdError = GetApiEkbItemClassificationByIdErrors[keyof GetApiEkbItemClassificationByIdErrors];

export type GetApiEkbItemClassificationByIdResponses = {
    /**
     * OK
     */
    200: ItemClassificationDto;
};

export type GetApiEkbItemClassificationByIdResponse = GetApiEkbItemClassificationByIdResponses[keyof GetApiEkbItemClassificationByIdResponses];

export type PutApiEkbItemClassificationByIdData = {
    body?: ItemClassificationCreateUpdateDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/item-classification/{id}';
};

export type PutApiEkbItemClassificationByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PutApiEkbItemClassificationByIdError = PutApiEkbItemClassificationByIdErrors[keyof PutApiEkbItemClassificationByIdErrors];

export type PutApiEkbItemClassificationByIdResponses = {
    /**
     * OK
     */
    200: ItemClassificationDto;
};

export type PutApiEkbItemClassificationByIdResponse = PutApiEkbItemClassificationByIdResponses[keyof PutApiEkbItemClassificationByIdResponses];

export type PostApiEkbItemClassificationFilterListData = {
    body?: QueryParametersDto;
    path?: never;
    query?: never;
    url: '/api/ekb/item-classification/filter-list';
};

export type PostApiEkbItemClassificationFilterListErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbItemClassificationFilterListError = PostApiEkbItemClassificationFilterListErrors[keyof PostApiEkbItemClassificationFilterListErrors];

export type PostApiEkbItemClassificationFilterListResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfItemClassificationDto;
};

export type PostApiEkbItemClassificationFilterListResponse = PostApiEkbItemClassificationFilterListResponses[keyof PostApiEkbItemClassificationFilterListResponses];

export type GetApiEkbJettyData = {
    body?: never;
    path?: never;
    query?: {
        Sorting?: string;
        SkipCount?: number;
        MaxResultCount?: number;
    };
    url: '/api/ekb/jetty';
};

export type GetApiEkbJettyErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbJettyError = GetApiEkbJettyErrors[keyof GetApiEkbJettyErrors];

export type GetApiEkbJettyResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfJettyDto;
};

export type GetApiEkbJettyResponse = GetApiEkbJettyResponses[keyof GetApiEkbJettyResponses];

export type PostApiEkbJettyData = {
    body?: JettyCreateUpdateDto;
    path?: never;
    query?: never;
    url: '/api/ekb/jetty';
};

export type PostApiEkbJettyErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbJettyError = PostApiEkbJettyErrors[keyof PostApiEkbJettyErrors];

export type PostApiEkbJettyResponses = {
    /**
     * OK
     */
    200: JettyDto;
};

export type PostApiEkbJettyResponse = PostApiEkbJettyResponses[keyof PostApiEkbJettyResponses];

export type DeleteApiEkbJettyByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/jetty/{id}';
};

export type DeleteApiEkbJettyByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type DeleteApiEkbJettyByIdError = DeleteApiEkbJettyByIdErrors[keyof DeleteApiEkbJettyByIdErrors];

export type DeleteApiEkbJettyByIdResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiEkbJettyByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/jetty/{id}';
};

export type GetApiEkbJettyByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbJettyByIdError = GetApiEkbJettyByIdErrors[keyof GetApiEkbJettyByIdErrors];

export type GetApiEkbJettyByIdResponses = {
    /**
     * OK
     */
    200: JettyDto;
};

export type GetApiEkbJettyByIdResponse = GetApiEkbJettyByIdResponses[keyof GetApiEkbJettyByIdResponses];

export type PutApiEkbJettyByIdData = {
    body?: JettyCreateUpdateDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/jetty/{id}';
};

export type PutApiEkbJettyByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PutApiEkbJettyByIdError = PutApiEkbJettyByIdErrors[keyof PutApiEkbJettyByIdErrors];

export type PutApiEkbJettyByIdResponses = {
    /**
     * OK
     */
    200: JettyDto;
};

export type PutApiEkbJettyByIdResponse = PutApiEkbJettyByIdResponses[keyof PutApiEkbJettyByIdResponses];

export type PostApiEkbJettyFilterListData = {
    body?: QueryParametersDto;
    path?: never;
    query?: never;
    url: '/api/ekb/jetty/filter-list';
};

export type PostApiEkbJettyFilterListErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbJettyFilterListError = PostApiEkbJettyFilterListErrors[keyof PostApiEkbJettyFilterListErrors];

export type PostApiEkbJettyFilterListResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfJettyDto;
};

export type PostApiEkbJettyFilterListResponse = PostApiEkbJettyFilterListResponses[keyof PostApiEkbJettyFilterListResponses];

export type PostApiAccountLoginData = {
    body?: UserLoginInfo;
    path?: never;
    query?: never;
    url: '/api/account/login';
};

export type PostApiAccountLoginErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiAccountLoginError = PostApiAccountLoginErrors[keyof PostApiAccountLoginErrors];

export type PostApiAccountLoginResponses = {
    /**
     * OK
     */
    200: AbpLoginResult;
};

export type PostApiAccountLoginResponse = PostApiAccountLoginResponses[keyof PostApiAccountLoginResponses];

export type GetApiAccountLogoutData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/account/logout';
};

export type GetApiAccountLogoutErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiAccountLogoutError = GetApiAccountLogoutErrors[keyof GetApiAccountLogoutErrors];

export type GetApiAccountLogoutResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type PostApiAccountCheckPasswordData = {
    body?: UserLoginInfo;
    path?: never;
    query?: never;
    url: '/api/account/check-password';
};

export type PostApiAccountCheckPasswordErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiAccountCheckPasswordError = PostApiAccountCheckPasswordErrors[keyof PostApiAccountCheckPasswordErrors];

export type PostApiAccountCheckPasswordResponses = {
    /**
     * OK
     */
    200: AbpLoginResult;
};

export type PostApiAccountCheckPasswordResponse = PostApiAccountCheckPasswordResponses[keyof PostApiAccountCheckPasswordResponses];

export type GetApiEkbPortOfLoadingData = {
    body?: never;
    path?: never;
    query?: {
        Sorting?: string;
        SkipCount?: number;
        MaxResultCount?: number;
    };
    url: '/api/ekb/port-of-loading';
};

export type GetApiEkbPortOfLoadingErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbPortOfLoadingError = GetApiEkbPortOfLoadingErrors[keyof GetApiEkbPortOfLoadingErrors];

export type GetApiEkbPortOfLoadingResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfPortOfLoadingDto;
};

export type GetApiEkbPortOfLoadingResponse = GetApiEkbPortOfLoadingResponses[keyof GetApiEkbPortOfLoadingResponses];

export type PostApiEkbPortOfLoadingData = {
    body?: PortOfLoadingCreateUpdateDto;
    path?: never;
    query?: never;
    url: '/api/ekb/port-of-loading';
};

export type PostApiEkbPortOfLoadingErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbPortOfLoadingError = PostApiEkbPortOfLoadingErrors[keyof PostApiEkbPortOfLoadingErrors];

export type PostApiEkbPortOfLoadingResponses = {
    /**
     * OK
     */
    200: PortOfLoadingDto;
};

export type PostApiEkbPortOfLoadingResponse = PostApiEkbPortOfLoadingResponses[keyof PostApiEkbPortOfLoadingResponses];

export type DeleteApiEkbPortOfLoadingByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/port-of-loading/{id}';
};

export type DeleteApiEkbPortOfLoadingByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type DeleteApiEkbPortOfLoadingByIdError = DeleteApiEkbPortOfLoadingByIdErrors[keyof DeleteApiEkbPortOfLoadingByIdErrors];

export type DeleteApiEkbPortOfLoadingByIdResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiEkbPortOfLoadingByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/port-of-loading/{id}';
};

export type GetApiEkbPortOfLoadingByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbPortOfLoadingByIdError = GetApiEkbPortOfLoadingByIdErrors[keyof GetApiEkbPortOfLoadingByIdErrors];

export type GetApiEkbPortOfLoadingByIdResponses = {
    /**
     * OK
     */
    200: PortOfLoadingDto;
};

export type GetApiEkbPortOfLoadingByIdResponse = GetApiEkbPortOfLoadingByIdResponses[keyof GetApiEkbPortOfLoadingByIdResponses];

export type PutApiEkbPortOfLoadingByIdData = {
    body?: PortOfLoadingCreateUpdateDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/port-of-loading/{id}';
};

export type PutApiEkbPortOfLoadingByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PutApiEkbPortOfLoadingByIdError = PutApiEkbPortOfLoadingByIdErrors[keyof PutApiEkbPortOfLoadingByIdErrors];

export type PutApiEkbPortOfLoadingByIdResponses = {
    /**
     * OK
     */
    200: PortOfLoadingDto;
};

export type PutApiEkbPortOfLoadingByIdResponse = PutApiEkbPortOfLoadingByIdResponses[keyof PutApiEkbPortOfLoadingByIdResponses];

export type PostApiEkbPortOfLoadingFilterListData = {
    body?: QueryParametersDto;
    path?: never;
    query?: never;
    url: '/api/ekb/port-of-loading/filter-list';
};

export type PostApiEkbPortOfLoadingFilterListErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbPortOfLoadingFilterListError = PostApiEkbPortOfLoadingFilterListErrors[keyof PostApiEkbPortOfLoadingFilterListErrors];

export type PostApiEkbPortOfLoadingFilterListResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfPortOfLoadingDto;
};

export type PostApiEkbPortOfLoadingFilterListResponse = PostApiEkbPortOfLoadingFilterListResponses[keyof PostApiEkbPortOfLoadingFilterListResponses];

export type GetApiEkbPortServiceData = {
    body?: never;
    path?: never;
    query?: {
        Sorting?: string;
        SkipCount?: number;
        MaxResultCount?: number;
    };
    url: '/api/ekb/port-service';
};

export type GetApiEkbPortServiceErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbPortServiceError = GetApiEkbPortServiceErrors[keyof GetApiEkbPortServiceErrors];

export type GetApiEkbPortServiceResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfPortServiceDto;
};

export type GetApiEkbPortServiceResponse = GetApiEkbPortServiceResponses[keyof GetApiEkbPortServiceResponses];

export type PostApiEkbPortServiceData = {
    body?: PortServiceCreateUpdateDto;
    path?: never;
    query?: never;
    url: '/api/ekb/port-service';
};

export type PostApiEkbPortServiceErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbPortServiceError = PostApiEkbPortServiceErrors[keyof PostApiEkbPortServiceErrors];

export type PostApiEkbPortServiceResponses = {
    /**
     * OK
     */
    200: PortServiceDto;
};

export type PostApiEkbPortServiceResponse = PostApiEkbPortServiceResponses[keyof PostApiEkbPortServiceResponses];

export type DeleteApiEkbPortServiceByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/port-service/{id}';
};

export type DeleteApiEkbPortServiceByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type DeleteApiEkbPortServiceByIdError = DeleteApiEkbPortServiceByIdErrors[keyof DeleteApiEkbPortServiceByIdErrors];

export type DeleteApiEkbPortServiceByIdResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiEkbPortServiceByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/port-service/{id}';
};

export type GetApiEkbPortServiceByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbPortServiceByIdError = GetApiEkbPortServiceByIdErrors[keyof GetApiEkbPortServiceByIdErrors];

export type GetApiEkbPortServiceByIdResponses = {
    /**
     * OK
     */
    200: PortServiceDto;
};

export type GetApiEkbPortServiceByIdResponse = GetApiEkbPortServiceByIdResponses[keyof GetApiEkbPortServiceByIdResponses];

export type PutApiEkbPortServiceByIdData = {
    body?: PortServiceCreateUpdateDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/port-service/{id}';
};

export type PutApiEkbPortServiceByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PutApiEkbPortServiceByIdError = PutApiEkbPortServiceByIdErrors[keyof PutApiEkbPortServiceByIdErrors];

export type PutApiEkbPortServiceByIdResponses = {
    /**
     * OK
     */
    200: PortServiceDto;
};

export type PutApiEkbPortServiceByIdResponse = PutApiEkbPortServiceByIdResponses[keyof PutApiEkbPortServiceByIdResponses];

export type PostApiEkbPortServiceFilterListData = {
    body?: QueryParametersDto;
    path?: never;
    query?: never;
    url: '/api/ekb/port-service/filter-list';
};

export type PostApiEkbPortServiceFilterListErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbPortServiceFilterListError = PostApiEkbPortServiceFilterListErrors[keyof PostApiEkbPortServiceFilterListErrors];

export type PostApiEkbPortServiceFilterListResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfPortServiceDto;
};

export type PostApiEkbPortServiceFilterListResponse = PostApiEkbPortServiceFilterListResponses[keyof PostApiEkbPortServiceFilterListResponses];

export type GetApiAccountMyProfileData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/account/my-profile';
};

export type GetApiAccountMyProfileErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiAccountMyProfileError = GetApiAccountMyProfileErrors[keyof GetApiAccountMyProfileErrors];

export type GetApiAccountMyProfileResponses = {
    /**
     * OK
     */
    200: ProfileDto;
};

export type GetApiAccountMyProfileResponse = GetApiAccountMyProfileResponses[keyof GetApiAccountMyProfileResponses];

export type PutApiAccountMyProfileData = {
    body?: UpdateProfileDto;
    path?: never;
    query?: never;
    url: '/api/account/my-profile';
};

export type PutApiAccountMyProfileErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PutApiAccountMyProfileError = PutApiAccountMyProfileErrors[keyof PutApiAccountMyProfileErrors];

export type PutApiAccountMyProfileResponses = {
    /**
     * OK
     */
    200: ProfileDto;
};

export type PutApiAccountMyProfileResponse = PutApiAccountMyProfileResponses[keyof PutApiAccountMyProfileResponses];

export type PostApiAccountMyProfileChangePasswordData = {
    body?: ChangePasswordInput;
    path?: never;
    query?: never;
    url: '/api/account/my-profile/change-password';
};

export type PostApiAccountMyProfileChangePasswordErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiAccountMyProfileChangePasswordError = PostApiAccountMyProfileChangePasswordErrors[keyof PostApiAccountMyProfileChangePasswordErrors];

export type PostApiAccountMyProfileChangePasswordResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiEkbSurveyorData = {
    body?: never;
    path?: never;
    query?: {
        Sorting?: string;
        SkipCount?: number;
        MaxResultCount?: number;
    };
    url: '/api/ekb/surveyor';
};

export type GetApiEkbSurveyorErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbSurveyorError = GetApiEkbSurveyorErrors[keyof GetApiEkbSurveyorErrors];

export type GetApiEkbSurveyorResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfSurveyorDto;
};

export type GetApiEkbSurveyorResponse = GetApiEkbSurveyorResponses[keyof GetApiEkbSurveyorResponses];

export type PostApiEkbSurveyorData = {
    body?: SurveyorCreateUpdateDto;
    path?: never;
    query?: never;
    url: '/api/ekb/surveyor';
};

export type PostApiEkbSurveyorErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbSurveyorError = PostApiEkbSurveyorErrors[keyof PostApiEkbSurveyorErrors];

export type PostApiEkbSurveyorResponses = {
    /**
     * OK
     */
    200: SurveyorDto;
};

export type PostApiEkbSurveyorResponse = PostApiEkbSurveyorResponses[keyof PostApiEkbSurveyorResponses];

export type DeleteApiEkbSurveyorByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/surveyor/{id}';
};

export type DeleteApiEkbSurveyorByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type DeleteApiEkbSurveyorByIdError = DeleteApiEkbSurveyorByIdErrors[keyof DeleteApiEkbSurveyorByIdErrors];

export type DeleteApiEkbSurveyorByIdResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiEkbSurveyorByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/surveyor/{id}';
};

export type GetApiEkbSurveyorByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbSurveyorByIdError = GetApiEkbSurveyorByIdErrors[keyof GetApiEkbSurveyorByIdErrors];

export type GetApiEkbSurveyorByIdResponses = {
    /**
     * OK
     */
    200: SurveyorDto;
};

export type GetApiEkbSurveyorByIdResponse = GetApiEkbSurveyorByIdResponses[keyof GetApiEkbSurveyorByIdResponses];

export type PutApiEkbSurveyorByIdData = {
    body?: SurveyorCreateUpdateDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/surveyor/{id}';
};

export type PutApiEkbSurveyorByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PutApiEkbSurveyorByIdError = PutApiEkbSurveyorByIdErrors[keyof PutApiEkbSurveyorByIdErrors];

export type PutApiEkbSurveyorByIdResponses = {
    /**
     * OK
     */
    200: SurveyorDto;
};

export type PutApiEkbSurveyorByIdResponse = PutApiEkbSurveyorByIdResponses[keyof PutApiEkbSurveyorByIdResponses];

export type PostApiEkbSurveyorFilterListData = {
    body?: QueryParametersDto;
    path?: never;
    query?: never;
    url: '/api/ekb/surveyor/filter-list';
};

export type PostApiEkbSurveyorFilterListErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbSurveyorFilterListError = PostApiEkbSurveyorFilterListErrors[keyof PostApiEkbSurveyorFilterListErrors];

export type PostApiEkbSurveyorFilterListResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfSurveyorDto;
};

export type PostApiEkbSurveyorFilterListResponse = PostApiEkbSurveyorFilterListResponses[keyof PostApiEkbSurveyorFilterListResponses];

export type DeleteApiMultiTenancyTenantsByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/multi-tenancy/tenants/{id}';
};

export type DeleteApiMultiTenancyTenantsByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type DeleteApiMultiTenancyTenantsByIdError = DeleteApiMultiTenancyTenantsByIdErrors[keyof DeleteApiMultiTenancyTenantsByIdErrors];

export type DeleteApiMultiTenancyTenantsByIdResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiMultiTenancyTenantsByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/multi-tenancy/tenants/{id}';
};

export type GetApiMultiTenancyTenantsByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiMultiTenancyTenantsByIdError = GetApiMultiTenancyTenantsByIdErrors[keyof GetApiMultiTenancyTenantsByIdErrors];

export type GetApiMultiTenancyTenantsByIdResponses = {
    /**
     * OK
     */
    200: TenantDto;
};

export type GetApiMultiTenancyTenantsByIdResponse = GetApiMultiTenancyTenantsByIdResponses[keyof GetApiMultiTenancyTenantsByIdResponses];

export type PutApiMultiTenancyTenantsByIdData = {
    body?: TenantUpdateDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/multi-tenancy/tenants/{id}';
};

export type PutApiMultiTenancyTenantsByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PutApiMultiTenancyTenantsByIdError = PutApiMultiTenancyTenantsByIdErrors[keyof PutApiMultiTenancyTenantsByIdErrors];

export type PutApiMultiTenancyTenantsByIdResponses = {
    /**
     * OK
     */
    200: TenantDto;
};

export type PutApiMultiTenancyTenantsByIdResponse = PutApiMultiTenancyTenantsByIdResponses[keyof PutApiMultiTenancyTenantsByIdResponses];

export type GetApiMultiTenancyTenantsData = {
    body?: never;
    path?: never;
    query?: {
        Filter?: string;
        Sorting?: string;
        SkipCount?: number;
        MaxResultCount?: number;
    };
    url: '/api/multi-tenancy/tenants';
};

export type GetApiMultiTenancyTenantsErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiMultiTenancyTenantsError = GetApiMultiTenancyTenantsErrors[keyof GetApiMultiTenancyTenantsErrors];

export type GetApiMultiTenancyTenantsResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfTenantDto;
};

export type GetApiMultiTenancyTenantsResponse = GetApiMultiTenancyTenantsResponses[keyof GetApiMultiTenancyTenantsResponses];

export type PostApiMultiTenancyTenantsData = {
    body?: TenantCreateDto;
    path?: never;
    query?: never;
    url: '/api/multi-tenancy/tenants';
};

export type PostApiMultiTenancyTenantsErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiMultiTenancyTenantsError = PostApiMultiTenancyTenantsErrors[keyof PostApiMultiTenancyTenantsErrors];

export type PostApiMultiTenancyTenantsResponses = {
    /**
     * OK
     */
    200: TenantDto;
};

export type PostApiMultiTenancyTenantsResponse = PostApiMultiTenancyTenantsResponses[keyof PostApiMultiTenancyTenantsResponses];

export type DeleteApiMultiTenancyTenantsByIdDefaultConnectionStringData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/multi-tenancy/tenants/{id}/default-connection-string';
};

export type DeleteApiMultiTenancyTenantsByIdDefaultConnectionStringErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type DeleteApiMultiTenancyTenantsByIdDefaultConnectionStringError = DeleteApiMultiTenancyTenantsByIdDefaultConnectionStringErrors[keyof DeleteApiMultiTenancyTenantsByIdDefaultConnectionStringErrors];

export type DeleteApiMultiTenancyTenantsByIdDefaultConnectionStringResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiMultiTenancyTenantsByIdDefaultConnectionStringData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/multi-tenancy/tenants/{id}/default-connection-string';
};

export type GetApiMultiTenancyTenantsByIdDefaultConnectionStringErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiMultiTenancyTenantsByIdDefaultConnectionStringError = GetApiMultiTenancyTenantsByIdDefaultConnectionStringErrors[keyof GetApiMultiTenancyTenantsByIdDefaultConnectionStringErrors];

export type GetApiMultiTenancyTenantsByIdDefaultConnectionStringResponses = {
    /**
     * OK
     */
    200: string;
};

export type GetApiMultiTenancyTenantsByIdDefaultConnectionStringResponse = GetApiMultiTenancyTenantsByIdDefaultConnectionStringResponses[keyof GetApiMultiTenancyTenantsByIdDefaultConnectionStringResponses];

export type PutApiMultiTenancyTenantsByIdDefaultConnectionStringData = {
    body?: never;
    path: {
        id: string;
    };
    query?: {
        defaultConnectionString?: string;
    };
    url: '/api/multi-tenancy/tenants/{id}/default-connection-string';
};

export type PutApiMultiTenancyTenantsByIdDefaultConnectionStringErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PutApiMultiTenancyTenantsByIdDefaultConnectionStringError = PutApiMultiTenancyTenantsByIdDefaultConnectionStringErrors[keyof PutApiMultiTenancyTenantsByIdDefaultConnectionStringErrors];

export type PutApiMultiTenancyTenantsByIdDefaultConnectionStringResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiEkbTenantData = {
    body?: never;
    path?: never;
    query?: {
        Sorting?: string;
        SkipCount?: number;
        MaxResultCount?: number;
    };
    url: '/api/ekb/tenant';
};

export type GetApiEkbTenantErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbTenantError = GetApiEkbTenantErrors[keyof GetApiEkbTenantErrors];

export type GetApiEkbTenantResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfMasterTenantDto;
};

export type GetApiEkbTenantResponse = GetApiEkbTenantResponses[keyof GetApiEkbTenantResponses];

export type PostApiEkbTenantData = {
    body?: TenantCreateUpdateDto;
    path?: never;
    query?: never;
    url: '/api/ekb/tenant';
};

export type PostApiEkbTenantErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbTenantError = PostApiEkbTenantErrors[keyof PostApiEkbTenantErrors];

export type PostApiEkbTenantResponses = {
    /**
     * OK
     */
    200: MasterTenantDto;
};

export type PostApiEkbTenantResponse = PostApiEkbTenantResponses[keyof PostApiEkbTenantResponses];

export type DeleteApiEkbTenantByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/tenant/{id}';
};

export type DeleteApiEkbTenantByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type DeleteApiEkbTenantByIdError = DeleteApiEkbTenantByIdErrors[keyof DeleteApiEkbTenantByIdErrors];

export type DeleteApiEkbTenantByIdResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiEkbTenantByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/tenant/{id}';
};

export type GetApiEkbTenantByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbTenantByIdError = GetApiEkbTenantByIdErrors[keyof GetApiEkbTenantByIdErrors];

export type GetApiEkbTenantByIdResponses = {
    /**
     * OK
     */
    200: MasterTenantDto;
};

export type GetApiEkbTenantByIdResponse = GetApiEkbTenantByIdResponses[keyof GetApiEkbTenantByIdResponses];

export type PutApiEkbTenantByIdData = {
    body?: TenantCreateUpdateDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/tenant/{id}';
};

export type PutApiEkbTenantByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PutApiEkbTenantByIdError = PutApiEkbTenantByIdErrors[keyof PutApiEkbTenantByIdErrors];

export type PutApiEkbTenantByIdResponses = {
    /**
     * OK
     */
    200: MasterTenantDto;
};

export type PutApiEkbTenantByIdResponse = PutApiEkbTenantByIdResponses[keyof PutApiEkbTenantByIdResponses];

export type PostApiEkbTenantFilterListData = {
    body?: QueryParametersDto;
    path?: never;
    query?: never;
    url: '/api/ekb/tenant/filter-list';
};

export type PostApiEkbTenantFilterListErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbTenantFilterListError = PostApiEkbTenantFilterListErrors[keyof PostApiEkbTenantFilterListErrors];

export type PostApiEkbTenantFilterListResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfMasterTenantDto;
};

export type PostApiEkbTenantFilterListResponse = PostApiEkbTenantFilterListResponses[keyof PostApiEkbTenantFilterListResponses];

export type GetApiTestAuthStatusData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/test/auth-status';
};

export type GetApiTestAuthStatusResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiTestTokenInfoData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/test/token-info';
};

export type GetApiTestTokenInfoResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type PostApiEkbVesselVesselHeadersData = {
    body?: VesselListRequestDto;
    path?: never;
    query?: never;
    url: '/api/ekb/vessel/vessel-headers';
};

export type PostApiEkbVesselVesselHeadersErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbVesselVesselHeadersError = PostApiEkbVesselVesselHeadersErrors[keyof PostApiEkbVesselVesselHeadersErrors];

export type PostApiEkbVesselVesselHeadersResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfVesselHeaderDto;
};

export type PostApiEkbVesselVesselHeadersResponse = PostApiEkbVesselVesselHeadersResponses[keyof PostApiEkbVesselVesselHeadersResponses];

export type PostApiEkbVesselVesselItemsData = {
    body?: VesselListRequestDto;
    path?: never;
    query?: never;
    url: '/api/ekb/vessel/vessel-items';
};

export type PostApiEkbVesselVesselItemsErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbVesselVesselItemsError = PostApiEkbVesselVesselItemsErrors[keyof PostApiEkbVesselVesselItemsErrors];

export type PostApiEkbVesselVesselItemsResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfVesselItemDto;
};

export type PostApiEkbVesselVesselItemsResponse = PostApiEkbVesselVesselItemsResponses[keyof PostApiEkbVesselVesselItemsResponses];

export type PostApiEkbVesselByIdVesselHeaderData = {
    body?: never;
    path: {
        id: string;
    };
    query?: {
        vesselType?: string;
    };
    url: '/api/ekb/vessel/{id}/vessel-header';
};

export type PostApiEkbVesselByIdVesselHeaderErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbVesselByIdVesselHeaderError = PostApiEkbVesselByIdVesselHeaderErrors[keyof PostApiEkbVesselByIdVesselHeaderErrors];

export type PostApiEkbVesselByIdVesselHeaderResponses = {
    /**
     * OK
     */
    200: VesselHeaderDto;
};

export type PostApiEkbVesselByIdVesselHeaderResponse = PostApiEkbVesselByIdVesselHeaderResponses[keyof PostApiEkbVesselByIdVesselHeaderResponses];

export type PostApiEkbVesselByIdVesselItemData = {
    body?: never;
    path: {
        id: string;
    };
    query?: {
        vesselType?: string;
    };
    url: '/api/ekb/vessel/{id}/vessel-item';
};

export type PostApiEkbVesselByIdVesselItemErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbVesselByIdVesselItemError = PostApiEkbVesselByIdVesselItemErrors[keyof PostApiEkbVesselByIdVesselItemErrors];

export type PostApiEkbVesselByIdVesselItemResponses = {
    /**
     * OK
     */
    200: VesselItemDto;
};

export type PostApiEkbVesselByIdVesselItemResponse = PostApiEkbVesselByIdVesselItemResponses[keyof PostApiEkbVesselByIdVesselItemResponses];

export type ClientOptions = {
    baseUrl: `${string}://swagger.json` | (string & {});
};