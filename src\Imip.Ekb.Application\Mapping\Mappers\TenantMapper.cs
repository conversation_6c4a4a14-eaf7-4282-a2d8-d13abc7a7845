﻿using Imip.Ekb.Master.Tenants;
using Imip.Ekb.Master.Tenants.Dtos;
using Riok.Mapperly.Abstractions;
using System;
using System.Collections.Generic;

namespace Imip.Ekb.Mapping.Mappers;

[Mapper]
public partial class TenantMapper : IMapperlyMapper
{
    // Entity to DTO mapping
    [MapProperty(nameof(MasterTenant.Id), nameof(MasterTenantDto.Id))]
    public partial MasterTenantDto MapToDto(MasterTenant entity);

    // DTO to Entity mapping for updates (maps to existing entity)
    [MapperIgnoreTarget(nameof(MasterTenant.Id))] // Don't change existing Id
    [MapperIgnoreTarget(nameof(MasterTenant.CreatedBy))] // Don't overwrite audit fields
    [MapperIgnoreTarget(nameof(MasterTenant.CreatedAt))]
    public partial void MapToEntity(TenantCreateUpdateDto dto, MasterTenant entity);

    // Custom mapping methods for complex scenarios
    public MasterTenant CreateEntityWithId(TenantCreateUpdateDto dto, Guid id)
    {
        // Create entity using reflection since constructor is protected
        var entity = (MasterTenant)Activator.CreateInstance(typeof(MasterTenant), true)!;

        // Map properties manually
        MapToEntity(dto, entity);

        return entity;
    }

    // Map list of entities to DTOs
    public partial List<MasterTenantDto> MapToDtoList(List<MasterTenant> entities);

    // Map IEnumerable for LINQ scenarios
    public partial IEnumerable<MasterTenantDto> MapToDtoEnumerable(IEnumerable<MasterTenant> entities);

    public static System.Linq.Expressions.Expression<Func<MasterTenant, MasterTenantDto>> ProjectionExpression =>
        entity => new MasterTenantDto
        {
            Id = entity.Id,
            ConcurrencyStamp = entity.ConcurrencyStamp,
            DocEntry = entity.DocEntry,
            Name = entity.Name,
            CreatedBy = entity.CreatedBy,
            CreatedAt = entity.CreatedAt,
            UpdatedAt = entity.UpdatedAt,
            Flags = entity.Flags,
            FullName = entity.FullName,
            LetterPerson = entity.LetterPerson,
            LetterRole = entity.LetterRole,
            Npwp = entity.Npwp,
            Address = entity.Address,
            Nib = entity.Nib,
            Phone = entity.Phone,
            Status = entity.Status,
            NoAndDateNotaris = entity.NoAndDateNotaris,
            DescNotaris = entity.DescNotaris,
            Sapcode = entity.Sapcode,
            IsExternal = entity.IsExternal,
            Billing = entity.Billing,
            BillingPrice = entity.BillingPrice,
            EsignUserId = entity.EsignUserId,
            Token = entity.Token,
            SapcodeBdt = entity.SapcodeBdt,
            SapcodeUsd = entity.SapcodeUsd,
            Coordinate = entity.Coordinate,
            Boundaries = entity.Boundaries,
            IsTenant = entity.IsTenant,
            ChannelId = entity.ChannelId,
            UsePrivy = entity.UsePrivy,
            SapcodeS4 = entity.SapcodeS4,
            Skbpph = entity.Skbpph,
            CompanyGroup = entity.CompanyGroup,
            FactoryLocation = entity.FactoryLocation,
            MasterGroupId = entity.MasterGroupId
        };
}
