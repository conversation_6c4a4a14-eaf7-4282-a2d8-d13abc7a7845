using System;
using System.Threading.Tasks;
using Imip.Ekb.Master.PortOfLoadings.Dtos;
using Imip.Ekb.Models;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.Ekb.Master.PortOfLoadings;

public interface IPortOfLoadingAppService :
    ICrudAppService<PortOfLoadingDto, Guid, PagedAndSortedResultRequestDto, PortOfLoadingCreateUpdateDto, PortOfLoadingCreateUpdateDto>
{
    Task<PagedResultDto<PortOfLoadingDto>> FilterListAsync(QueryParametersDto parameters);
}