# Implementation Plan

- [x] 1. Refactor ImportVesselMapper to use automatic mappings













  - Remove excessive explicit MapProperty attributes and let <PERSON><PERSON><PERSON> handle automatic mapping
  - Configure MapperIgnore attributes for properties that should not be mapped
  - Add proper navigation property mapping configuration
  - _Requirements: 1.1, 1.2, 1.3_

- [ ] 2. Implement projection expressions for EF Core compatibility















  - Create projection expression properties for efficient database queries
  - Add MapToProjectionDto method with EF Core compatible mapping
  - Test projection expressions work correctly with Entity Framework
  - _Requirements: 3.1, 3.2_

- [ ] 3. Add comprehensive mapping methods for all DTO scenarios




  - Implement MapToWithItemsDto method using Map<PERSON>ly's nested mapping
  - Create collection mapping methods for lists and enumerables
  - Add custom mapping methods for complex business logic scenarios
  - _Requirements: 4.1, 4.2, 4.3_
-

- [x] 4. Refactor GetWithItemsAsync method in ImportVesselAppService



  - Replace manual property assignment with mapper method call
  - Remove the large manual DTO construction code
  - Ensure all navigation properties are properly mapped through the mapper
  - _Requirements: 2.1, 2.3_

- [ ] 5. Refactor FilterListAsync method to use projection expressions















  - Replace manual LINQ projection with mapper-generated projection expression
  - Update query to use the new projection method
  - Verify performance is maintained or improved
  - _Requirements: 2.2, 3.1, 3.2_

- [-] 6. Clean up mapper configuration and remove redundant code



  - Remove unused MapProperty attributes that are now handled automatically
  - Clean up unnecessary using statements
  - Remove the manual MapToDtoWithItems method that duplicates functionality
  - _Requirements: 1.1, 1.4_

- [ ] 7. Add proper error handling and null safety to mappings
  - Implement custom mapping methods for complex scenarios that need error handling
  - Add null safety checks where needed
  - Create fallback mapping strategies for edge cases
  - _Requirements: 4.4_

- [ ] 8. Create unit tests for the refactored mapping functionality
  - Write tests to verify all properties are mapped correctly
  - Add tests for null safety and edge cases
  - Create performance comparison tests
  - Test EF Core projection compatibility
  - _Requirements: 5.1, 5.2, 5.3_

- [ ] 9. Update entity-to-DTO mapping methods to handle ABP audit properties
  - Configure proper mapping for ABP framework audit fields
  - Ensure CreationTime, CreatorId, LastModificationTime, etc. are handled correctly
  - Add MapperIgnore for properties that should not be mapped from DTOs to entities
  - _Requirements: 1.2, 4.2_

- [ ] 10. Optimize collection and nested object mappings
  - Implement efficient mapping for Items collection in ImportVesselWithItemsDto
  - Configure automatic mapping for nested DTOs like JettyDto, CargoDto, etc.
  - Add custom converters for complex type transformations if needed
  - _Requirements: 1.3, 4.1, 4.3_