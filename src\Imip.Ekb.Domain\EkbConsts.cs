﻿using Volo.Abp.Identity;

namespace Imip.Ekb;

public static class EkbConsts
{
    public const string DbTablePrefix = "App";
    public const string? DbSchema = null;
    public const string AdminEmailDefaultValue = IdentityDataSeedContributor.AdminEmailDefaultValue;
    public const string AdminPasswordDefaultValue = IdentityDataSeedContributor.AdminPasswordDefaultValue;

    public static class ExportVesselType
    {
        public const string Export = "Export";
        public const string TemporaryExport = "TemporaryExport";
    }

    public static class LocalVesselType
    {
        public const string IN = "IN";
        public const string OUT = "OUT";
        public const string Local = "Local";
    }

    public static class ImportVesselType
    {
        public const string Import = "Import";
    }
}
