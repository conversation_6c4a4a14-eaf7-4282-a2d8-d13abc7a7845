using Imip.Ekb.Master.Jetties;
using Imip.Ekb.Master.Jetties.Dtos;
using Riok.Mapperly.Abstractions;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Imip.Ekb.Mapping.Mappers;

[Mapper]
public partial class JettyMapper : IMapperlyMapper
{
    // Entity to DTO mapping
    [MapProperty(nameof(Jetty.Id), nameof(JettyDto.Id))]
    public partial JettyDto MapToDto(Jetty entity);

    // DTO to Entity mapping for updates (maps to existing entity)
    [MapperIgnoreTarget(nameof(Jetty.Id))] // Don't change existing Id
    [MapperIgnoreTarget(nameof(Jetty.CreatedBy))] // Don't overwrite audit fields
    [MapperIgnoreTarget(nameof(Jetty.CreatedAt))]
    public partial void MapToEntity(JettyCreateUpdateDto dto, Jetty entity);

    // Custom mapping methods for complex scenarios
    public Jetty CreateEntityWithId(JettyCreateUpdateDto dto, Guid id)
    {
        // Create entity using reflection since constructor is protected
        var entity = (Jetty)Activator.CreateInstance(typeof(Jetty), true)!;

        // Map properties manually
        MapToEntity(dto, entity);

        return entity;
    }

    // Map list of entities to DTOs
    public partial List<JettyDto> MapToDtoList(List<Jetty> entities);

    // Map IEnumerable for LINQ scenarios
    public partial IEnumerable<JettyDto> MapToDtoEnumerable(IEnumerable<Jetty> entities);
    public static readonly System.Linq.Expressions.Expression<Func<Jetty, JettyDto>> ProjectionExpression = jetty => new JettyDto
    {
        Id = jetty.Id,
        DocEntry = jetty.DocEntry,
        ConcurrencyStamp = jetty.ConcurrencyStamp,
        Name = jetty.Name,
        Alias = jetty.Alias,
        Max = jetty.Max,
        Deleted = jetty.Deleted,
        CreatedBy = jetty.CreatedBy,
        UpdatedBy = jetty.UpdatedBy,
        CreatedAt = jetty.CreatedAt,
        UpdatedAt = jetty.UpdatedAt,
        Port = jetty.Port,
        IsCustomArea = jetty.IsCustomArea
    };
}