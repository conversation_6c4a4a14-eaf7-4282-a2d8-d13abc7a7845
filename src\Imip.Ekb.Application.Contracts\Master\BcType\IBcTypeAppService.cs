using System;
using System.Threading.Tasks;
using Imip.Ekb.Master.BcTypes.Dtos;
using Imip.Ekb.Models;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.Ekb.Master.BcTypes;

public interface IBcTypeAppService :
    ICrudAppService<BcTypeDto, Guid, PagedAndSortedResultRequestDto, BcTypeCreateUpdateDto, BcTypeCreateUpdateDto>
{
    Task<PagedResultDto<BcTypeDto>> FilterListAsync(QueryParametersDto parameters);
}