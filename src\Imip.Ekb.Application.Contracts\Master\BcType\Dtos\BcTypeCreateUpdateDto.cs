using System;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Entities;

namespace Imip.Ekb.Master.BcTypes.Dtos;

public class BcTypeCreateUpdateDto : EntityDto<Guid>, IHasConcurrencyStamp
{
    public string ConcurrencyStamp { get; set; }
    // public int DocEntry { get; set; }
    public string Type { get; set; } = null!;
    public string CreatedBy { get; set; } = null!;
    public int? TransNo { get; set; }
    public string? TransName { get; set; }
    public string Status { get; set; } = null!;
}