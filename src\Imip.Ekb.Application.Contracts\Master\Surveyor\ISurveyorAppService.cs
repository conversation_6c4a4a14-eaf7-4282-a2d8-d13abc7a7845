using System;
using System.Threading.Tasks;
using Imip.Ekb.Master.Surveyors.Dtos;
using Imip.Ekb.Models;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.Ekb.Master.Surveyors;

public interface ISurveyorAppService :
    ICrudAppService<SurveyorDto, Guid, PagedAndSortedResultRequestDto, SurveyorCreateUpdateDto, SurveyorCreateUpdateDto>
{
    Task<PagedResultDto<SurveyorDto>> FilterListAsync(QueryParametersDto parameters);
}