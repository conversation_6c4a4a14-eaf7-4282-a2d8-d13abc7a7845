﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Volo.Abp.Domain.Entities.Auditing;

namespace Imip.Ekb.Master.PortServices;

[Table("MPS")]
public class PortService : FullAuditedAggregateRoot<Guid>
{
    [Key]
    public int DocEntry { get; set; }

    [StringLength(255)]
    public string? ItemCode { get; set; }

    public string? ItemName { get; set; }

    [StringLength(255)]
    public string Active { get; set; } = null!;

    public int? CreatedBy { get; set; }

    public int? UpdatedBy { get; set; }

    [Column("created_at", TypeName = "datetime")]
    public DateTime? CreatedAt { get; set; }

    [Column("updated_at", TypeName = "datetime")]
    public DateTime? UpdatedAt { get; set; }

    [StringLength(255)]
    public string? FrgnName { get; set; }

    [StringLength(50)]
    public string? SapCodeS4 { get; set; }



}
