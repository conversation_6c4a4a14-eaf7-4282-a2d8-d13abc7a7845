﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Imip.Ekb.Migrations
{
    /// <inheritdoc />
    public partial class FixCargoStatusColumnType : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Change Status column from fixed-length CHAR to variable-length NVARCHAR
            migrationBuilder.AlterColumn<string>(
                name: "Status",
                table: "M_CARGO",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "char(255)",
                oldFixedLength: true);

            // Update existing records to trim trailing spaces
            migrationBuilder.Sql(@"
                UPDATE M_CARGO 
                SET status = 
                    LTRIM(RTRIM(
                        STUFF((
                            SELECT ' ' + value 
                            FROM STRING_SPLIT(status, ' ') 
                            WHERE value != '' 
                            FOR XML PATH('')
                        ), 1, 1, '')
                    ))
                WHERE CHARINDEX('  ', status) > 0
            ");


            // Change Status column from fixed-length CHAR to variable-length NVARCHAR
            migrationBuilder.AlterColumn<string>(
                name: "Status",
                table: "M_Tenant",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "char(255)",
                oldFixedLength: true);

            // Update existing records to trim trailing spaces
            migrationBuilder.Sql(@"
                UPDATE M_Tenant 
                SET status = 
                    LTRIM(RTRIM(
                        STUFF((
                            SELECT ' ' + value 
                            FROM STRING_SPLIT(status, ' ') 
                            WHERE value != '' 
                            FOR XML PATH('')
                        ), 1, 1, '')
                    ))
                WHERE CHARINDEX('  ', status) > 0
            ");

            // Change Status column from fixed-length CHAR to variable-length NVARCHAR
            migrationBuilder.AlterColumn<string>(
                name: "Status",
                table: "M_TBC",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "char(255)",
                oldFixedLength: true);

            // Update existing records to trim trailing spaces
            migrationBuilder.Sql(@"
                UPDATE M_TBC 
                SET status = 
                    LTRIM(RTRIM(
                        STUFF((
                            SELECT ' ' + value 
                            FROM STRING_SPLIT(status, ' ') 
                            WHERE value != '' 
                            FOR XML PATH('')
                        ), 1, 1, '')
                    ))
                WHERE CHARINDEX('  ', status) > 0
            ");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Revert Status column back to fixed-length CHAR
            migrationBuilder.AlterColumn<string>(
                name: "Status",
                table: "M_CARGO",
                type: "char(255)",
                fixedLength: true,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(255)",
                oldMaxLength: 255);
        }
    }
}
