using ImportVesselEntity = Imip.Ekb.BoundedZone.ImportVessels.ImportVessel;
using Imip.Ekb.BoundedZone.ImportVessels.Dtos;
using Imip.Ekb.BoundedZone.Dtos;
using Imip.Ekb.BoundedZone;
using Imip.Ekb.BoundedZone.ImportVessels;
using Imip.Ekb.Mapping.Mappers;
using Imip.Ekb.Models;
using Imip.Ekb.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp;
using Imip.Ekb.BoundedZone.ImportVessel;
using Microsoft.Extensions.Configuration;

namespace Imip.Ekb.Application.BoundedZone.ImportVessel;

[Authorize]
public class ImportVesselAppService :
    CrudAppService<
        ImportVesselEntity,
        ImportVesselDto,
        Guid,
        QueryParametersDto,
        CreateUpdateImportVesselDto>,
    IImportVesselAppService
{
    private readonly IImportVesselRepository _importVesselRepository;
    private readonly IZoneDetailRepository _zoneDetailRepository;
    private readonly ImportVesselMapper _importVesselMapper;
    private readonly VesselMapper _vesselMapper;
    private readonly IConfiguration _configuration;
    private readonly ZoneDetailMapper _zoneDetailMapper;
    private readonly ILogger<ImportVesselAppService> _logger;

    public ImportVesselAppService(
        IImportVesselRepository importVesselRepository,
        IZoneDetailRepository zoneDetailRepository,
        ImportVesselMapper importVesselMapper,
        VesselMapper vesselMapper,
        IConfiguration configuration,
        ZoneDetailMapper zoneDetailMapper,
        ILogger<ImportVesselAppService> logger)
        : base(importVesselRepository)
    {
        _configuration = configuration;
        _importVesselRepository = importVesselRepository;
        _zoneDetailRepository = zoneDetailRepository;
        _importVesselMapper = importVesselMapper;
        _vesselMapper = vesselMapper;
        _zoneDetailMapper = zoneDetailMapper;
        _logger = logger;
    }

    public override async Task<PagedResultDto<ImportVesselDto>> GetListAsync(QueryParametersDto input)
    {
        await CheckGetListPolicyAsync();

        var query = await _importVesselRepository.GetQueryableWithIncludesAsync();

        // Apply sorting
        if (!string.IsNullOrWhiteSpace(input.Sorting))
        {
            query = query.OrderBy(input.Sorting);
        }
        else
        {
            query = query.OrderByDescending(x => x.CreationTime);
        }

        var totalCount = await AsyncExecuter.CountAsync(query);

        var entities = await AsyncExecuter.ToListAsync(
            query.PageBy(input.SkipCount, input.MaxResultCount)
        );

        var dtos = entities.Select(MapToGetListOutputDto).ToList();

        return new PagedResultDto<ImportVesselDto>(totalCount, dtos);
    }



    public async Task<int> GenerateNextDocNumAsync(DateTime postDate)
    {
        var prefix = postDate.ToString("yyMM");
        var queryable = await _importVesselRepository.GetQueryableAsync();
        var maxDocNum = queryable
            .Where(x => x.DocNum.ToString().StartsWith(prefix) && x.DocType == EkbConsts.ImportVesselType.Import)
            .OrderByDescending(x => x.DocNum)
            .Select(x => x.DocNum)
            .FirstOrDefault();

        int nextIncrement = 1;
        if (maxDocNum != 0)
        {
            var lastIncrementStr = maxDocNum.ToString().Length > 4 ? maxDocNum.ToString().Substring(4, 4) : "0000";
            if (int.TryParse(lastIncrementStr, out var lastIncrement))
            {
                nextIncrement = lastIncrement + 1;
            }
        }
        var docNum = int.Parse($"{prefix}{nextIncrement:D4}");
        return docNum;
    }

    public override async Task<ImportVesselDto> CreateAsync(CreateUpdateImportVesselDto input)
    {
        await CheckCreatePolicyAsync();

        // Generate DocNum
        var dt = input.PostingDate ?? DateOnly.FromDateTime(DateTime.Now);
        input.DocNum = await GenerateNextDocNumAsync(dt.ToDateTime(TimeOnly.MinValue));

        // Set DocType for Import vessel
        input.DocType = EkbConsts.ImportVesselType.Import;

        var entity = _importVesselMapper.CreateEntityWithId(input, Guid.NewGuid());

        // Set CreatedBy from authenticated user
        entity.CreatedBy = CurrentUser.UserName ?? "System";

        await _importVesselRepository.InsertAsync(entity, autoSave: true);

        // Set HeaderId for each item after entity is inserted (if needed)
        if (input.Items != null && input.Items.Count != 0)
        {
            foreach (var itemDto in input.Items)
            {
                // If HeaderId is Guid, assign entity.Id
                itemDto.HeaderId = entity.Id;
                itemDto.DocNum = entity.DocEntry;
                itemDto.DocType = EkbConsts.ImportVesselType.Import;

                var itemEntity = _zoneDetailMapper.CreateEntityWithId(itemDto, Guid.NewGuid());
                // Set CreatedBy from authenticated user on detail
                itemEntity.CreatedBy = CurrentUser.UserName ?? "System";
                // Ensure required fields are set
                itemEntity.IsUrgent = string.IsNullOrEmpty(itemEntity.IsUrgent) ? "N" : itemEntity.IsUrgent;
                itemEntity.IsScan = string.IsNullOrEmpty(itemEntity.IsScan) ? "N" : itemEntity.IsScan;
                itemEntity.IsOriginal = string.IsNullOrEmpty(itemEntity.IsOriginal) ? "N" : itemEntity.IsOriginal;
                itemEntity.IsSend = string.IsNullOrEmpty(itemEntity.IsSend) ? "N" : itemEntity.IsSend;
                itemEntity.IsFeOri = string.IsNullOrEmpty(itemEntity.IsFeOri) ? "N" : itemEntity.IsFeOri;
                itemEntity.IsFeSend = string.IsNullOrEmpty(itemEntity.IsFeSend) ? "N" : itemEntity.IsFeSend;
                itemEntity.IsChange = string.IsNullOrEmpty(itemEntity.IsChange) ? "N" : itemEntity.IsChange;
                itemEntity.Deleted = string.IsNullOrEmpty(itemEntity.Deleted) ? "N" : itemEntity.Deleted;
                itemEntity.DocType = string.IsNullOrEmpty(itemEntity.DocType) ? "Import" : itemEntity.DocType;
                await _zoneDetailRepository.InsertAsync(itemEntity, autoSave: false);
            }
        }

        // Save all changes in a single transaction
        await CurrentUnitOfWork.SaveChangesAsync();

        // Load the entity with includes for proper mapping
        var createdEntity = await _importVesselRepository.GetAsync(entity.Id);
        return MapToGetOutputDto(createdEntity);
    }

    public override async Task<ImportVesselDto> UpdateAsync(Guid id, CreateUpdateImportVesselDto input)
    {
        await CheckUpdatePolicyAsync();

        var entity = await _importVesselRepository.GetAsync(id);
        _importVesselMapper.MapToEntity(input, entity);

        await _importVesselRepository.UpdateAsync(entity, autoSave: false);


        // Handle items update - update existing items and add new ones
        if (input.Items != null)
        {
            // Get existing items
            var existingItems = await _zoneDetailRepository.GetByHeaderIdAsync(id);

            // Process each item in the input
            for (int i = 0; i < input.Items.Count; i++)
            {
                var itemDto = input.Items[i];
                itemDto.HeaderId = id;

                if (i < existingItems.Count)
                {
                    // Update existing item
                    var existingItem = existingItems[i];
                    _zoneDetailMapper.MapToEntity(itemDto, existingItem);
                    await _zoneDetailRepository.UpdateAsync(existingItem, autoSave: false);
                }
                else
                {
                    // If HeaderId is Guid, assign entity.Id
                    itemDto.HeaderId = entity.Id;
                    itemDto.DocNum = entity.DocEntry;
                    itemDto.DocType = EkbConsts.ImportVesselType.Import;

                    var itemEntity = _zoneDetailMapper.CreateEntityWithId(itemDto, Guid.NewGuid());
                    // Set CreatedBy from authenticated user on detail
                    itemEntity.CreatedBy = CurrentUser.UserName ?? "System";
                    // Ensure required fields are set
                    itemEntity.IsUrgent = string.IsNullOrEmpty(itemEntity.IsUrgent) ? "N" : itemEntity.IsUrgent;
                    itemEntity.IsScan = string.IsNullOrEmpty(itemEntity.IsScan) ? "N" : itemEntity.IsScan;
                    itemEntity.IsOriginal = string.IsNullOrEmpty(itemEntity.IsOriginal) ? "N" : itemEntity.IsOriginal;
                    itemEntity.IsSend = string.IsNullOrEmpty(itemEntity.IsSend) ? "N" : itemEntity.IsSend;
                    itemEntity.IsFeOri = string.IsNullOrEmpty(itemEntity.IsFeOri) ? "N" : itemEntity.IsFeOri;
                    itemEntity.IsFeSend = string.IsNullOrEmpty(itemEntity.IsFeSend) ? "N" : itemEntity.IsFeSend;
                    itemEntity.IsChange = string.IsNullOrEmpty(itemEntity.IsChange) ? "N" : itemEntity.IsChange;
                    itemEntity.Deleted = string.IsNullOrEmpty(itemEntity.Deleted) ? "N" : itemEntity.Deleted;
                    itemEntity.DocType = string.IsNullOrEmpty(itemEntity.DocType) ? "Import" : itemEntity.DocType;
                    await _zoneDetailRepository.InsertAsync(itemEntity, autoSave: false);
                }
            }

            // Remove excess existing items if input has fewer items
            for (int i = input.Items.Count; i < existingItems.Count; i++)
            {
                await _zoneDetailRepository.DeleteAsync(existingItems[i], autoSave: false);
            }
        }

        // Save all changes in a single transaction
        await CurrentUnitOfWork.SaveChangesAsync();

        // Load the entity with includes for proper mapping
        var updatedEntity = await _importVesselRepository.GetAsync(entity.Id);
        return MapToGetOutputDto(updatedEntity);
    }

    public override async Task<ImportVesselDto> GetAsync(Guid id)
    {
        await CheckGetPolicyAsync();

        var entity = await _importVesselRepository.GetQueryableWithItemsSplitAsync(id);

        if (entity == null)
        {
            throw new UserFriendlyException("LocalVessel not found");
        }

        // Map the items from the navigation property
        var items = entity.Items?.Select(zoneDetail =>
        {
            var itemDto = _vesselMapper.MapZoneDetailToItemDtoWithType(zoneDetail, "LocalVessel");

            // Use navigation property for Tenant
            if (zoneDetail.Tenant != null)
            {
                itemDto.Tenant = _vesselMapper.MapTenantToTenantShortDto(zoneDetail.Tenant);
            }

            // Map DocAttachments if available
            if (zoneDetail.DocAttachment != null && zoneDetail.DocAttachment.Any())
            {
                itemDto.Attachments = zoneDetail.DocAttachment.Select(att => _vesselMapper.MapDocAttachmentToDto(att)).ToList();
            }
            else
            {
                itemDto.Attachments = new List<DocAttachmentSortDto>();
            }

            return itemDto;
        }).ToList() ?? new List<VesselItemDto>();

        return _importVesselMapper.MapToDtoWithItems(entity, items);
    }

    private IQueryable<ImportVesselEntity> ApplyDynamicQuery(IQueryable<ImportVesselEntity> query, QueryParametersDto parameters)
    {
        // Apply filters if provided
        if (parameters.FilterGroup?.Conditions != null && parameters.FilterGroup.Conditions.Count > 0)
        {
            query = DynamicQueryBuilder<ImportVesselEntity>.ApplyFilters(query, parameters.FilterGroup);
        }

        // Apply sorting
        if (parameters.Sort?.Count > 0)
        {
            query = DynamicQueryBuilder<ImportVesselEntity>.ApplyMultipleSorting(query, parameters.Sort);
        }
        else if (!string.IsNullOrEmpty(parameters.Sorting))
        {
            query = query.OrderBy(parameters.Sorting);
        }
        else
        {
            query = query.OrderByDescending(x => x.CreationTime);
        }

        return query;
    }

    protected override ImportVesselDto MapToGetOutputDto(ImportVesselEntity entity)
    {
        return _importVesselMapper.MapToDto(entity);
    }

    protected override ImportVesselDto MapToGetListOutputDto(ImportVesselEntity entity)
    {
        return _importVesselMapper.MapToDto(entity);
    }

    protected override ImportVesselEntity MapToEntity(CreateUpdateImportVesselDto createInput)
    {
        return _importVesselMapper.MapToEntity(createInput);
    }

    protected override void MapToEntity(CreateUpdateImportVesselDto updateInput, ImportVesselEntity entity)
    {
        _importVesselMapper.MapToEntity(updateInput, entity);
    }

    public async Task<ImportVesselWithItemsDto> GetWithItemsAsync(Guid id)
    {
        await CheckGetPolicyAsync();

        var vesselWithItems = await _importVesselRepository.GetQueryableWithItemsSplitAsync(id);

        if (vesselWithItems == null)
        {
            throw new UserFriendlyException("ImportVessel not found");
        }

        return vesselWithItems.MapToWithItemsDto(_importVesselMapper, _configuration);
    }


    public async Task<PagedResultDto<ImportVesselProjectionDto>> FilterListAsync(QueryParametersDto parameters)
    {
        await CheckGetListPolicyAsync();

        // Get queryable and apply filters/sorting
        var entityQuery = await _importVesselRepository.GetQueryableWithIncludesAsync();
        entityQuery = ApplyDynamicQuery(entityQuery, parameters);

        // Use the projection expression from mapper for optimal performance
        var projectedQuery = entityQuery.Select(ImportVesselMapper.ProjectionExpression);

        // Execute count and data queries efficiently
        var totalCount = await AsyncExecuter.CountAsync(projectedQuery);
        var items = await AsyncExecuter.ToListAsync(
            projectedQuery.Skip(parameters.SkipCount).Take(parameters.MaxResultCount)
        );

        return new PagedResultDto<ImportVesselProjectionDto>
        {
            TotalCount = totalCount,
            Items = items
        };
    }
}