using System;
using System.ComponentModel.DataAnnotations;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Entities;

namespace Imip.Ekb.Master.BusinessPartners.Dtos;

public class BusinessPartnerDto : AuditedEntityDto<Guid>, IHasConcurrencyStamp
{
    public string ConcurrencyStamp { get; set; }
    public int DocEntry { get; set; }
    public string Name { get; set; } = string.Empty;
    public string CreatedBy { get; set; } = string.Empty;
    public DateTime? CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public string Status { get; set; } = string.Empty;
    public string? Alias { get; set; }
    public string? Image { get; set; }
    public string? Direction { get; set; }
    public string RegionType { get; set; } = string.Empty;
    public string? Address { get; set; }
    public string? Tenant { get; set; }
    public string? Npwp { get; set; }
    public string? Nitku { get; set; }


}