using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using Imip.Ekb.BoundedZone;
using Imip.Ekb.BoundedZone.Dtos;
using Imip.Ekb.Mapping.Mappers;
using Imip.Ekb.Models;
using Imip.Ekb.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.Ekb.Vessel;

[Authorize]
public class BoundedZoneAppService :
    CrudAppService<ZoneDetail, ZoneDetailDto, Guid, PagedAndSortedResultRequestDto, CreateUpdateVesselItemDto, CreateUpdateVesselItemDto>,
    IZoneDetailAppService
{
    private readonly IZoneDetailRepository _zoneDetailRepository;
    private readonly ZoneDetailMapper _mapper;
    private readonly ILogger<BoundedZoneAppService> _logger;

    public BoundedZoneAppService(
        IZoneDetailRepository zoneDetailRepository,
        ZoneDetailMapper mapper,
        ILogger<BoundedZoneAppService> logger)
        : base(zoneDetailRepository)
    {
        _zoneDetailRepository = zoneDetailRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public override async Task<ZoneDetailDto> CreateAsync(CreateUpdateVesselItemDto input)
    {
        await CheckCreatePolicyAsync();

        var entity = _mapper.CreateEntityWithId(input, Guid.NewGuid());

        // Set audit properties
        entity.CreatedAt = Clock.Now;

        await _zoneDetailRepository.InsertAsync(entity, autoSave: true);

        return _mapper.MapToDto(entity);
    }

    public override async Task<ZoneDetailDto> UpdateAsync(Guid id, CreateUpdateVesselItemDto input)
    {
        await CheckUpdatePolicyAsync();

        var entity = await _zoneDetailRepository.GetAsync(id);

        // Preserve original creation info
        var originalCreatedBy = entity.CreatedBy;
        var originalCreatedAt = entity.CreatedAt;
        var originalCreatedId = entity.CreatedId;

        _mapper.MapToEntity(input, entity);

        // Restore preserved values
        entity.CreatedBy = originalCreatedBy;
        entity.CreatedAt = originalCreatedAt;
        entity.CreatedId = originalCreatedId;

        // Set update audit properties
        entity.UpdatedAt = Clock.Now;

        await _zoneDetailRepository.UpdateAsync(entity, autoSave: true);

        return _mapper.MapToDto(entity);
    }

    public override async Task DeleteAsync(Guid id)
    {
        await CheckDeletePolicyAsync();

        var entity = await _zoneDetailRepository.GetAsync(id);

        // Soft delete implementation
        entity.DeletedAt = Clock.Now;
        entity.DeleteBy = CurrentUser.UserName;
        entity.Deleted = "Y";

        await _zoneDetailRepository.UpdateAsync(entity, autoSave: true);
    }

    public override async Task<ZoneDetailDto> GetAsync(Guid id)
    {
        await CheckGetPolicyAsync();

        var entity = await _zoneDetailRepository.GetAsync(id);
        return _mapper.MapToDto(entity);
    }

    public override async Task<PagedResultDto<ZoneDetailDto>> GetListAsync(PagedAndSortedResultRequestDto input)
    {
        await CheckGetListPolicyAsync();

        // Use optimized queryable that pre-filters deleted records
        var queryable = await _zoneDetailRepository.GetOptimizedQueryableAsync();

        // Apply sorting before counting for better performance
        var sortedQueryable = queryable
            .OrderBy(input.Sorting.IsNullOrWhiteSpace() ? nameof(ZoneDetail.DocEntry) : input.Sorting);

        // Use optimized counting strategy with timeout protection
        int totalCount;
        try
        {
            // Try to get count with timeout protection
            totalCount = await _zoneDetailRepository.GetCountWithTimeoutAsync(30); // 30 second timeout
        }
        catch (TimeoutException ex)
        {
            _logger.LogWarning("Count query timed out: {Message}", ex.Message);

            // Fallback strategies for large datasets
            if (input.SkipCount > 10000) // If skipping more than 10k records, don't count
            {
                totalCount = -1; // Indicate unknown count
                _logger.LogInformation("Skipping count for large dataset (skip count: {SkipCount})", input.SkipCount);
            }
            else
            {
                // Use a more efficient count with limit
                totalCount = await AsyncExecuter.CountAsync(
                    queryable.Take(100000) // Limit count to prevent timeout
                );
                _logger.LogInformation("Used limited count strategy, result: {Count}", totalCount);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error during count operation");
            throw;
        }

        // Apply paging
        var entities = await AsyncExecuter.ToListAsync(
            sortedQueryable
                .PageBy(input.SkipCount, input.MaxResultCount)
        );

        var dtos = entities.Select(_mapper.MapToDto).ToList();

        return new PagedResultDto<ZoneDetailDto>(totalCount, dtos);
    }

    public virtual async Task<PagedResultDto<ZoneDetailDto>> FilterListAsync(QueryParametersDto parameters)
    {
        // Get base query from repository
        var query = await _zoneDetailRepository.GetQueryableWithIncludesAsync();

        // Apply dynamic filtering and sorting HERE (in Application Service)
        query = ApplyDynamicQuery(query, parameters);

        // Get total count
        var totalCount = await AsyncExecuter.CountAsync(query);

        // Apply paging
        var items = await AsyncExecuter.ToListAsync(
            query.Skip(parameters.SkipCount).Take(parameters.MaxResultCount)
        );

        // Map to DTOs using Mapperly
        var dtos = _mapper.MapToDtoList(items);

        return new PagedResultDto<ZoneDetailDto>
        {
            TotalCount = totalCount,
            Items = dtos
        };
    }

    public virtual async Task<PagedResultDto<ZoneDetailDto>> GetQueryableWithVesselHeadersAsync(QueryParametersDto parameters)
    {
        // Get base query from repository with vessel headers
        var query = await _zoneDetailRepository.GetQueryableWithVesselHeadersAsync();

        // Apply dynamic filtering and sorting
        query = ApplyDynamicQuery(query, parameters);

        // Get total count
        var totalCount = await AsyncExecuter.CountAsync(query);

        // Apply paging
        var items = await AsyncExecuter.ToListAsync(
            query.Skip(parameters.SkipCount).Take(parameters.MaxResultCount)
        );

        // Map to DTOs using Mapperly
        var dtos = _mapper.MapToDtoList(items);

        return new PagedResultDto<ZoneDetailDto>
        {
            TotalCount = totalCount,
            Items = dtos
        };
    }

    public virtual async Task<PagedResultDto<ZoneDetailWithVesselHeadersDto>> QueryableWithVesselHeadersDtoAsync(QueryParametersDto parameters)
    {
        // Get base query from repository with vessel headers
        var query = await _zoneDetailRepository.GetQueryableWithVesselHeadersAsync();

        // Apply dynamic filtering and sorting
        query = ApplyDynamicQuery(query, parameters);

        // Get total count
        var totalCount = await AsyncExecuter.CountAsync(query);

        // Apply paging
        var items = await AsyncExecuter.ToListAsync(
            query.Skip(parameters.SkipCount).Take(parameters.MaxResultCount)
        );

        // Map to DTOs using Mapperly - you'll need to create a mapper for ZoneDetailWithVesselHeadersDto
        var dtos = items.Select(item => new ZoneDetailWithVesselHeadersDto
        {
            // Map base ZoneDetailDto properties
            Id = item.Id,
            DocEntry = item.DocEntry,
            BcTypeKey = item.BcTypeKey,
            TenantKey = item.TenantKey,
            Bp = item.BP,
            Cargo = item.Cargo,
            Weight = item.Weight,
            BlNo = item.BlNo,
            BlDate = item.BlDate,
            AjuNo = item.AjuNo,
            RegNo = item.RegNo,
            RegDate = item.RegDate,
            SppbNo = item.SppbNo,
            SppbDate = item.SppbDate,
            SppdNo = item.SppdNo,
            SppdDate = item.SppdDate,
            Shipment = item.Shipment,
            Remarks = item.Remarks,
            CreatedBy = item.CreatedBy,
            CreatedAt = item.CreatedAt,
            UpdatedAt = item.UpdatedAt,
            CreatedId = item.CreatedId,
            UpdatedBy = item.UpdatedBy,
            UpdatedId = item.UpdatedId,
            Color = item.Color,
            SapKbEntry = item.SapKbEntry,
            NoBl = item.NoBl,
            DateBl = item.DateBl,
            NoInv = item.NoInv,
            DateInv = item.DateInv,
            ShipmentNo = item.ShipmentNo,
            EbillingDate = item.EbillingDate,
            Skep = item.Skep,
            SkepDate = item.SkepDate,
            PibNo = item.PibNo,
            PibDate = item.PibDate,
            VesselArrive = item.VesselArrive,
            ExpiredDate = item.ExpiredDate,
            Item = item.Item,
            Qty = item.Qty,
            Amount = item.Amount,
            Status = item.Status,
            SiteStatus = item.SiteStatus,
            DocNum = item.DocNum,
            Flags = item.Flags,
            OceanFreight = item.OceanFreight,
            Currency = item.Currency,
            Ocean = item.Ocean,
            Cbmb = item.Cbmb,
            FreightValue = item.FreightValue,
            Attachment = item.Attachment,
            PostDate = item.PostDate,
            DocType = item.DocType,
            IsScan = item.IsScan,
            IsOriginal = item.IsOriginal,
            IsSend = item.IsSend,
            IsFeOri = item.IsFeOri,
            IsFeSend = item.IsFeSend,
            SecretKey = item.SecretKey,
            Ppjk = item.Ppjk,
            PpjkcodeTemp = item.PpjkcodeTemp,
            PortOfLoading = item.PortOfLoading,
            EmailToPpjk = item.EmailToPpjk,
            LetterNo = item.LetterNo,
            ItemName = item.ItemName,
            ItemQty = item.ItemQty,
            UnitQty = item.UnitQty,
            GrossWeight = item.GrossWeight,
            UnitWeight = item.UnitWeight,
            MatchKey = item.MatchKey,
            Bpnum = item.Bpnum,
            CargoNum = item.CargoNum,
            LineNum = item.LineNum,
            IsChange = item.IsChange,
            Deleted = item.Deleted,
            SppbUpdateDate = item.SppbUpdateDate,
            SppbNoUpdate = item.SppbNoUpdate,
            SppbDateUpdate = item.SppbDateUpdate,
            SppdNoUpdate = item.SppdNoUpdate,
            SppdDateUpdate = item.SppdDateUpdate,
            DeletedAt = item.DeletedAt,
            DeleteBy = item.DeleteBy,
            EBillingNo = item.EBillingNo,
            ContractNo = item.ContractNo,
            OpenDate = item.OpenDate,
            UpdateDate = item.UpdateDate,
            InternalCode = item.InternalCode,
            ContractDate = item.ContractDate,
            RegType = item.RegType,
            Cbm = item.Cbm,
            Notification = item.Notification,
            Sppbstatus = item.Sppbstatus,
            Agent = item.Agent,
            BillingId = item.BillingId,
            InsuranceCurrency = item.InsuranceCurrency,
            InsuranceValue = item.InsuranceValue,
            DestinationPortId = item.DestinationPortId,
            NetWeight = item.NetWeight,
            UnitPrice = item.UnitPrice,
            TotalInv = item.TotalInv,
            QtyEstimate = item.QtyEstimate,
            PriceEstimate = item.PriceEstimate,
            BillingType = item.BillingType,
            ChargeTo = item.ChargeTo,
            QtyRevised = item.QtyRevised,
            PriceRevised = item.PriceRevised,
            NoNota = item.NoNota,
            TotalEstimate = item.TotalEstimate,
            TotalRevised = item.TotalRevised,
            SerialNumber = item.SerialNumber,
            SerialNumber1 = item.SerialNumber1,
            SerialNumber2 = item.SerialNumber2,
            SerialNumber3 = item.SerialNumber3,
            SerialNumber4 = item.SerialNumber4,
            SerialNumber5 = item.SerialNumber5,
            SerialNumber6 = item.SerialNumber6,
            IsParent = item.IsParent,
            GrtVessel = item.GrtVessel,
            NpwpBp = item.NpwpBp,
            EsignDecimal = item.EsignDecimal,
            CargoId = item.CargoId,
            BargeId = item.BargeId,
            Voyage = item.Voyage,
            VesselName = item.VesselName,
            ProcessName = item.ProcessName,
            Rate = item.Rate,
            Bm = item.Bm,
            Ppn = item.Ppn,
            Pph = item.Pph,
            Bmad = item.Bmad,
            Bmtp = item.Bmtp,
            FormType = item.FormType,
            BillingDate = item.BillingDate,
            IsUrgent = item.IsUrgent,
            SurveyorId = item.SurveyorId,
            SurveyorName = item.SurveyorName,
            EmailToBcDate = item.EmailToBcDate,
            Container = item.Container,
            ExportClassificationId = item.ExportClassificationId,
            WarehouseId = item.WarehouseId,
            IncreaseValue = item.IncreaseValue,
            DecreaseValue = item.DecreaseValue,
            IncreaseValuePpn = item.IncreaseValuePpn,
            DecreaseValuePpn = item.DecreaseValuePpn,
            IncreaseValuePph = item.IncreaseValuePph,
            DecreaseValuePph = item.DecreaseValuePph,
            RepairLocation = item.RepairLocation,
            RepresentativeId = item.RepresentativeId,
            InvoiceDetailId = item.InvoiceDetailId,
            CostOfRepair = item.CostOfRepair,
            ItemCategoryCode = item.ItemCategoryCode,
            ItemCategoryDescription = item.ItemCategoryDescription,
            SapBillingStatus = item.SapBillingStatus,
            LetterDate = item.LetterDate,
            ShippingInstructionNo = item.ShippingInstructionNo,
            ShippingInstructionDate = item.ShippingInstructionDate,
            TenantId = item.TenantId,
            BcTypeId = item.BcTypeId,
            HeaderId = item.HeaderId,
            BusinessPartnerId = item.BusinessPartnerId,
            AgentId = item.AgentId,
            MasterExportClassificationId = item.MasterExportClassificationId,

            // Map vessel header information
            ImportVessel = item.ImportVessel != null ? new ImportVesselShortDto
            {
                Id = item.ImportVessel.Id,
                DocNum = item.ImportVessel.DocNum,
                VesselName = item.ImportVessel.VesselName,
                Voyage = item.ImportVessel.Voyage,
                VesselArrival = item.ImportVessel.VesselArrival,
                VesselDeparture = item.ImportVessel.VesselDeparture,
                Shipment = item.ImportVessel.Shipment,
                PortOrigin = item.ImportVessel.PortOrigin,
                DestinationPort = item.ImportVessel.DestinationPort,
                BerthingDate = item.ImportVessel.BerthingDate,
                AnchorageDate = item.ImportVessel.AnchorageDate,
                UnloadingDate = item.ImportVessel.UnloadingDate,
                FinishUnloadingDate = item.ImportVessel.FinishUnloadingDate,
                GrtWeight = item.ImportVessel.GrtWeight,
                Status = item.ImportVessel.Status,
                AgentName = item.ImportVessel.AgentName,

                // Master entity mappings
                MasterJetty = item.ImportVessel.MasterJetty != null ? new MasterJettyShortDto
                {
                    Id = item.ImportVessel.MasterJetty.Id,
                    Name = item.ImportVessel.MasterJetty.Name,
                    Alias = item.ImportVessel.MasterJetty.Alias,
                    Port = item.ImportVessel.MasterJetty.Port,
                    Max = item.ImportVessel.MasterJetty.Max
                } : null,
                MasterPortOrigin = item.ImportVessel.MasterPortOrigin != null ? new MasterPortOriginShortDto
                {
                    Id = item.ImportVessel.MasterPortOrigin.Id,
                    Name = item.ImportVessel.MasterPortOrigin.Name,
                    Code = null, // PortOfLoading doesn't have Code property
                    Country = item.ImportVessel.MasterPortOrigin.Country
                } : null,
                MasterDestinationPort = item.ImportVessel.MasterDestinationPort != null ? new MasterDestinationPortShortDto
                {
                    Id = item.ImportVessel.MasterDestinationPort.Id,
                    Name = item.ImportVessel.MasterDestinationPort.Name,
                    Code = null, // DestinationPort doesn't have Code property
                    Country = item.ImportVessel.MasterDestinationPort.Country
                } : null,
                MasterVessel = item.ImportVessel.Vessel != null ? new MasterVesselShortDto
                {
                    Id = item.ImportVessel.Vessel.Id,
                    Name = item.ImportVessel.Vessel.Name,
                    Alias = item.ImportVessel.Vessel.Alias,
                    Type = item.ImportVessel.Vessel.Type,
                    GrossWeight = item.ImportVessel.Vessel.GrossWeight
                } : null
            } : null,

            ExportVessel = item.ExportVessel != null ? new ExportVesselShortDto
            {
                Id = item.ExportVessel.Id,
                DocNum = item.ExportVessel.DocNum,
                VesselName = item.ExportVessel.Vessel?.Name,
                Voyage = item.ExportVessel.Voyage,
                VesselArrival = item.ExportVessel.VesselArrival,
                VesselDeparture = item.ExportVessel.VesselDeparture,
                Shipment = item.ExportVessel.Shipment,
                PortOrigin = item.ExportVessel.PortOrigin,
                DestinationPort = item.ExportVessel.DestinationPort,
                BerthingDate = item.ExportVessel.BerthingDate,
                AnchorageDate = item.ExportVessel.AnchorageDate,
                UnloadingDate = item.ExportVessel.UnloadingDate,
                FinishUnloadingDate = item.ExportVessel.FinishUnloadingDate,
                GrtWeight = item.ExportVessel.GrtWeight,
                Status = item.ExportVessel.DocStatus,
                AgentName = item.ExportVessel.MasterAgent?.Name,

                // Master entity mappings
                MasterJetty = item.ExportVessel.MasterJetty != null ? new MasterJettyShortDto
                {
                    Id = item.ExportVessel.MasterJetty.Id,
                    Name = item.ExportVessel.MasterJetty.Name,
                    Alias = item.ExportVessel.MasterJetty.Alias,
                    Port = item.ExportVessel.MasterJetty.Port,
                    Max = item.ExportVessel.MasterJetty.Max
                } : null,
                MasterPortOrigin = item.ExportVessel.MasterPortOrigin != null ? new MasterPortOriginShortDto
                {
                    Id = item.ExportVessel.MasterPortOrigin.Id,
                    Name = item.ExportVessel.MasterPortOrigin.Name,
                    Code = null,
                    Country = item.ExportVessel.MasterPortOrigin.Country
                } : null,
                MasterDestinationPort = item.ExportVessel.MasterDestinationPort != null ? new MasterDestinationPortShortDto
                {
                    Id = item.ExportVessel.MasterDestinationPort.Id,
                    Name = item.ExportVessel.MasterDestinationPort.Name,
                    Code = null,
                    Country = item.ExportVessel.MasterDestinationPort.Country
                } : null,
                MasterVessel = item.ExportVessel.Vessel != null ? new MasterVesselShortDto
                {
                    Id = item.ExportVessel.Vessel.Id,
                    Name = item.ExportVessel.Vessel.Name,
                    Alias = item.ExportVessel.Vessel.Alias,
                    Type = item.ExportVessel.Vessel.Type,
                    GrossWeight = item.ExportVessel.Vessel.GrossWeight
                } : null
            } : null,

            LocalVessel = item.LocalVessel != null ? new LocalVesselShortDto
            {
                Id = item.LocalVessel.Id,
                DocNum = item.LocalVessel.DocNum,
                VesselName = item.LocalVessel.Vessel?.Name,
                Voyage = item.LocalVessel.Voyage,
                VesselArrival = item.LocalVessel.VesselArrival,
                VesselDeparture = item.LocalVessel.VesselDeparture,
                Shipment = item.LocalVessel.Shipment,
                PortOrigin = item.LocalVessel.PortOrigin,
                DestinationPort = item.LocalVessel.DestinationPort,
                BerthingDate = item.LocalVessel.BerthingDate,
                AnchorageDate = item.LocalVessel.AnchorageDate,
                UnloadingDate = item.LocalVessel.UnloadingDate,
                FinishUnloadingDate = item.LocalVessel.FinishUnloadingDate,
                GrtWeight = item.LocalVessel.GrtWeight,
                Status = item.LocalVessel.DocStatus,
                AgentName = item.LocalVessel.MasterAgent?.Name,

                // Master entity mappings
                MasterJetty = item.LocalVessel.MasterJetty != null ? new MasterJettyShortDto
                {
                    Id = item.LocalVessel.MasterJetty.Id,
                    Name = item.LocalVessel.MasterJetty.Name,
                    Alias = item.LocalVessel.MasterJetty.Alias,
                    Port = item.LocalVessel.MasterJetty.Port,
                    Max = item.LocalVessel.MasterJetty.Max
                } : null,
                MasterPortOrigin = item.LocalVessel.MasterPortOrigin != null ? new MasterPortOriginShortDto
                {
                    Id = item.LocalVessel.MasterPortOrigin.Id,
                    Name = item.LocalVessel.MasterPortOrigin.Name,
                    Code = null,
                    Country = item.LocalVessel.MasterPortOrigin.Country
                } : null,
                MasterDestinationPort = item.LocalVessel.MasterDestinationPort != null ? new MasterDestinationPortShortDto
                {
                    Id = item.LocalVessel.MasterDestinationPort.Id,
                    Name = item.LocalVessel.MasterDestinationPort.Name,
                    Code = null,
                    Country = item.LocalVessel.MasterDestinationPort.Country
                } : null,
                MasterVessel = item.LocalVessel.Vessel != null ? new MasterVesselShortDto
                {
                    Id = item.LocalVessel.Vessel.Id,
                    Name = item.LocalVessel.Vessel.Name,
                    Alias = item.LocalVessel.Vessel.Alias,
                    Type = item.LocalVessel.Vessel.Type,
                    GrossWeight = item.LocalVessel.Vessel.GrossWeight
                } : null,
                MasterBarge = item.LocalVessel.Barge != null ? new MasterVesselShortDto
                {
                    Id = item.LocalVessel.Barge.Id,
                    Name = item.LocalVessel.Barge.Name,
                    Alias = item.LocalVessel.Barge.Alias,
                    Type = item.LocalVessel.Barge.Type,
                    GrossWeight = item.LocalVessel.Barge.GrossWeight
                } : null
            } : null,

            TradingVessel = item.TradingVessel != null ? new TradingVesselShortDto
            {
                Id = item.TradingVessel.Id,
                VesselName = null, // TradingVessel doesn't have VesselName property
                Voyage = null, // TradingVessel doesn't have Voyage property
                VesselArrival = null, // TradingVessel doesn't have VesselArrival property
                VesselDeparture = null, // TradingVessel doesn't have VesselDeparture property
                Shipment = null, // TradingVessel doesn't have Shipment property
                PortOrigin = null, // TradingVessel doesn't have PortOrigin property
                DestinationPort = null, // TradingVessel doesn't have DestinationPort property
                BerthingDate = null, // TradingVessel doesn't have BerthingDate property
                AnchorageDate = null, // TradingVessel doesn't have AnchorageDate property
                UnloadingDate = null, // TradingVessel doesn't have UnloadingDate property
                FinishUnloadingDate = null, // TradingVessel doesn't have FinishUnloadingDate property
                GrtWeight = null, // TradingVessel doesn't have GrtWeight property
                Status = item.TradingVessel.Status,
                AgentName = null, // TradingVessel doesn't have AgentName property

                // Master entity mappings (TradingVessel has limited master entity relationships)
                MasterJetty = null, // TradingVessel doesn't have MasterJetty relationship
                MasterPortOrigin = null, // TradingVessel doesn't have MasterPortOrigin relationship
                MasterDestinationPort = null, // TradingVessel doesn't have MasterDestinationPort relationship
                MasterVessel = null // TradingVessel doesn't have MasterVessel relationship
            } : null
        }).ToList();

        return new PagedResultDto<ZoneDetailWithVesselHeadersDto>
        {
            TotalCount = totalCount,
            Items = dtos
        };
    }

    private IQueryable<ZoneDetail> ApplyDynamicQuery(IQueryable<ZoneDetail> query, QueryParametersDto parameters)
    {
        // Check if we need to include deeper relationships
        var fieldsToCheck = new List<string>();

        // Add filter fields
        if (parameters.FilterGroup?.Conditions != null)
        {
            fieldsToCheck.AddRange(parameters.FilterGroup.Conditions
                .Where(c => c.FieldName.Contains('.'))
                .Select(c => c.FieldName));
        }

        // Add sort fields
        if (parameters.Sort?.Count > 0)
        {
            fieldsToCheck.AddRange(parameters.Sort
                .Where(s => s.Field.Contains('.'))
                .Select(s => s.Field));
        }

        // Apply filters
        if (parameters.FilterGroup?.Conditions != null && parameters.FilterGroup.Conditions.Count > 0)
        {
            query = DynamicQueryBuilder<ZoneDetail>.ApplyFilters(query, parameters.FilterGroup);
        }

        // Apply sorting
        if (parameters.Sort?.Count > 0)
        {
            query = DynamicQueryBuilder<ZoneDetail>.ApplyMultipleSorting(query, parameters.Sort);
        }
        else if (!string.IsNullOrEmpty(parameters.Sorting))
        {
            query = query.OrderBy(parameters.Sorting);
        }
        else
        {
            query = query.OrderByDescending(x => x.CreationTime);
        }

        return query;
    }
}