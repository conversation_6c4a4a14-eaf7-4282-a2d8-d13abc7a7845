﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Imip.Ekb.Billing.LocalVesselBillings;
using Imip.Ekb.BoundedZone;
using Volo.Abp.Domain.Entities.Auditing;

namespace Imip.Ekb.Master.Tenants;

[Table("M_Tenant")]
public class MasterTenant : FullAuditedAggregateRoot<Guid>
{
    [Key]
    public int DocEntry { get; set; }

    [StringLength(255)]
    public string Name { get; set; } = null!;

    [Column("Created_by")]
    [StringLength(255)]
    public string CreatedBy { get; set; } = null!;

    [Column("created_at", TypeName = "datetime")]
    public DateTime? CreatedAt { get; set; }

    [Column("updated_at", TypeName = "datetime")]
    public DateTime? UpdatedAt { get; set; }

    [StringLength(20)]
    public string? Flags { get; set; }

    public string? FullName { get; set; }

    [StringLength(250)]
    public string? LetterPerson { get; set; }

    [StringLength(250)]
    public string? LetterRole { get; set; }

    [Column("NPWP")]
    [StringLength(250)]
    public string? Npwp { get; set; }

    public string? Address { get; set; }

    [Column("NIB")]
    [StringLength(200)]
    public string? Nib { get; set; }

    [StringLength(200)]
    public string? Phone { get; set; }

    [StringLength(255)]
    public string Status { get; set; } = null!;

    public string? NoAndDateNotaris { get; set; }

    public string? DescNotaris { get; set; }

    [Column("SAPCode")]
    [StringLength(100)]
    public string? Sapcode { get; set; }

    [Column("isExternal")]
    [StringLength(10)]
    public string IsExternal { get; set; } = null!;

    [StringLength(100)]
    public string? Billing { get; set; }

    [Column(TypeName = "decimal(20, 4)")]
    public decimal? BillingPrice { get; set; }

    [Column("esign_user_id")]
    [StringLength(255)]
    public string? EsignUserId { get; set; }

    [Column("token")]
    public string? Token { get; set; }

    [Column("SAPCodeBDT")]
    [StringLength(100)]
    public string? SapcodeBdt { get; set; }

    [Column("SAPCodeUSD")]
    [StringLength(255)]
    public string? SapcodeUsd { get; set; }

    [StringLength(255)]
    public string? Coordinate { get; set; }

    [StringLength(255)]
    public string? Boundaries { get; set; }

    [StringLength(255)]
    public string? IsTenant { get; set; }

    [StringLength(255)]
    public string? ChannelId { get; set; }

    [StringLength(10)]
    public string UsePrivy { get; set; } = null!;

    [Column("SAPCodeS4")]
    [StringLength(30)]
    public string? SapcodeS4 { get; set; }

    [Column("SKBPPH")]
    [StringLength(10)]
    public string? Skbpph { get; set; }

    [StringLength(255)]
    public string? CompanyGroup { get; set; }

    [StringLength(255)]
    public string? FactoryLocation { get; set; }

    public long? MasterGroupId { get; set; }
    public Guid? TenantGroupId { get; set; }

    public virtual TenantGroup? TenantGroup { get; set; }
    public virtual ICollection<BillingItem>? BillingItems { get; set; }
    public virtual ICollection<ZoneDetail>? VesselTransactions { get; set; }



}