﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Imip.Ekb.BoundedZone.ExportVessels;
using Imip.Ekb.BoundedZone.ImportVessels;
using Imip.Ekb.BoundedZone.LocalVessels;
using Volo.Abp.Domain.Entities.Auditing;

namespace Imip.Ekb.Master.Cargos;

[Table("M_CARGO")]
public class Cargo : FullAuditedAggregateRoot<Guid>
{
    [Key]
    public int DocEntry { get; set; }

    [StringLength(255)]
    public string Name { get; set; } = null!;

    [Column("Created_by")]
    [StringLength(255)]
    public string CreatedBy { get; set; } = null!;

    [Column("created_at", TypeName = "datetime")]
    public DateTime? CreatedAt { get; set; }

    [Column("updated_at", TypeName = "datetime")]
    public DateTime? UpdatedAt { get; set; }

    [StringLength(255)]
    public string Status { get; set; } = null!;

    [StringLength(100)]
    public string? Alias { get; set; }

    [StringLength(200)]
    public string? Flag { get; set; }

    [Column(TypeName = "decimal(20, 4)")]
    public decimal GrossWeight { get; set; }

    [StringLength(255)]
    public string? Type { get; set; }

    [Column(TypeName = "decimal(20, 4)")]
    public decimal? LoaQty { get; set; }

    // Maps to the rowversion column



    public virtual ICollection<ImportVessel>? ImportVessels { get; set; }
    public virtual ICollection<ExportVessel>? ExportVessels { get; set; }
    public virtual ICollection<LocalVessel>? LocalVessels { get; set; }
    public virtual ICollection<LocalVessel>? LocalBarges { get; set; }
}
