using System;
using System.ComponentModel.DataAnnotations;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Entities;

namespace Imip.Ekb.Master.Jetties.Dtos;

public class JettyCreateUpdateDto : EntityDto<Guid>, IHasConcurrencyStamp
{
    public string ConcurrencyStamp { get; set; }
    // [Required]
    // public int DocEntry { get; set; }

    [Required]
    [StringLength(255)]
    public string Name { get; set; } = string.Empty;

    [Required]
    [StringLength(255)]
    public string Alias { get; set; } = string.Empty;

    [Required]
    public decimal Max { get; set; }

    [Required]
    [StringLength(5)]
    public string Deleted { get; set; } = string.Empty;

    [Required]
    public int CreatedBy { get; set; }

    [Required]
    public int UpdatedBy { get; set; }

    [StringLength(255)]
    public string? Port { get; set; }

    public bool? IsCustomArea { get; set; } = false;
}