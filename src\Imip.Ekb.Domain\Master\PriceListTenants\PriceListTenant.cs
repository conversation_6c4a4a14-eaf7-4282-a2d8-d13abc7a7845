﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Volo.Abp.Domain.Entities.Auditing;

namespace Imip.Ekb.Master.PriceListTenants;

[Table("MPLTENANT")]
public class PriceListTenant : FullAuditedAggregateRoot<Guid>
{
    [Key]
    public int DocEntry { get; set; }

    public DateOnly PeriodStart { get; set; }

    public DateOnly PeriodEnd { get; set; }

    public int TenantKey { get; set; }

    [Column(TypeName = "decimal(20, 4)")]
    public decimal Price { get; set; }

    public int Currency { get; set; }

    public int CreatedBy { get; set; }

    public int UpdatedBy { get; set; }

    [Column("created_at", TypeName = "datetime")]
    public DateTime? CreatedAt { get; set; }

    [Column("updated_at", TypeName = "datetime")]
    public DateTime? UpdatedAt { get; set; }

    [StringLength(255)]
    public string? BilledBy { get; set; }



}
