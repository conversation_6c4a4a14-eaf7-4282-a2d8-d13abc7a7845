﻿using System;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Entities;

namespace Imip.Ekb.Master.ItemClassifications.Dtos;

public class ItemClassificationDto : AuditedEntityDto<Guid>, IHasConcurrencyStamp
{
    public string ConcurrencyStamp { get; set; }
    public int DocEntry { get; set; }

    public string Name { get; set; } = null!;

    public string? ReportType { get; set; }

    public string Deleted { get; set; } = null!;

    public int? CreatedBy { get; set; }

    public int? UpdatedBy { get; set; }

    public DateTime? CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }

    public long? Category { get; set; }
}
