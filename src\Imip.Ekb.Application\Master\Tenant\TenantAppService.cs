using System;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using Imip.Ekb.Mapping.Mappers;
using Imip.Ekb.Master.Tenants.Dtos;
using Imip.Ekb.Models;
using Imip.Ekb.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.Ekb.Master.Tenants;

[Authorize]
public class TenantAppService :
    CrudAppService<MasterTenant, MasterTenantDto, Guid, PagedAndSortedResultRequestDto, TenantCreateUpdateDto, TenantCreateUpdateDto>,
    ITenantAppService
{
    private readonly ITenantRepository _masterTenantRepository;
    private readonly TenantMapper _mapper;
    private readonly ILogger<TenantAppService> _logger;

    public TenantAppService(
        ITenantRepository masterTenantRepository,
        TenantM<PERSON>per mapper,
        ILogger<TenantAppService> logger)
        : base(masterTenantRepository)
    {
        _masterTenantRepository = masterTenantRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public override async Task<MasterTenantDto> CreateAsync(TenantCreateUpdateDto input)
    {
        await CheckCreatePolicyAsync();
        var entity = _mapper.CreateEntityWithId(input, Guid.NewGuid());
        entity.CreatedAt = Clock.Now;
        await _masterTenantRepository.InsertAsync(entity, autoSave: true);
        return _mapper.MapToDto(entity);
    }

    public override async Task<MasterTenantDto> UpdateAsync(Guid id, TenantCreateUpdateDto input)
    {
        await CheckUpdatePolicyAsync();
        var entity = await _masterTenantRepository.GetAsync(id);
        var originalCreatedBy = entity.CreatedBy;
        var originalCreatedAt = entity.CreatedAt;
        _mapper.MapToEntity(input, entity);
        entity.CreatedBy = originalCreatedBy;
        entity.CreatedAt = originalCreatedAt;
        entity.UpdatedAt = Clock.Now;
        await _masterTenantRepository.UpdateAsync(entity, autoSave: true);
        return _mapper.MapToDto(entity);
    }

    public override async Task DeleteAsync(Guid id)
    {
        await CheckDeletePolicyAsync();
        var entity = await _masterTenantRepository.GetAsync(id);
        entity.UpdatedAt = Clock.Now;
        await _masterTenantRepository.UpdateAsync(entity, autoSave: true);
    }

    public override async Task<MasterTenantDto> GetAsync(Guid id)
    {
        await CheckGetPolicyAsync();
        var entity = await _masterTenantRepository.GetAsync(id);
        return _mapper.MapToDto(entity);
    }

    public override async Task<PagedResultDto<MasterTenantDto>> GetListAsync(PagedAndSortedResultRequestDto input)
    {
        await CheckGetListPolicyAsync();
        var queryable = await _masterTenantRepository.GetQueryableWithIncludesAsync();
        var sortedQueryable = queryable.OrderBy(input.Sorting.IsNullOrWhiteSpace() ? nameof(MasterTenant.DocEntry) : input.Sorting);
        var totalCount = await AsyncExecuter.CountAsync(queryable);
        var entities = await AsyncExecuter.ToListAsync(
            sortedQueryable.PageBy(input.SkipCount, input.MaxResultCount)
        );
        var dtos = entities.Select(_mapper.MapToDto).ToList();
        return new PagedResultDto<MasterTenantDto>(totalCount, dtos);
    }

    public virtual async Task<PagedResultDto<MasterTenantDto>> FilterListAsync(QueryParametersDto parameters)
    {
        // Apply caching for frequently accessed data
        var cacheKey = $"tenants_{parameters.Page}_{parameters.MaxResultCount}_{parameters.GetHashCode()}";

        // Check cache first (implement IDistributedCache)
        // var cachedResult = await _cache.GetAsync<PagedResultDto<MasterTenantDto>>(cacheKey);
        // if (cachedResult != null) return cachedResult;

        var query = await _masterTenantRepository.GetOptimizedQueryableAsync();

        // Apply filters before projection
        query = ApplyDynamicQuery(query, parameters);

        // Get total count efficiently (before projection)
        var totalCount = await AsyncExecuter.CountAsync(query);

        // Apply pagination before projection to reduce memory usage
        var pagedQuery = query
            .Skip(parameters.SkipCount)
            .Take(parameters.MaxResultCount);

        // Use direct projection to avoid loading full entities
        var items = await AsyncExecuter.ToListAsync(
            pagedQuery
            .Select(TenantMapper.ProjectionExpression)
        );

        var result = new PagedResultDto<MasterTenantDto>
        {
            TotalCount = totalCount,
            Items = items
        };

        // Cache the result for 5 minutes
        // await _cache.SetAsync(cacheKey, result, TimeSpan.FromMinutes(5));

        return result;
    }

    private IQueryable<MasterTenant> ApplyDynamicQuery(IQueryable<MasterTenant> query, QueryParametersDto parameters)
    {
        if (parameters.FilterGroup?.Conditions != null && parameters.FilterGroup.Conditions.Count > 0)
        {
            query = DynamicQueryBuilder<MasterTenant>.ApplyFilters(query, parameters.FilterGroup);
        }
        if (parameters.Sort?.Count > 0)
        {
            query = DynamicQueryBuilder<MasterTenant>.ApplyMultipleSorting(query, parameters.Sort);
        }
        else if (!string.IsNullOrEmpty(parameters.Sorting))
        {
            query = query.OrderBy(parameters.Sorting);
        }
        else
        {
            query = query.OrderByDescending(x => x.CreationTime);
        }
        return query;
    }
}