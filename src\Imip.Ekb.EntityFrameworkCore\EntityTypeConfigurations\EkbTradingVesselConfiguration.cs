using Imip.Ekb.BoundedZone.TradingVessels;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Volo.Abp.EntityFrameworkCore.Modeling;

namespace Imip.Ekb.EntityTypeConfigurations;

public class EkbTradingVesselConfiguration : IEntityTypeConfiguration<TradingVessel>
{
    private readonly string? prefix;
    private readonly string? schema;

    public EkbTradingVesselConfiguration(string? prefix, string? schema)
    {
        this.prefix = prefix;
        this.schema = schema;
    }

    public void Configure(EntityTypeBuilder<TradingVessel> b)
    {
        b.ToTable("R_Master", EkbConsts.DbSchema);
        b.ConfigureByConvention();


        // Configure Items collection using HeaderId as foreign key (not creating columns on vessel side)
        b.<PERSON><PERSON>any(x => x.Items)
            .WithOne(x => x.TradingVessel)
            .HasForeignKey(x => x.HeaderId)
            .HasPrincipalKey(z => z.Id)
            .IsRequired(false)
            .OnDelete(DeleteBehavior.NoAction);
    }
}
