﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Imip.Ekb.Attachments;
using Imip.Ekb.BoundedZone.ExportVessels;
using Imip.Ekb.BoundedZone.ImportVessels;
using Imip.Ekb.BoundedZone.ImportVessels.ZoneDetailInvoice;
using Imip.Ekb.BoundedZone.LocalVessels;
using Imip.Ekb.BoundedZone.TradingVessels;
using Imip.Ekb.BoundedZone.TradingVessels.TradingInvoice;
using Imip.Ekb.Master.Agents;
using Imip.Ekb.Master.BcTypes;
using Imip.Ekb.Master.BusinessPartners;
using Imip.Ekb.Master.ExportClassifications;
using Imip.Ekb.Master.Tenants;
using Volo.Abp.Domain.Entities.Auditing;

namespace Imip.Ekb.BoundedZone;

[Table("T_MDOC")]
public class ZoneDetail : FullAuditedAggregateRoot<Guid>
{
    [Key]
    public int DocEntry { get; set; }

    [Column("BC_type_key")]
    public int? BcTypeKey { get; set; }

    [Column("Tenant_key")]
    public int? TenantKey { get; set; }

    [Column("BP")]
    [StringLength(255)]
    public string? BP { get; set; }

    [StringLength(255)]
    public string? Cargo { get; set; }

    [Column(TypeName = "decimal(20, 2)")]
    public decimal? Weight { get; set; }

    [Column("BL_No")]
    [StringLength(255)]
    public string? BlNo { get; set; }

    [Column("BL_date")]
    public DateOnly? BlDate { get; set; }

    [Column("AJU_No")]
    [StringLength(255)]
    public string? AjuNo { get; set; }

    [Column("REG_No")]
    [StringLength(255)]
    public string? RegNo { get; set; }

    [Column("REG_date")]
    public DateOnly? RegDate { get; set; }

    [Column("SPPB_No")]
    [StringLength(255)]
    public string? SppbNo { get; set; }

    [Column("SPPB_date")]
    public DateOnly? SppbDate { get; set; }

    [Column("SPPD_No")]
    [StringLength(255)]
    public string? SppdNo { get; set; }

    [Column("SPPD_date")]
    public DateOnly? SppdDate { get; set; }

    [Column("Shipement")]
    [StringLength(255)]
    public string? Shipment { get; set; }

    public string? Remarks { get; set; }

    [Column("Created_by")]
    [StringLength(255)]
    public string CreatedBy { get; set; } = null!;

    [Column("created_at", TypeName = "datetime")]
    public DateTime? CreatedAt { get; set; }

    [Column("updated_at", TypeName = "datetime")]
    public DateTime? UpdatedAt { get; set; }

    [Column("Created_id")]
    public int? CreatedId { get; set; }

    [Column("Updated_by")]
    [StringLength(255)]
    public string? UpdatedBy { get; set; }

    [Column("Updated_id")]
    public int? UpdatedId { get; set; }

    [StringLength(255)]
    public string? Color { get; set; }

    [Column("sap_kb_entry")]
    public int? SapKbEntry { get; set; }

    [Column("No_bl")]
    [StringLength(255)]
    public string? NoBl { get; set; }

    [Column("Date_bl")]
    public DateOnly? DateBl { get; set; }

    [Column("No_inv")]
    [StringLength(255)]
    public string? NoInv { get; set; }

    [Column("Date_inv")]
    public DateOnly? DateInv { get; set; }

    [Column("Shipement_no")]
    [StringLength(255)]
    public string? ShipmentNo { get; set; }

    [Column("Ebilling_date")]
    public DateOnly? EbillingDate { get; set; }

    [StringLength(255)]
    public string? Skep { get; set; }

    [Column("Skep_date")]
    public DateOnly? SkepDate { get; set; }

    [Column("PIB_no")]
    [StringLength(255)]
    public string? PibNo { get; set; }

    [Column("PIB_date")]
    public DateOnly? PibDate { get; set; }

    [Column("Vessel_arrive")]
    public DateOnly? VesselArrive { get; set; }

    [Column("Expired_date")]
    public DateOnly? ExpiredDate { get; set; }

    public string? Item { get; set; }

    [Column(TypeName = "decimal(20, 2)")]
    public decimal? Qty { get; set; }

    [Column(TypeName = "decimal(20, 2)")]
    public decimal? Amount { get; set; }

    [StringLength(255)]
    public string? Status { get; set; }

    [Column("Site_status")]
    [StringLength(255)]
    public string? SiteStatus { get; set; }

    public int? DocNum { get; set; }

    [StringLength(10)]
    public string? Flags { get; set; }

    [Column("Ocean_freight")]
    [StringLength(255)]
    public string? OceanFreight { get; set; }

    [StringLength(255)]
    public string? Currency { get; set; }

    [StringLength(255)]
    public string? Ocean { get; set; }

    [Column("CBMB")]
    [StringLength(255)]
    public string? Cbmb { get; set; }

    [Column("Freight_value", TypeName = "decimal(20, 4)")]
    public decimal? FreightValue { get; set; }

    [StringLength(255)]
    public string? Attachment { get; set; }

    [Column("Post_date")]
    public DateOnly? PostDate { get; set; }

    [StringLength(100)]
    public string DocType { get; set; } = null!;

    [Column("isScan")]
    [StringLength(10)]
    public string IsScan { get; set; } = null!;

    [Column("isOriginal")]
    [StringLength(10)]
    public string IsOriginal { get; set; } = null!;

    [Column("isSend")]
    [StringLength(10)]
    public string IsSend { get; set; } = null!;

    [Column("isFeOri")]
    [StringLength(10)]
    public string IsFeOri { get; set; } = null!;

    [Column("isFeSend")]
    [StringLength(10)]
    public string IsFeSend { get; set; } = null!;

    [StringLength(100)]
    public string? SecretKey { get; set; }

    [Column("PPJK")]
    public int? Ppjk { get; set; }

    [Column("PPJKCodeTemp")]
    [StringLength(20)]
    public string? PpjkcodeTemp { get; set; }

    [StringLength(255)]
    public string? PortOfLoading { get; set; }

    [Column("EmailToPPJK")]
    public DateOnly? EmailToPpjk { get; set; }

    [StringLength(255)]
    public string? LetterNo { get; set; }

    public string? ItemName { get; set; }

    [Column(TypeName = "numeric(20, 5)")]
    public decimal? ItemQty { get; set; }

    [StringLength(20)]
    public string? UnitQty { get; set; }

    [Column(TypeName = "numeric(20, 6)")]
    public decimal? GrossWeight { get; set; }

    [StringLength(20)]
    public string? UnitWeight { get; set; }

    [StringLength(100)]
    public string? MatchKey { get; set; }

    [Column("BPNum")]
    public int? Bpnum { get; set; }

    public int? CargoNum { get; set; }

    public int? LineNum { get; set; }

    [Column("isChange")]
    [StringLength(10)]
    public string IsChange { get; set; } = null!;

    [StringLength(10)]
    public string Deleted { get; set; } = null!;

    [Column("SPPB_update_date")]
    public DateOnly? SppbUpdateDate { get; set; }

    [Column("SPPB_No_update")]
    [StringLength(50)]
    public string? SppbNoUpdate { get; set; }

    [Column("SPPB_Date_update", TypeName = "datetime")]
    public DateTime? SppbDateUpdate { get; set; }

    [Column("SPPD_No_update")]
    [StringLength(50)]
    public string? SppdNoUpdate { get; set; }

    [Column("SPPD_Date_update", TypeName = "datetime")]
    public DateTime? SppdDateUpdate { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? DeletedAt { get; set; }

    [StringLength(200)]
    public string? DeleteBy { get; set; }

    [Column("eBilling_No")]
    [StringLength(80)]
    public string? EBillingNo { get; set; }

    [Column("contract_no")]
    [StringLength(255)]
    public string? ContractNo { get; set; }

    [StringLength(100)]
    public string? OpenDate { get; set; }

    [StringLength(100)]
    public string? UpdateDate { get; set; }

    [StringLength(255)]
    public string? InternalCode { get; set; }

    public DateOnly? ContractDate { get; set; }

    [StringLength(255)]
    public string? RegType { get; set; }

    [Column("CBM", TypeName = "decimal(20, 4)")]
    public decimal? Cbm { get; set; }

    [StringLength(255)]
    public string? Notification { get; set; }

    [Column("SPPBStatus")]
    [StringLength(255)]
    public string? Sppbstatus { get; set; }

    public int? Agent { get; set; }

    public int? BillingId { get; set; }

    [StringLength(100)]
    public string? InsuranceCurrency { get; set; }

    [Column(TypeName = "decimal(20, 4)")]
    public decimal? InsuranceValue { get; set; }

    public int? DestinationPortId { get; set; }

    [Column(TypeName = "numeric(20, 6)")]
    public decimal? NetWeight { get; set; }

    [Column(TypeName = "decimal(20, 4)")]
    public decimal? UnitPrice { get; set; }

    [Column(TypeName = "decimal(20, 4)")]
    public decimal? TotalInv { get; set; }

    [Column(TypeName = "decimal(20, 4)")]
    public decimal? QtyEstimate { get; set; }

    [Column(TypeName = "decimal(20, 4)")]
    public decimal? PriceEstimate { get; set; }

    [StringLength(200)]
    public string? BillingType { get; set; }

    [StringLength(200)]
    public string? ChargeTo { get; set; }

    [Column(TypeName = "decimal(20, 4)")]
    public decimal? QtyRevised { get; set; }

    [Column(TypeName = "decimal(20, 4)")]
    public decimal? PriceRevised { get; set; }

    [StringLength(255)]
    public string? NoNota { get; set; }

    [Column(TypeName = "decimal(20, 4)")]
    public decimal? TotalEstimate { get; set; }

    [Column(TypeName = "decimal(20, 4)")]
    public decimal? TotalRevised { get; set; }

    [Column("serial_number")]
    [StringLength(255)]
    public string? SerialNumber { get; set; }

    [Column("serial_number1")]
    [StringLength(200)]
    public string? SerialNumber1 { get; set; }

    [Column("serial_number2")]
    [StringLength(200)]
    public string? SerialNumber2 { get; set; }

    [Column("serial_number3")]
    [StringLength(200)]
    public string? SerialNumber3 { get; set; }

    [Column("serial_number4")]
    [StringLength(200)]
    public string? SerialNumber4 { get; set; }

    [Column("serial_number5")]
    [StringLength(200)]
    public string? SerialNumber5 { get; set; }

    [Column("serial_number6")]
    [StringLength(200)]
    public string? SerialNumber6 { get; set; }

    [StringLength(255)]
    public string? IsParent { get; set; }

    [Column(TypeName = "decimal(20, 5)")]
    public decimal? GrtVessel { get; set; }

    [StringLength(255)]
    public string? NpwpBp { get; set; }

    public int? EsignDecimal { get; set; }

    public long? CargoId { get; set; }

    public long? BargeId { get; set; }

    [StringLength(255)]
    public string? Voyage { get; set; }

    [StringLength(255)]
    public string? VesselName { get; set; }

    [StringLength(100)]
    public string? ProcessName { get; set; }

    [Column(TypeName = "decimal(20, 2)")]
    public decimal? Rate { get; set; }

    [Column("BM", TypeName = "decimal(20, 2)")]
    public decimal? Bm { get; set; }

    [Column("PPN", TypeName = "decimal(20, 2)")]
    public decimal? Ppn { get; set; }

    [Column("PPH", TypeName = "decimal(20, 2)")]
    public decimal? Pph { get; set; }

    [Column("BMAD", TypeName = "decimal(20, 2)")]
    public decimal? Bmad { get; set; }

    [Column("BMTP", TypeName = "decimal(20, 2)")]
    public decimal? Bmtp { get; set; }

    [StringLength(255)]
    public string? FormType { get; set; }

    public DateOnly? BillingDate { get; set; }

    [StringLength(10)]
    public string IsUrgent { get; set; } = null!;

    public long? SurveyorId { get; set; }

    [StringLength(255)]
    public string? SurveyorName { get; set; }

    public DateOnly? EmailToBcDate { get; set; }

    [Column(TypeName = "decimal(20, 3)")]
    public decimal? Container { get; set; }

    [StringLength(26)]
    public string? ExportClassificationId { get; set; }

    public long? WarehouseId { get; set; }

    [Column(TypeName = "decimal(12, 3)")]
    public decimal? IncreaseValue { get; set; }

    [Column(TypeName = "decimal(12, 3)")]
    public decimal? DecreaseValue { get; set; }

    [Column("IncreaseValuePPN", TypeName = "decimal(12, 3)")]
    public decimal? IncreaseValuePpn { get; set; }

    [Column("DecreaseValuePPN", TypeName = "decimal(12, 3)")]
    public decimal? DecreaseValuePpn { get; set; }

    [Column("IncreaseValuePPH", TypeName = "decimal(12, 3)")]
    public decimal? IncreaseValuePph { get; set; }

    [Column("DecreaseValuePPH", TypeName = "decimal(12, 3)")]
    public decimal? DecreaseValuePph { get; set; }

    public string? RepairLocation { get; set; }

    public long? RepresentativeId { get; set; }

    public long? InvoiceDetailId { get; set; }

    [Column(TypeName = "decimal(10, 2)")]
    public decimal? CostOfRepair { get; set; }

    [StringLength(10)]
    public string? ItemCategoryCode { get; set; }

    [StringLength(150)]
    public string? ItemCategoryDescription { get; set; }

    [StringLength(100)]
    public string? SapBillingStatus { get; set; }

    public Guid? TenantId { get; set; }
    public Guid? BcTypeId { get; set; }
    public Guid? HeaderId { get; set; }
    public Guid? BusinessPartnerId { get; set; }
    public Guid? AgentId { get; set; }
    public Guid? MasterExportClassificationId { get; set; }
    public DateOnly? LetterDate { get; set; }

    [StringLength(200)]
    public string? ShippingInstructionNo { get; set; }
    public DateOnly? ShippingInstructionDate { get; set; }

    public virtual MasterTenant? Tenant { get; set; }
    public virtual BcType? BcType { get; set; }
    public virtual Agent? MasterAgent { get; set; }
    public virtual BusinessPartner? BusinessPartner { get; set; }
    public virtual ExportClassification? ExportClassification { get; set; }
    public virtual ICollection<DocAttachment>? DocAttachment { get; set; }

    // Vessel navigation properties using HeaderId (GUID) for relationships
    // Keep DocEntry as PK, but use HeaderId for foreign key relationships
    public virtual ImportVessel? ImportVessel { get; set; }
    public virtual ExportVessel? ExportVessel { get; set; }
    public virtual LocalVessel? LocalVessel { get; set; }
    public virtual TradingVessel? TradingVessel { get; set; }
    public virtual ICollection<ZoneDetailInvoice>? ImportInvoices { get; set; }
    public virtual ICollection<TradingInvoice>? TradingInvoices { get; set; }




    protected ZoneDetail()
    { }
}
