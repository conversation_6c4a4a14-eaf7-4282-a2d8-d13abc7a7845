﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace Imip.Ekb.Models;
[JsonConverter(typeof(JsonStringEnumConverter))]
public enum FilterOperator
{
    Equals,
    NotEquals,
    Contains,
    StartsWith,
    EndsWith,
    GreaterThan,
    GreaterThanOrEqual,
    LessThan,
    LessThanOrEqual,
    In,
    NotIn,
    Between,
    NotBetween,
    IsNull,
    IsNotNull,
    IsEmpty,
    IsNotEmpty,
    IsTrue,
    IsFalse,
    IsNullOrEmpty,
    IsNotNullOrEmpty,
    IsNullOrWhiteSpace,
    IsNotNullOrWhiteSpace,
    IsNumeric,
    IsAlpha,
    IsAlphaNumeric,
    IsEmail,
    IsUrl,
    IsIp,
    IsIpv4,
    IsIpv6,
    IsGuid,
    IsGuidEmpty,
    IsGuidNotEmpty,
    IsGuidNull,
    IsGuidNotNull,
    IsGuidNullOrEmpty,
    IsGuidNotNullOrEmpty,
    IsGuidNullOrWhiteSpace,
    IsGuidNotNullOrWhiteSpace,
    IsGuidNumeric,
    IsGuidAlpha,
    IsGuidAlphaNumeric,
}

[JsonConverter(typeof(JsonStringEnumConverter))]
public enum LogicalOperator
{
    And,
    Or
}

public class FilterCondition
{
    [Required]
    public string FieldName { get; set; } = string.Empty;

    [Required]
    [EnumDataType(typeof(FilterOperator))]  // Fix: Changed from LogicalOperator to FilterOperator
    public FilterOperator Operator { get; set; }

    [Required]
    public object Value { get; set; } = string.Empty;
}

public class FilterGroup
{
    [Required]
    [EnumDataType(typeof(LogicalOperator))]
    public LogicalOperator Operator { get; set; }

    [Required]
    public List<FilterCondition> Conditions { get; set; } = [];
}

public class SortInfo
{
    public string Field { get; set; } = string.Empty;
    public bool Desc { get; set; }
}


public class QueryParameters
{
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 10;
    public string? Sorting { get; set; }

    // New sorting implementation
    public List<SortInfo> Sort { get; set; } = [];
    public FilterGroup? FilterGroup { get; set; }

    // Calculated properties
    public int SkipCount => (Page - 1) * PageSize;
    public int MaxResultCount => PageSize;
}