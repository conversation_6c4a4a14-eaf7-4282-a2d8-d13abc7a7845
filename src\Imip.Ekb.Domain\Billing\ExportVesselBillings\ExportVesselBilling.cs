﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Imip.Ekb.Billing.LocalVesselBillings;
using Imip.Ekb.Master.Jetties;
using Volo.Abp.Domain.Entities.Auditing;

namespace Imip.Ekb.Billing.ExportVesselBillings;

[Table("BHEXP")]
public class ExportVesselBilling : FullAuditedAggregateRoot<Guid>
{
    [Key]
    public int DocEntry { get; set; }

    [StringLength(255)]
    public string PortService { get; set; } = null!;

    [StringLength(255)]
    public string Status { get; set; } = null!;

    [StringLength(50)]
    public string YearArrival { get; set; } = null!;

    public int Jetty { get; set; }

    [Column("ExportID")]
    public int ExportId { get; set; }

    public int CreatedBy { get; set; }

    public int UpdatedBy { get; set; }

    [Column("created_at", TypeName = "datetime")]
    public DateTime? CreatedAt { get; set; }

    [Column("updated_at", TypeName = "datetime")]
    public DateTime? UpdatedAt { get; set; }

    [StringLength(255)]
    public string? DocNum { get; set; }

    public DateOnly? PostingDate { get; set; }

    public string? Remarks { get; set; }

    [StringLength(100)]
    public string Type { get; set; } = null!;

    public DateOnly? PeriodDate { get; set; }

    [Column(TypeName = "decimal(20, 4)")]
    public decimal? DeadWeight { get; set; }

    public DateOnly? BillingNoteDate { get; set; }

    public Guid? JettyId { get; set; }
    public virtual Jetty? MasterJetty { get; set; }




    public virtual ICollection<BillingItem>? Items { get; set; }
}
