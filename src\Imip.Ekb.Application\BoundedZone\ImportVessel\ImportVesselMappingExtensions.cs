using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Imip.Ekb.Attachments;
using Imip.Ekb.BoundedZone.Dtos;
using Imip.Ekb.Mapping.Mappers;
using Imip.Ekb.Master.Agents;
using Imip.Ekb.Master.Agents.Dtos;
using Imip.Ekb.Master.BcTypes;
using Imip.Ekb.Master.BusinessPartners;
using Imip.Ekb.Master.Cargos;
using Imip.Ekb.Master.Cargos.Dtos;
using Imip.Ekb.Master.DestinationPorts;
using Imip.Ekb.Master.DestinationPorts.Dtos;
using Imip.Ekb.Master.Jetties;
using Imip.Ekb.Master.Jetties.Dtos;
using Imip.Ekb.Master.PortOfLoadings;
using Imip.Ekb.Master.PortOfLoadings.Dtos;
using Imip.Ekb.Master.Surveyors;
using Imip.Ekb.Master.Surveyors.Dtos;
using Imip.Ekb.Master.Tenants;
using Imip.Ekb.Master.Tradings;
using Imip.Ekb.Master.Tradings.Dtos;
using Microsoft.Extensions.Configuration;
using ImportVesselEntity = Imip.Ekb.BoundedZone.ImportVessels.ImportVessel;

namespace Imip.Ekb.BoundedZone.ImportVessel;

public static class ImportVesselMappingExtensions
{
    public static ImportVesselWithItemsDto MapToWithItemsDto(this ImportVesselEntity entity, ImportVesselMapper vesselMapper, IConfiguration configuration)
    {
        if (entity == null) return null;

        return new ImportVesselWithItemsDto
        {
            // Base properties
            Id = entity.Id,
            ConcurrencyStamp = entity.ConcurrencyStamp,
            DocEntry = entity.DocEntry,
            DocNum = entity.DocNum,
            PostingDate = entity.PostingDate,
            Bp = entity.Bp,
            VesselName = entity.Vessel?.Name,
            Shipment = entity.Shipment,
            ShipmentNo = entity.ShipmentNo,
            VesselArrival = entity.VesselArrival,
            CreatedBy = entity.CreatedBy,
            UpdatedBy = entity.UpdatedBy,
            CreatedAt = entity.CreatedAt,
            UpdatedAt = entity.UpdatedAt,
            Color = entity.Color,
            Flags = entity.Flags,
            Remarks = entity.Remarks,
            Status = entity.Status,
            IsLocked = entity.IsLocked,
            IsChange = entity.IsChange,
            TransType = entity.TransType,
            DocType = entity.DocType,
            BcType = entity.BcType,
            PortOrigin = entity.PortOrigin,
            EmailToPpjk = entity.EmailToPpjk,
            MatchKey = entity.MatchKey,
            Voyage = entity.Voyage,
            Deleted = entity.Deleted,
            DocStatus = entity.DocStatus,
            GrossWeight = entity.GrossWeight,
            VesselFlag = entity.VesselFlag,
            VesselDeparture = entity.VesselDeparture,
            VesselStatus = entity.VesselStatus,
            Jetty = entity.Jetty,
            DestinationPort = entity.DestinationPort,
            BerthingDate = entity.BerthingDate,
            AnchorageDate = entity.AnchorageDate,
            Type = entity.Type,
            JettyUpdate = entity.JettyUpdate,
            ReportDate = entity.ReportDate,
            UnloadingDate = entity.UnloadingDate,
            FinishUnloadingDate = entity.FinishUnloadingDate,
            GrtWeight = entity.GrtWeight,
            InvoiceStatus = entity.InvoiceStatus,
            AgentId = entity.AgentId,
            AgentName = entity.AgentName,
            StatusBms = entity.StatusBms,
            SurveyorId = entity.SurveyorId,
            TradingId = entity.TradingId,
            JettyId = entity.JettyId,
            VesselId = entity.VesselId,
            MasterAgentId = entity.MasterAgentId,
            MasterTradingId = entity.MasterTradingId,
            MasterSurveyorId = entity.MasterSurveyorId,
            AsideDate = entity.AsideDate,
            CastOfDate = entity.CastOfDate,
            PortOriginId = entity.PortOriginId,
            DestinationPortId = entity.DestinationPortId,

            // Navigation properties - using null-conditional operators for cleaner code
            MasterJetty = entity.MasterJetty?.MapToJettyDto(),
            Vessel = entity.Vessel?.MapToCargoDto(),
            MasterAgent = entity.MasterAgent?.MapToAgentDto(),
            MasterTrading = entity.MasterTrading?.MapToTradingDto(),
            MasterSurveyor = entity.MasterSurveyor?.MapToSurveyorDto(),
            MasterPortOrigin = entity.MasterPortOrigin?.MapToPortOfLoadingDto(),
            MasterDestinationPort = entity.MasterDestinationPort?.MapToDestinationPortDto(),

            // Items mapping
            Items = entity.Items?.Select(zoneDetail => zoneDetail.MapToVesselItemDto(configuration)).ToList() ?? new List<VesselItemDto>()
        };
    }
}

// Extension methods for navigation property mapping
public static class NavigationMappingExtensions
{
    public static JettyDto MapToJettyDto(this Jetty jetty) =>
        new()
        {
            Id = jetty.Id,
            Name = jetty.Name,
            Alias = jetty.Alias,
            Max = jetty.Max,
            Port = jetty.Port,
            IsCustomArea = jetty.IsCustomArea,
        };

    public static CargoDto MapToCargoDto(this Cargo cargo) =>
        new()
        {
            Id = cargo.Id,
            Name = cargo.Name,
            Alias = cargo.Alias,
            Flag = cargo.Flag,
            GrossWeight = cargo.GrossWeight,
            Type = cargo.Type,
            LoaQty = cargo.LoaQty,
        };

    public static AgentDto MapToAgentDto(this Agent agent) =>
        new()
        {
            Id = agent.Id,
            Name = agent.Name,
            Status = agent.Status,
            Type = agent.Type,
            NpwpNo = agent.NpwpNo,
            BdmSapcode = agent.BdmSapcode,
            TaxCode = agent.TaxCode,
            AddressNpwp = agent.AddressNpwp,
            Address = agent.Address,
            SapcodeS4 = agent.SapcodeS4,
        };

    public static TradingDto MapToTradingDto(this Trading trading) =>
        new()
        {
            Id = trading.Id,
            Name = trading.Name,
            Address = trading.Address,
            Npwp = trading.Npwp,
        };

    public static SurveyorDto MapToSurveyorDto(this Surveyor surveyor) =>
        new()
        {
            Id = surveyor.Id,
            Name = surveyor.Name,
            Address = surveyor.Address,
            Npwp = surveyor.Npwp,
        };

    public static PortOfLoadingDto MapToPortOfLoadingDto(this PortOfLoading port) =>
        new()
        {
            Id = port.Id,
            Name = port.Name,
            DocType = port.DocType,
        };

    public static DestinationPortDto MapToDestinationPortDto(this DestinationPort port) =>
        new()
        {
            Id = port.Id,
            Name = port.Name,
            DocType = port.DocType,
        };

    public static VesselItemDto MapToVesselItemDto(this ZoneDetail zoneDetail, IConfiguration configuration) =>
        new()
        {
            Id = zoneDetail.Id,
            ConcurrencyStamp = zoneDetail.ConcurrencyStamp,
            DocEntry = zoneDetail.DocEntry,
            DocNum = zoneDetail.DocNum ?? 0,
            TenantName = zoneDetail.Tenant?.Name,
            ItemName = zoneDetail.ItemName,
            ItemQty = zoneDetail.ItemQty,
            UnitQty = zoneDetail.UnitQty,
            Cargo = zoneDetail.Cargo,
            Shipment = zoneDetail.Shipment,
            Remarks = zoneDetail.Remarks,
            NoBl = zoneDetail.NoBl,
            DateBl = zoneDetail.DateBl,
            AjuNo = zoneDetail.AjuNo,
            RegNo = zoneDetail.RegNo,
            RegDate = zoneDetail.RegDate,
            SppbNo = zoneDetail.SppbNo,
            SppbDate = zoneDetail.SppbDate,
            SppdNo = zoneDetail.SppdNo,
            SppdDate = zoneDetail.SppdDate,
            GrossWeight = zoneDetail.GrossWeight,
            UnitWeight = zoneDetail.UnitWeight,
            HeaderId = zoneDetail.HeaderId,
            LetterNo = zoneDetail.LetterNo,
            DocType = zoneDetail.DocType,
            VesselType = "ImportVessel",
            ShippingInstructionNo = zoneDetail.ShippingInstructionNo,
            ShippingInstructionDate = zoneDetail.ShippingInstructionDate,
            RegType = zoneDetail.RegType,
            Status = zoneDetail.Status,
            LetterDate = zoneDetail.LetterDate,
            TenantId = zoneDetail.TenantId,
            BcTypeId = zoneDetail.BcTypeId,
            BusinessPartnerId = zoneDetail.BusinessPartnerId,
            AgentId = zoneDetail.AgentId,
            MasterExportClassificationId = zoneDetail.MasterExportClassificationId,
            Item = zoneDetail.Item,

            // Navigation properties
            Tenant = zoneDetail.Tenant?.MapToTenantShortDto(),
            BcType = zoneDetail.BcType?.MapToBcTypeShortDto(),
            BusinessPartner = zoneDetail.BusinessPartner?.MapToBusinessPartnerShortDto(),
            MasterAgent = zoneDetail.MasterAgent?.MapToAgentShortDto(),

            // Attachments with stream URL generation
            Attachments = zoneDetail.DocAttachment?.Select(att => att.MapToDocAttachmentDto(configuration)).ToList() ?? new List<DocAttachmentSortDto>()
        };

    // Helper mapping methods for short DTOs
    public static TenantShortDto MapToTenantShortDto(this MasterTenant tenant) =>
        new()
        {
            Id = tenant.Id,
            Name = tenant.Name,
            FullName = tenant.FullName,
            DocEntry = tenant.DocEntry,
            Npwp = tenant.Npwp,
            Address = tenant.Address,
            Nib = tenant.Nib,
            Phone = tenant.Phone,
            NoAndDateNotaris = tenant.NoAndDateNotaris,
            DescNotaris = tenant.DescNotaris,
        };

    public static BcTypeShortDto MapToBcTypeShortDto(this BcType bcType) =>
        new()
        {
            Id = bcType.Id,
            Type = bcType.Type,
            TransName = bcType.TransName,
        };

    public static BusinessPartnerShortDto MapToBusinessPartnerShortDto(this BusinessPartner bp) =>
        new()
        {
            Id = bp.Id,
            Name = bp.Name,
            Npwp = bp.Npwp,
            Nitku = bp.Nitku,
            DocEntry = bp.DocEntry
        };

    public static AgentShortDto MapToAgentShortDto(this Agent agent) =>
        new()
        {
            Id = agent.Id,
            Name = agent.Name,
            Type = agent.Type,
        };

    public static DocAttachmentSortDto MapToDocAttachmentDto(this DocAttachment att, IConfiguration configuration) =>
        new()
        {
            Id = att.Id,
            DocType = att.DocType,
            TransType = att.TransType,
            Description = att.Description,
            BlobName = att.BlobName,
            ReferenceId = att.ReferenceId,
            FileName = att.FileName,
            TabName = att.TabName,
            StreamUrl = att.FilePath,
            TypePa = att.TypePa
        };

}