using Imip.Ekb.BoundedZone.ExportVessels;
using Imip.Ekb.BoundedZone.Dtos;
using Imip.Ekb.Master.Cargos;
using Imip.Ekb.Master.Jetties;
using Riok.Mapperly.Abstractions;
using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using Imip.Ekb.Master.Dtos;

namespace Imip.Ekb.Mapping.Mappers;

[Mapper]
public partial class ExportVesselMapper : IMapperlyMapper
{
    // Entity to DTO mapping
    [MapProperty(nameof(ExportVessel.Id), nameof(ExportVesselDto.Id))]
    [MapProperty(nameof(ExportVessel.DocEntry), nameof(ExportVesselDto.DocEntry))]
    [MapProperty(nameof(ExportVessel.DocNum), nameof(ExportVesselDto.DocNum))]
    [MapProperty(nameof(ExportVessel.PostingDate), nameof(ExportVesselDto.PostingDate))]
    [MapProperty(nameof(ExportVessel.VesselName), nameof(ExportVesselDto.VesselName))]
    [MapProperty(nameof(ExportVessel.VesselArrival), nameof(ExportVesselDto.VesselArrival))]
    [MapProperty(nameof(ExportVessel.VesselDeparture), nameof(ExportVesselDto.VesselDeparture))]
    [MapProperty(nameof(ExportVessel.Voyage), nameof(ExportVesselDto.Voyage))]
    [MapProperty(nameof(ExportVessel.Shipment), nameof(ExportVesselDto.Shipment))]
    [MapProperty(nameof(ExportVessel.VesselQty), nameof(ExportVesselDto.VesselQty))]
    [MapProperty(nameof(ExportVessel.PortOrigin), nameof(ExportVesselDto.PortOrigin))]
    [MapProperty(nameof(ExportVessel.DestinationPort), nameof(ExportVesselDto.DestinationPort))]
    [MapProperty(nameof(ExportVessel.Remarks), nameof(ExportVesselDto.Remarks))]
    [MapProperty(nameof(ExportVessel.Deleted), nameof(ExportVesselDto.Deleted))]
    [MapProperty(nameof(ExportVessel.CreatedBy), nameof(ExportVesselDto.CreatedBy))]
    [MapProperty(nameof(ExportVessel.UpdatedBy), nameof(ExportVesselDto.UpdatedBy))]
    [MapProperty(nameof(ExportVessel.CreatedAt), nameof(ExportVesselDto.CreatedAt))]
    [MapProperty(nameof(ExportVessel.UpdatedAt), nameof(ExportVesselDto.UpdatedAt))]
    [MapProperty(nameof(ExportVessel.DocStatus), nameof(ExportVesselDto.DocStatus))]
    [MapProperty(nameof(ExportVessel.GrossWeight), nameof(ExportVesselDto.GrossWeight))]
    [MapProperty(nameof(ExportVessel.VesselFlag), nameof(ExportVesselDto.VesselFlag))]
    [MapProperty(nameof(ExportVessel.VesselStatus), nameof(ExportVesselDto.VesselStatus))]
    [MapProperty(nameof(ExportVessel.Jetty), nameof(ExportVesselDto.Jetty))]
    [MapProperty(nameof(ExportVessel.BerthingDate), nameof(ExportVesselDto.BerthingDate))]
    [MapProperty(nameof(ExportVessel.AnchorageDate), nameof(ExportVesselDto.AnchorageDate))]
    [MapProperty(nameof(ExportVessel.ReportDate), nameof(ExportVesselDto.ReportDate))]
    [MapProperty(nameof(ExportVessel.UnloadingDate), nameof(ExportVesselDto.UnloadingDate))]
    [MapProperty(nameof(ExportVessel.FinishUnloadingDate), nameof(ExportVesselDto.FinishUnloadingDate))]
    [MapProperty(nameof(ExportVessel.DeadWeight), nameof(ExportVesselDto.DeadWeight))]
    [MapProperty(nameof(ExportVessel.GrtWeight), nameof(ExportVesselDto.GrtWeight))]
    [MapProperty(nameof(ExportVessel.InvoiceStatus), nameof(ExportVesselDto.InvoiceStatus))]
    [MapProperty(nameof(ExportVessel.AgentId), nameof(ExportVesselDto.AgentId))]
    [MapProperty(nameof(ExportVessel.AgentName), nameof(ExportVesselDto.AgentName))]
    [MapProperty(nameof(ExportVessel.StatusBms), nameof(ExportVesselDto.StatusBms))]
    [MapProperty(nameof(ExportVessel.SurveyorId), nameof(ExportVesselDto.SurveyorId))]
    [MapProperty(nameof(ExportVessel.TradingId), nameof(ExportVesselDto.TradingId))]
    [MapProperty(nameof(ExportVessel.DocType), nameof(ExportVesselDto.DocType))]
    [MapProperty(nameof(ExportVessel.JettyId), nameof(ExportVesselDto.JettyId))]
    [MapProperty(nameof(ExportVessel.VesselId), nameof(ExportVesselDto.VesselId))]
    public partial ExportVesselDto MapToDto(ExportVessel entity);

    // DTO to Entity mapping for updates (maps to existing entity)
    [MapperIgnoreTarget(nameof(ExportVessel.Id))] // Don't change existing Id
    [MapperIgnoreTarget(nameof(ExportVessel.DocEntry))] // Don't change existing DocEntry
    [MapperIgnoreTarget(nameof(ExportVessel.CreatedBy))] // Don't overwrite audit fields
    [MapperIgnoreTarget(nameof(ExportVessel.CreatedAt))]
    [MapperIgnoreTarget(nameof(ExportVessel.CreationTime))]
    [MapperIgnoreTarget(nameof(ExportVessel.CreatorId))]
    [MapperIgnoreTarget(nameof(ExportVessel.LastModificationTime))]
    [MapperIgnoreTarget(nameof(ExportVessel.LastModifierId))]
    [MapperIgnoreTarget(nameof(ExportVessel.IsDeleted))]
    [MapperIgnoreTarget(nameof(ExportVessel.DeleterId))]
    [MapperIgnoreTarget(nameof(ExportVessel.DeletionTime))]
    [MapperIgnoreTarget(nameof(ExportVessel.ExtraProperties))]
    [MapperIgnoreTarget(nameof(ExportVessel.ConcurrencyStamp))]
    [MapperIgnoreTarget(nameof(ExportVessel.MasterJetty))] // Navigation properties handled separately
    [MapperIgnoreTarget(nameof(ExportVessel.Vessel))]
    [MapperIgnoreTarget(nameof(ExportVessel.Items))]
    public partial void MapToEntity(CreateUpdateExportVesselDto dto, ExportVessel entity);

    // DTO to Entity mapping for creation
    [MapperIgnoreTarget(nameof(ExportVessel.Id))] // Will be set by EF Core
    [MapperIgnoreTarget(nameof(ExportVessel.DocEntry))] // Will be set by EF Core
    [MapperIgnoreTarget(nameof(ExportVessel.CreatedBy))] // Will be set by ABP
    [MapperIgnoreTarget(nameof(ExportVessel.CreatedAt))]
    [MapperIgnoreTarget(nameof(ExportVessel.CreationTime))]
    [MapperIgnoreTarget(nameof(ExportVessel.CreatorId))]
    [MapperIgnoreTarget(nameof(ExportVessel.LastModificationTime))]
    [MapperIgnoreTarget(nameof(ExportVessel.LastModifierId))]
    [MapperIgnoreTarget(nameof(ExportVessel.IsDeleted))]
    [MapperIgnoreTarget(nameof(ExportVessel.DeleterId))]
    [MapperIgnoreTarget(nameof(ExportVessel.DeletionTime))]
    [MapperIgnoreTarget(nameof(ExportVessel.ExtraProperties))]
    [MapperIgnoreTarget(nameof(ExportVessel.ConcurrencyStamp))]
    [MapperIgnoreTarget(nameof(ExportVessel.MasterJetty))]
    [MapperIgnoreTarget(nameof(ExportVessel.Vessel))]
    [MapperIgnoreTarget(nameof(ExportVessel.Items))]
    public partial ExportVessel MapToEntity(CreateUpdateExportVesselDto dto);

    // Custom mapping methods for complex scenarios
    public ExportVessel CreateEntityWithId(CreateUpdateExportVesselDto dto, Guid id)
    {
        // Create entity using reflection since constructor is protected
        var entity = (ExportVessel)Activator.CreateInstance(typeof(ExportVessel), true)!;

        // Set the ID using reflection
        var idProperty = typeof(ExportVessel).GetProperty("Id");
        idProperty?.SetValue(entity, id);

        // Map properties manually
        MapToEntity(dto, entity);

        return entity;
    }

    // Map list of entities to DTOs
    public partial List<ExportVesselDto> MapToDtoList(List<ExportVessel> entities);

    // Map IEnumerable for LINQ scenarios
    public partial IEnumerable<ExportVesselDto> MapToDtoEnumerable(IEnumerable<ExportVessel> entities);

    // Map with items included
    public ExportVesselWithItemsDto MapToDtoWithItems(ExportVessel entity, List<VesselItemDto> items)
    {
        var dto = MapToDto(entity);
        var withItemsDto = new ExportVesselWithItemsDto
        {
            Id = dto.Id,
            DocEntry = dto.DocEntry,
            DocNum = dto.DocNum,
            PostingDate = dto.PostingDate,
            VesselName = dto.VesselName,
            VesselArrival = dto.VesselArrival,
            VesselDeparture = dto.VesselDeparture,
            Voyage = dto.Voyage,
            Shipment = dto.Shipment,
            VesselQty = dto.VesselQty,
            PortOrigin = dto.PortOrigin,
            DestinationPort = dto.DestinationPort,
            Remarks = dto.Remarks,
            Deleted = dto.Deleted,
            CreatedBy = dto.CreatedBy,
            UpdatedBy = dto.UpdatedBy,
            CreatedAt = dto.CreatedAt,
            UpdatedAt = dto.UpdatedAt,
            DocStatus = dto.DocStatus,
            GrossWeight = dto.GrossWeight,
            VesselFlag = dto.VesselFlag,
            VesselStatus = dto.VesselStatus,
            Jetty = dto.Jetty,
            BerthingDate = dto.BerthingDate,
            AnchorageDate = dto.AnchorageDate,
            ReportDate = dto.ReportDate,
            UnloadingDate = dto.UnloadingDate,
            FinishUnloadingDate = dto.FinishUnloadingDate,
            DeadWeight = dto.DeadWeight,
            GrtWeight = dto.GrtWeight,
            InvoiceStatus = dto.InvoiceStatus,
            AgentId = dto.AgentId,
            AgentName = dto.AgentName,
            StatusBms = dto.StatusBms,
            SurveyorId = dto.SurveyorId,
            TradingId = dto.TradingId,
            DocType = dto.DocType,
            JettyId = dto.JettyId,
            VesselId = dto.VesselId,
            MasterJetty = dto.MasterJetty,
            Vessel = dto.Vessel,
            Items = items
        };

        return withItemsDto;
    }

    // Projection expression for efficient database queries
    public static Expression<Func<ExportVessel, ExportVesselProjectionDto>> ProjectionExpression =>
        entity => new ExportVesselProjectionDto
        {
            Id = entity.Id,
            ConcurrencyStamp = entity.ConcurrencyStamp,
            DocEntry = entity.DocEntry,
            DocNum = entity.DocNum,
            PostingDate = entity.PostingDate,
            VesselName = entity.Vessel != null ? entity.Vessel.Name : null,
            Shipment = entity.Shipment,
            VesselArrival = entity.VesselArrival,
            VesselDeparture = entity.VesselDeparture,
            PortOrigin = entity.PortOrigin,
            DestinationPort = entity.DestinationPort,
            Voyage = entity.Voyage,
            GrossWeight = entity.GrossWeight,
            DocStatus = entity.DocStatus,
            Remarks = entity.Remarks,
            DocType = entity.DocType,
            BerthingDate = entity.BerthingDate,
            AnchorageDate = entity.AnchorageDate,
            ReportDate = entity.ReportDate,
            UnloadingDate = entity.UnloadingDate,
            FinishUnloadingDate = entity.FinishUnloadingDate,
            GrtWeight = entity.GrtWeight,
            InvoiceStatus = entity.InvoiceStatus,
            StatusBms = entity.StatusBms,
            JettyId = entity.JettyId,
            VesselId = entity.VesselId,
            MasterAgentId = entity.MasterAgentId,
            MasterTradingId = entity.MasterTradingId,
            MasterSurveyorId = entity.MasterSurveyorId,
            AsideDate = entity.AsideDate,
            CastOfDate = entity.CastOfDate,
            PortOriginId = entity.PortOriginId,
            DestinationPortId = entity.DestinationPortId,
            // Navigation properties with efficient projections
            MasterAgent = entity.MasterAgent == null ? null : new AgentProjectionDto
            {
                Id = entity.MasterAgent.Id,
                Name = entity.MasterAgent.Name,
                Status = entity.MasterAgent.Status,
                Type = entity.MasterAgent.Type,
                NpwpNo = entity.MasterAgent.NpwpNo,
                BdmSapcode = entity.MasterAgent.BdmSapcode,
                TaxCode = entity.MasterAgent.TaxCode,
                AddressNpwp = entity.MasterAgent.AddressNpwp,
                Address = entity.MasterAgent.Address,
                SapcodeS4 = entity.MasterAgent.SapcodeS4,
            },
            MasterTrading = entity.MasterTrading == null ? null : new TradingProjectionDto
            {
                Id = entity.MasterTrading.Id,
                Name = entity.MasterTrading.Name,
                Address = entity.MasterTrading.Address,
                Npwp = entity.MasterTrading.Npwp,
                IsActive = entity.MasterTrading.IsActive,
            },
            MasterSurveyor = entity.MasterSurveyor == null ? null : new SurveyorProjectionDto
            {
                Id = entity.MasterSurveyor.Id,
                Name = entity.MasterSurveyor.Name,
                Address = entity.MasterSurveyor.Address,
                Npwp = entity.MasterSurveyor.Npwp,
                IsActive = entity.MasterSurveyor.IsActive,
            },
            MasterJetty = entity.MasterJetty == null ? null : new JettyProjectionDto
            {
                Id = entity.MasterJetty.Id,
                Name = entity.MasterJetty.Name,
                Alias = entity.MasterJetty.Alias,
                Max = entity.MasterJetty.Max,
                Port = entity.MasterJetty.Port,
                IsCustomArea = entity.MasterJetty.IsCustomArea,
                Deleted = entity.MasterJetty.Deleted,
                DocEntry = entity.MasterJetty.DocEntry,
            },
            Vessel = entity.Vessel == null ? null : new CargoProjectionDto
            {
                Id = entity.Vessel.Id,
                Name = entity.Vessel.Name,
                Alias = entity.Vessel.Alias,
                Flag = entity.Vessel.Flag,
                GrossWeight = entity.Vessel.GrossWeight,
                Type = entity.Vessel.Type,
                LoaQty = entity.Vessel.LoaQty,
                Status = entity.Vessel.Status,
                DocEntry = entity.Vessel.DocEntry,
            },
            MasterPortOrigin = entity.MasterPortOrigin == null ? null : new PortOfOriginProjectionDto
            {
                Id = entity.MasterPortOrigin.Id,
                Name = entity.MasterPortOrigin.Name,
                DocType = entity.MasterPortOrigin.DocType,
            },
            MasterDestinationPort = entity.MasterDestinationPort == null ? null : new DestinationPortProjectionDto
            {
                Id = entity.MasterDestinationPort.Id,
                Name = entity.MasterDestinationPort.Name,
                DocType = entity.MasterDestinationPort.DocType,
            },
        };
}