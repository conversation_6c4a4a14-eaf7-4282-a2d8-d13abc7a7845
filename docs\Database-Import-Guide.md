# Database Table Import Guide for ABP Framework

## Method 1: EF Core Scaffold-DbContext (Recommended)

### Step 1: Install Required Tools
```bash
# Ensure you have EF Core tools installed
dotnet tool install --global dotnet-ef
dotnet tool update --global dotnet-ef
```

### Step 2: Navigate to EntityFrameworkCore Project
```bash
cd src/Imip.Ekb.EntityFrameworkCore
```

### Step 3: Scaffold Existing Tables
```bash
# Scaffold all tables (excluding ABP tables)
dotnet ef dbcontext scaffold "Server=localhost;Database=EKB;User ID=sa;Password=******;Integrated Security=false;TrustServerCertificate=true;Encrypt=true;" Microsoft.EntityFrameworkCore.SqlServer \
  --output-dir TempModels \
  --context TempDbContext \
  --data-annotations \
  --force

# Or scaffold specific tables only
dotnet ef dbcontext scaffold "YourConnectionString" Microsoft.EntityFrameworkCore.SqlServer \
  --output-dir TempModels \
  --context TempDbContext \
  --table YourTableName1 \
  --table YourTableName2 \
  --data-annotations \
  --force
```

### Step 4: Convert Generated Models to ABP Entities

#### Original Generated Model:
```csharp
// TempModels/Customer.cs (Generated)
public partial class Customer
{
    public int Id { get; set; }
    public string Name { get; set; }
    public string Email { get; set; }
    public DateTime CreatedDate { get; set; }
}
```

#### Convert to ABP Entity:
```csharp
// src/Imip.Ekb.Domain/Customers/Customer.cs
using System;
using System.Diagnostics.CodeAnalysis;
using Volo.Abp.Domain.Entities.Auditing;

namespace Imip.Ekb.Customers
{
    [SetsRequiredMembers]
    public class Customer : AuditedAggregateRoot<Guid>
    {
        public required string Name { get; set; }
        public required string Email { get; set; }
        
        protected Customer() { }

        [SetsRequiredMembers]
        public Customer(Guid id, string name, string email) : base(id)
        {
            Name = name;
            Email = email;
        }
    }
}
```

### Step 5: Add to DbContext
```csharp
// src/Imip.Ekb.EntityFrameworkCore/EntityFrameworkCore/EkbDbContext.cs
public DbSet<Customers.Customer> Customers { get; set; }

// In OnModelCreating method:
builder.Entity<Customers.Customer>(b =>
{
    b.ToTable(EkbConsts.DbTablePrefix + "Customers", EkbConsts.DbSchema);
    b.ConfigureByConvention();
 b.Property(_ => _.RowVersion)
            .IsRowVersion()
            .HasConversion<byte[]>();
    
    b.Property(c => c.Name).IsRequired().HasMaxLength(100);
    b.Property(c => c.Email).IsRequired().HasMaxLength(200);
    
    b.HasIndex(c => c.Email).IsUnique();
});
```

### Step 6: Create Migration for Existing Table
```bash
# If table already exists, create empty migration
dotnet ef migrations add ImportExistingCustomerTable --no-build

# Then edit the migration to handle existing data
```

### Step 7: Handle Existing Data Migration
```csharp
// In the generated migration file
protected override void Up(MigrationBuilder migrationBuilder)
{
    // Check if table exists before creating
    migrationBuilder.Sql(@"
        IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='AppCustomers' AND xtype='U')
        BEGIN
            -- Create table if it doesn't exist
        END
        ELSE
        BEGIN
            -- Add any new columns or modify existing ones
        END
    ");
}
```

## Method 2: EF Core Power Tools (Visual Studio)

### Step 1: Install Extension
- Install "EF Core Power Tools" from Visual Studio Marketplace

### Step 2: Reverse Engineer
1. Right-click on EntityFrameworkCore project
2. Select "EF Core Power Tools" → "Reverse Engineer"
3. Configure connection string
4. Select tables/views to import
5. Configure options (pluralization, data annotations, etc.)
6. Generate models

### Step 3: Follow steps 4-7 from Method 1

## Method 3: Manual Table Analysis

### Query to Get Table Structure:
```sql
-- Get table columns
SELECT 
    c.COLUMN_NAME,
    c.DATA_TYPE,
    c.IS_NULLABLE,
    c.CHARACTER_MAXIMUM_LENGTH,
    c.COLUMN_DEFAULT,
    CASE WHEN pk.COLUMN_NAME IS NOT NULL THEN 'YES' ELSE 'NO' END AS IS_PRIMARY_KEY
FROM INFORMATION_SCHEMA.COLUMNS c
LEFT JOIN (
    SELECT ku.TABLE_NAME, ku.COLUMN_NAME
    FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS AS tc
    INNER JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE AS ku
        ON tc.CONSTRAINT_TYPE = 'PRIMARY KEY' 
        AND tc.CONSTRAINT_NAME = ku.CONSTRAINT_NAME
) pk ON c.TABLE_NAME = pk.TABLE_NAME AND c.COLUMN_NAME = pk.COLUMN_NAME
WHERE c.TABLE_NAME = 'YourTableName'
ORDER BY c.ORDINAL_POSITION;

-- Get foreign keys
SELECT 
    fk.name AS ForeignKeyName,
    tp.name AS ParentTable,
    cp.name AS ParentColumn,
    tr.name AS ReferencedTable,
    cr.name AS ReferencedColumn
FROM sys.foreign_keys fk
INNER JOIN sys.tables tp ON fk.parent_object_id = tp.object_id
INNER JOIN sys.tables tr ON fk.referenced_object_id = tr.object_id
INNER JOIN sys.foreign_key_columns fkc ON fk.object_id = fkc.constraint_object_id
INNER JOIN sys.columns cp ON fkc.parent_column_id = cp.column_id AND fkc.parent_object_id = cp.object_id
INNER JOIN sys.columns cr ON fkc.referenced_column_id = cr.column_id AND fkc.referenced_object_id = cr.object_id
WHERE tp.name = 'YourTableName';
```

## Best Practices for ABP Integration

### 1. Entity Naming Conventions
- Use singular names for entities
- Place in appropriate namespace folders
- Follow ABP base class hierarchy

### 2. ABP Base Classes
- `Entity<TKey>` - Basic entity
- `AuditedEntity<TKey>` - With creation/modification audit
- `FullAuditedEntity<TKey>` - With soft delete
- `AggregateRoot<TKey>` - For aggregate roots
- `AuditedAggregateRoot<TKey>` - Most common choice

### 3. Required Properties
- Use `required` keyword for .NET 7+
- Use `[SetsRequiredMembers]` on constructors
- Provide protected parameterless constructor for EF Core

### 4. Table Configuration
- Use `EkbConsts.DbTablePrefix` for table names
- Configure in `OnModelCreating` method
- Use `ConfigureByConvention()` for ABP conventions

### 5. Repository Registration
```csharp
// In EkbEntityFrameworkCoreModule.cs
options.AddRepository<Customer, EfCoreCustomerRepository>();
```

## Cleanup
After successful import, delete the temporary scaffolded files:
```bash
rm -rf TempModels/
rm TempDbContext.cs
```
