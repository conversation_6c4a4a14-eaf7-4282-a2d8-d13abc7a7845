using Imip.Ekb.BoundedZone;
using Imip.Ekb.Master.Agents;
using Imip.Ekb.Master.BcTypes;
using Imip.Ekb.Master.BusinessPartners;
using Imip.Ekb.Master.Cargos;
using Imip.Ekb.Master.DestinationPorts;
using Imip.Ekb.Master.ItemClassifications;
using Imip.Ekb.Master.Jetties;
using Imip.Ekb.Master.PortOfLoadings;
using Imip.Ekb.Master.PortServices;
using Imip.Ekb.Master.Surveyors;
using Imip.Ekb.Master.Tenants;
using Imip.Ekb.Master.Tradings;
using Imip.Ekb.Repositories;
using Imip.Ekb.SilkierQuartz;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Volo.Abp.AuditLogging.EntityFrameworkCore;
using Volo.Abp.BackgroundJobs.EntityFrameworkCore;
using Volo.Abp.BlobStoring.Database.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore.SqlServer;
using Volo.Abp.FeatureManagement.EntityFrameworkCore;
using Volo.Abp.Identity.EntityFrameworkCore;
using Volo.Abp.Modularity;
using Volo.Abp.OpenIddict.EntityFrameworkCore;
using Volo.Abp.PermissionManagement.EntityFrameworkCore;
using Volo.Abp.SettingManagement.EntityFrameworkCore;
using Volo.Abp.Studio;
using Volo.Abp.TenantManagement.EntityFrameworkCore;

namespace Imip.Ekb.EntityFrameworkCore;

[DependsOn(
    typeof(EkbDomainModule),
    typeof(AbpPermissionManagementEntityFrameworkCoreModule),
    typeof(AbpSettingManagementEntityFrameworkCoreModule),
    typeof(AbpEntityFrameworkCoreSqlServerModule),
    typeof(AbpBackgroundJobsEntityFrameworkCoreModule),
    typeof(AbpAuditLoggingEntityFrameworkCoreModule),
    typeof(AbpFeatureManagementEntityFrameworkCoreModule),
    typeof(AbpIdentityEntityFrameworkCoreModule),
    typeof(AbpOpenIddictEntityFrameworkCoreModule),
    typeof(AbpTenantManagementEntityFrameworkCoreModule),
    typeof(BlobStoringDatabaseEntityFrameworkCoreModule)
    )]
public class EkbEntityFrameworkCoreModule : AbpModule
{
    public override void PreConfigureServices(ServiceConfigurationContext context)
    {

        EkbEfCoreEntityExtensionMappings.Configure();
    }

    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        context.Services.AddAbpDbContext<EkbDbContext>(options =>
        {
            /* Remove "includeAllEntities: true" to create
             * default repositories only for aggregate roots */
            options.AddDefaultRepositories(includeAllEntities: true);
        });

        // Register custom repositories
        ConfigureCustomRepositories(context.Services);

        if (AbpStudioAnalyzeHelper.IsInAnalyzeMode)
        {
            return;
        }

        Configure<AbpDbContextOptions>(options =>
        {
            /* The main point to change your DBMS.
             * See also EkbDbContextFactory for EF Core tooling. */

            options.UseSqlServer();

            // options.UseSqlServer(optionsBuilder =>
            // {
            //     optionsBuilder.UseQuerySplittingBehavior(QuerySplittingBehavior.SplitQuery);
            //     // optionsBuilder.EnableRetryOnFailure(3);

            // });

            // options.Configure(configureOptions =>
            // {
            //     configureOptions.DbContextOptions.EnableSensitiveDataLogging(false);
            //     configureOptions.DbContextOptions.EnableServiceProviderCaching(true);
            // });


        });

    }

    private void ConfigureCustomRepositories(IServiceCollection services)
    {
        // Register custom repository implementations
        services.AddTransient<IZoneDetailRepository, ZoneDetailRepository>();
        services.AddTransient<ITenantRepository, TenantRepository>();
        services.AddTransient<IAgentRepository, AgentRepository>();
        services.AddTransient<IBcTypeRepository, BcTypeRepository>();
        services.AddTransient<IBusinessPartnerRepository, BusinessPartnerRepository>();
        services.AddTransient<ICargoRepository, CargoRepository>();
        services.AddTransient<IDestinationPortRepository, DestinationPortRepository>();
        services.AddTransient<IItemClassificationRepository, ItemClassificationRepository>();
        services.AddTransient<IJettyRepository, JettyRepository>();
        services.AddTransient<IPortOfLoadingRepository, PortOfLoadingRepository>();
        services.AddTransient<IPortServiceRepository, PortServiceRepository>();
        services.AddTransient<ISurveyorRepository, SurveyorRepository>();
        services.AddTransient<ITradingRepository, TradingRepository>();

        // Register Quartz repositories
        services.AddTransient<IQuartzExecutionHistoryRepository, QuartzExecutionHistoryRepository>();
        services.AddTransient<IQuartzJobSummaryRepository, QuartzJobSummaryRepository>();
    }
}
