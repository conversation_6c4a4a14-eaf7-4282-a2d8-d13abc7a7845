using System;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Entities;

namespace Imip.Ekb.Master.Tradings.Dtos;

public class TradingDto : AuditedEntityDto<Guid>, IHasConcurrencyStamp
{
    public string ConcurrencyStamp { get; set; }
    public int DocEntry { get; set; }
    public string Name { get; set; } = string.Empty;
    public string? Address { get; set; }
    public string? Npwp { get; set; }
    public DateTime? CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public string IsActive { get; set; } = string.Empty;
    public long CreatedBy { get; set; }
    public long? UpdatedBy { get; set; }
}