using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Volo.Abp.Domain.Entities.Auditing;

namespace Imip.Ekb.BoundedZone.Notuls;

[Table("notul_headers")]
public class NotulHeader : FullAuditedAggregateRoot<Guid>
{
    [Key]
    [Column("DocEntry")]
    public int DocEntry { get; set; }

    [Column("doc_number")]
    public long DocNumber { get; set; }

    [Column("tenant_id")]
    public long TenantId { get; set; }

    [Column("start_period")]
    public DateOnly StartPeriod { get; set; }

    [Column("end_period")]
    public DateOnly EndPeriod { get; set; }

    [Column("created_by")]
    public long CreatedBy { get; set; }

    [Column("created_at", TypeName = "datetime")]
    public DateTime? CreatedAt { get; set; }

    [Column("updated_at", TypeName = "datetime")]
    public DateTime? UpdatedAt { get; set; }


}
