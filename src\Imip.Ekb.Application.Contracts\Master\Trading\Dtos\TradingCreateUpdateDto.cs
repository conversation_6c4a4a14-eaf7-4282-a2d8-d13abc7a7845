using System;
using System.ComponentModel.DataAnnotations;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Entities;

namespace Imip.Ekb.Master.Tradings.Dtos;

public class TradingCreateUpdateDto : EntityDto<Guid>, IHasConcurrencyStamp
{
    public string ConcurrencyStamp { get; set; }
    // [Required]
    // public int DocEntry { get; set; }

    [Required]
    [StringLength(255)]
    public string Name { get; set; } = null!;

    public string? Address { get; set; }

    [StringLength(255)]
    public string? Npwp { get; set; }

    [Required]
    [StringLength(5)]
    public string IsActive { get; set; } = null!;

    [Required]
    public long CreatedBy { get; set; }

    public long? UpdatedBy { get; set; }
}