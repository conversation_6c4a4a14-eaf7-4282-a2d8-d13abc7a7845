﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Imip.Ekb.Billing.LocalVesselBillings;
using Imip.Ekb.BoundedZone;
using Volo.Abp.Domain.Entities.Auditing;

namespace Imip.Ekb.Master.BusinessPartners;

[Table("M_BP")]
public class BusinessPartner : FullAuditedAggregateRoot<Guid>
{
    [Key]
    public int DocEntry { get; set; }

    [StringLength(255)]
    public string Name { get; set; } = null!;

    [Column("Created_by")]
    [StringLength(255)]
    public string CreatedBy { get; set; } = null!;

    [Column("created_at", TypeName = "datetime")]
    public DateTime? CreatedAt { get; set; }

    [Column("updated_at", TypeName = "datetime")]
    public DateTime? UpdatedAt { get; set; }

    [StringLength(255)]
    public string Status { get; set; } = null!;

    [StringLength(100)]
    public string? Alias { get; set; }

    [StringLength(255)]
    public string? Image { get; set; }

    [StringLength(200)]
    public string? Direction { get; set; }

    [StringLength(200)]
    public string RegionType { get; set; } = null!;

    [StringLength(255)]
    public string? Address { get; set; }

    [StringLength(255)]
    public string? Tenant { get; set; }

    [StringLength(255)]
    [Column("Npwp1")]
    public string? Npwp { get; set; }

    [StringLength(255)]
    [Column("Npwp2")]
    public string? Nitku { get; set; }

    public virtual ICollection<ZoneDetail>? VesselTransactions { get; set; }
    public virtual ICollection<BillingItem>? BillingItems { get; set; }



}