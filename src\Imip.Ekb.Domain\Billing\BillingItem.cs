﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Imip.Ekb.Billing.ExportVesselBillings;
using Imip.Ekb.Billing.ImportVesselBillings;
using Imip.Ekb.BoundedZone;
using Imip.Ekb.Master.BcTypes;
using Imip.Ekb.Master.BusinessPartners;
using Imip.Ekb.Master.Jetties;
using Imip.Ekb.Master.Tenants;
using Volo.Abp.Domain.Entities.Auditing;

namespace Imip.Ekb.Billing.LocalVesselBillings;

[Table("BEXP")]
public class BillingItem : FullAuditedAggregateRoot<Guid>
{
    [Key]
    public int DocEntry { get; set; }

    public int DocNum { get; set; }

    [Column("NoBL")]
    [StringLength(100)]
    public string NoBl { get; set; } = null!;

    [Column("DateBL")]
    public DateOnly? DateBl { get; set; }

    public int TenantId { get; set; }

    [Column("BCType")]
    public int Bctype { get; set; }

    public string? LoadingItem { get; set; }

    [Column(TypeName = "numeric(20, 5)")]
    public decimal LoadingQty { get; set; }

    public string? Remark { get; set; }

    [StringLength(5)]
    public string Deleted { get; set; } = null!;

    [StringLength(255)]
    public string? Status { get; set; }

    [StringLength(255)]
    public string? PortServiceType { get; set; }

    [StringLength(255)]
    public string? LoadingUnloadingType { get; set; }

    public int CreatedBy { get; set; }

    public int UpdatedBy { get; set; }

    [Column("created_at", TypeName = "datetime")]
    public DateTime? CreatedAt { get; set; }

    [Column("updated_at", TypeName = "datetime")]
    public DateTime? UpdatedAt { get; set; }

    [StringLength(100)]
    public string? Unit { get; set; }

    [StringLength(255)]
    public string? Notification { get; set; }

    public string? ItemName { get; set; }

    [StringLength(255)]
    public string? CompanyHeader { get; set; }

    [Column(TypeName = "decimal(20, 4)")]
    public decimal Total { get; set; }

    [StringLength(255)]
    public string? Signature1 { get; set; }

    [StringLength(255)]
    public string? Signature2 { get; set; }

    [StringLength(255)]
    public string? Signature3 { get; set; }

    [Column("BP")]
    public int? Bp { get; set; }

    public int? Jetty { get; set; }

    public string? AttachmentText { get; set; }

    [StringLength(100)]
    public string Type { get; set; } = null!;

    [Column(TypeName = "decimal(20, 4)")]
    public decimal? Price { get; set; }

    [Column(TypeName = "decimal(20, 4)")]
    public decimal? ServiceLoading { get; set; }

    public string? Classification { get; set; }

    [StringLength(255)]
    public string WeightCategory { get; set; } = null!;

    [Column(TypeName = "decimal(20, 5)")]
    public decimal TotalServiceLoading { get; set; }

    [StringLength(255)]
    public string? CurrencyPortService { get; set; }

    [StringLength(255)]
    public string? CurrencyServiceLoading { get; set; }

    public int? BaseId { get; set; }

    public int? Agent { get; set; }

    [StringLength(255)]
    public string? BaseIdString { get; set; }

    [Column(TypeName = "decimal(20, 4)")]
    public decimal? NetWeight { get; set; }

    [Column(TypeName = "decimal(20, 4)")]
    public decimal? UnitPrice { get; set; }

    [Column(TypeName = "decimal(20, 4)")]
    public decimal? TotalInv { get; set; }

    [StringLength(100)]
    public string? StatusServiceLoading { get; set; }

    [Column(TypeName = "decimal(20, 4)")]
    public decimal? QtyEstimate { get; set; }

    [Column(TypeName = "decimal(20, 4)")]
    public decimal? PriceEstimate { get; set; }

    [StringLength(200)]
    public string? BillingType { get; set; }

    [StringLength(200)]
    public string? ChargeTo { get; set; }

    [StringLength(50)]
    public string? TaxCode { get; set; }

    [Column(TypeName = "decimal(20, 4)")]
    public decimal? QtyRevised { get; set; }

    [Column(TypeName = "decimal(20, 4)")]
    public decimal? PriceRevised { get; set; }

    [StringLength(255)]
    public string? NoNota { get; set; }

    [Column(TypeName = "decimal(20, 4)")]
    public decimal? TotalEstimate { get; set; }

    [Column(TypeName = "decimal(20, 4)")]
    public decimal? TotalRevised { get; set; }

    [StringLength(255)]
    public string? NoNotaSl { get; set; }

    public int? LineNum { get; set; }

    [StringLength(255)]
    public string? IsTenant { get; set; }

    [Column(TypeName = "decimal(20, 2)")]
    public decimal? SubTotal { get; set; }

    [Column(TypeName = "decimal(20, 2)")]
    public decimal? VatValue { get; set; }

    [Column("VatValueSL", TypeName = "decimal(20, 2)")]
    public decimal? VatValueSl { get; set; }

    [Column("SubTotalSL", TypeName = "decimal(20, 2)")]
    public decimal? SubTotalSl { get; set; }

    [Column("TotalEstimateSL", TypeName = "decimal(20, 2)")]
    public decimal? TotalEstimateSl { get; set; }




    public Guid? HeaderId { get; set; }
    public Guid? ZoneDetailId { get; set; }
    public Guid? MasterTenantId { get; set; }
    public Guid? BcTypeId { get; set; }
    public Guid? BusinessPartnerId { get; set; }
    public Guid? JettyId { get; set; }
    public virtual ExportVesselBilling? ExportVesselBilling { get; set; }
    public virtual ImportVesselBilling? ImportVesselBilling { get; set; }
    public virtual LocalVesselBilling? LocalVesselBilling { get; set; }
    public virtual ZoneDetail? ZoneDetail { get; set; }
    public virtual MasterTenant? MasterTenant { get; set; }
    public virtual BcType? BcType { get; set; }
    public virtual BusinessPartner? BusinessPartner { get; set; }
    public virtual Jetty? MasterJetty { get; set; }
}
