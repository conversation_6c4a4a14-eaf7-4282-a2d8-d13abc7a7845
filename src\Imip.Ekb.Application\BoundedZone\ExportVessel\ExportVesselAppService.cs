using ExportVesselEntity = Imip.Ekb.BoundedZone.ExportVessels.ExportVessel;
using Imip.Ekb.BoundedZone.Dtos;
using Imip.Ekb.BoundedZone;
using Imip.Ekb.BoundedZone.ExportVessels;
using Imip.Ekb.Mapping.Mappers;
using Imip.Ekb.Models;
using Imip.Ekb.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp;
using Microsoft.Extensions.Configuration;
using Imip.Ekb.BoundedZone.ExportVessel;

namespace Imip.Ekb.Application.BoundedZone.ExportVessel;

[Authorize]
public class ExportVesselAppService :
    CrudAppService<
        ExportVesselEntity,
        ExportVesselDto,
        Guid,
        QueryParametersDto,
        CreateUpdateExportVesselDto>,
    IExportVesselAppService
{
    private readonly IExportVesselRepository _exportVesselRepository;
    private readonly IZoneDetailRepository _zoneDetailRepository;
    private readonly ExportVesselMapper _exportVesselMapper;
    private readonly VesselMapper _vesselMapper;
    private readonly ZoneDetailMapper _zoneDetailMapper;
    private readonly IConfiguration _configuration;
    private readonly ILogger<ExportVesselAppService> _logger;

    public ExportVesselAppService(
        IExportVesselRepository exportVesselRepository,
        IZoneDetailRepository zoneDetailRepository,
        ExportVesselMapper exportVesselMapper,
        VesselMapper vesselMapper,
        ZoneDetailMapper zoneDetailMapper,
        IConfiguration configuration,
        ILogger<ExportVesselAppService> logger)
        : base(exportVesselRepository)
    {
        _configuration = configuration;
        _exportVesselRepository = exportVesselRepository;
        _zoneDetailRepository = zoneDetailRepository;
        _exportVesselMapper = exportVesselMapper;
        _vesselMapper = vesselMapper;
        _zoneDetailMapper = zoneDetailMapper;
        _logger = logger;
    }

    public override async Task<PagedResultDto<ExportVesselDto>> GetListAsync(QueryParametersDto input)
    {
        await CheckGetListPolicyAsync();

        var query = await _exportVesselRepository.GetQueryableWithIncludesAsync();

        // Apply sorting
        if (!string.IsNullOrWhiteSpace(input.Sorting))
        {
            query = query.OrderBy(input.Sorting);
        }
        else
        {
            query = query.OrderByDescending(x => x.CreationTime);
        }

        var totalCount = await AsyncExecuter.CountAsync(query);

        var entities = await AsyncExecuter.ToListAsync(
            query.PageBy(input.SkipCount, input.MaxResultCount)
        );

        var dtos = entities.Select(MapToGetListOutputDto).ToList();

        return new PagedResultDto<ExportVesselDto>(totalCount, dtos);
    }



    public async Task<string> GenerateNextDocNumAsync(DateTime postDate)
    {
        var prefix = postDate.ToString("yyMM");
        var queryable = await _exportVesselRepository.GetQueryableAsync();
        var maxDocNum = queryable
            .Where(x => x.DocNum.StartsWith(prefix) && x.DocType == EkbConsts.ExportVesselType.Export)
            .OrderByDescending(x => x.DocNum)
            .Select(x => x.DocNum)
            .FirstOrDefault();

        int nextIncrement = 1;
        if (!string.IsNullOrEmpty(maxDocNum) && maxDocNum.Length > 4)
        {
            var lastIncrementStr = maxDocNum.Substring(4, 4);
            if (int.TryParse(lastIncrementStr, out var lastIncrement))
            {
                nextIncrement = lastIncrement + 1;
            }
        }
        var docNum = $"{prefix}{nextIncrement:D4}";
        return docNum;
    }

    public override async Task<ExportVesselDto> CreateAsync(CreateUpdateExportVesselDto input)
    {
        await CheckCreatePolicyAsync();

        // Generate DocNum
        var dt = input.PostingDate;
        var docNum = await GenerateNextDocNumAsync(dt.ToDateTime(TimeOnly.MinValue));
        input.DocNum = docNum;

        // Set DocType for Export vessel
        input.DocType = EkbConsts.ExportVesselType.Export;

        var entity = _exportVesselMapper.CreateEntityWithId(input, Guid.NewGuid());

        await _exportVesselRepository.InsertAsync(entity, autoSave: false);

        // Set HeaderId for each item after entity is inserted (if needed)
        if (input.Items != null && input.Items.Count != 0)
        {
            foreach (var itemDto in input.Items)
            {
                // If HeaderId is Guid, assign entity.Id
                itemDto.HeaderId = entity.Id;
                itemDto.DocNum = entity.DocEntry;
                itemDto.DocType = input.DocType;

                var itemEntity = _zoneDetailMapper.CreateEntityWithId(itemDto, Guid.NewGuid());
                // Set CreatedBy from authenticated user on detail
                itemEntity.CreatedBy = CurrentUser.UserName ?? "System";
                // Ensure required fields are set
                itemEntity.IsUrgent = string.IsNullOrEmpty(itemEntity.IsUrgent) ? "N" : itemEntity.IsUrgent;
                itemEntity.IsScan = string.IsNullOrEmpty(itemEntity.IsScan) ? "N" : itemEntity.IsScan;
                itemEntity.IsOriginal = string.IsNullOrEmpty(itemEntity.IsOriginal) ? "N" : itemEntity.IsOriginal;
                itemEntity.IsSend = string.IsNullOrEmpty(itemEntity.IsSend) ? "N" : itemEntity.IsSend;
                itemEntity.IsFeOri = string.IsNullOrEmpty(itemEntity.IsFeOri) ? "N" : itemEntity.IsFeOri;
                itemEntity.IsFeSend = string.IsNullOrEmpty(itemEntity.IsFeSend) ? "N" : itemEntity.IsFeSend;
                itemEntity.IsChange = string.IsNullOrEmpty(itemEntity.IsChange) ? "N" : itemEntity.IsChange;
                itemEntity.Deleted = string.IsNullOrEmpty(itemEntity.Deleted) ? "N" : itemEntity.Deleted;
                itemEntity.DocType = string.IsNullOrEmpty(itemEntity.DocType) ? "Export" : itemEntity.DocType;
                await _zoneDetailRepository.InsertAsync(itemEntity, autoSave: false);
            }
        }

        // Save all changes in a single transaction
        await CurrentUnitOfWork.SaveChangesAsync();

        // Load the entity with includes for proper mapping
        var createdEntity = await _exportVesselRepository.GetAsync(entity.Id);
        return MapToGetOutputDto(createdEntity);
    }

    public override async Task<ExportVesselDto> UpdateAsync(Guid id, CreateUpdateExportVesselDto input)
    {
        await CheckUpdatePolicyAsync();

        var entity = await _exportVesselRepository.GetAsync(id);
        _exportVesselMapper.MapToEntity(input, entity);

        await _exportVesselRepository.UpdateAsync(entity, autoSave: false);


        // Handle items update - update existing items and add new ones
        if (input.Items != null)
        {
            // Get existing items
            var existingItems = await _zoneDetailRepository.GetByHeaderIdAsync(id);

            // Process each item in the input
            for (int i = 0; i < input.Items.Count; i++)
            {
                var itemDto = input.Items[i];
                itemDto.HeaderId = id;

                if (i < existingItems.Count)
                {
                    // Update existing item
                    var existingItem = existingItems[i];
                    _zoneDetailMapper.MapToEntity(itemDto, existingItem);
                    // Ensure required fields are set
                    existingItem.IsUrgent = string.IsNullOrEmpty(existingItem.IsUrgent) ? "N" : existingItem.IsUrgent;
                    existingItem.IsScan = string.IsNullOrEmpty(existingItem.IsScan) ? "N" : existingItem.IsScan;
                    existingItem.IsOriginal = string.IsNullOrEmpty(existingItem.IsOriginal) ? "N" : existingItem.IsOriginal;
                    existingItem.IsSend = string.IsNullOrEmpty(existingItem.IsSend) ? "N" : existingItem.IsSend;
                    existingItem.IsFeOri = string.IsNullOrEmpty(existingItem.IsFeOri) ? "N" : existingItem.IsFeOri;
                    existingItem.IsFeSend = string.IsNullOrEmpty(existingItem.IsFeSend) ? "N" : existingItem.IsFeSend;
                    existingItem.IsChange = string.IsNullOrEmpty(existingItem.IsChange) ? "N" : existingItem.IsChange;
                    existingItem.Deleted = string.IsNullOrEmpty(existingItem.Deleted) ? "N" : existingItem.Deleted;
                    existingItem.DocType = string.IsNullOrEmpty(existingItem.DocType) ? "Export" : existingItem.DocType;
                    await _zoneDetailRepository.UpdateAsync(existingItem, autoSave: false);
                }
                else
                {
                    // If HeaderId is Guid, assign entity.Id
                    itemDto.HeaderId = entity.Id;
                    itemDto.DocNum = entity.DocEntry;
                    itemDto.DocType = input.DocType;

                    var itemEntity = _zoneDetailMapper.CreateEntityWithId(itemDto, Guid.NewGuid());
                    // Set CreatedBy from authenticated user on detail
                    itemEntity.CreatedBy = CurrentUser.UserName ?? "System";
                    // Ensure required fields are set
                    itemEntity.IsUrgent = string.IsNullOrEmpty(itemEntity.IsUrgent) ? "N" : itemEntity.IsUrgent;
                    itemEntity.IsScan = string.IsNullOrEmpty(itemEntity.IsScan) ? "N" : itemEntity.IsScan;
                    itemEntity.IsOriginal = string.IsNullOrEmpty(itemEntity.IsOriginal) ? "N" : itemEntity.IsOriginal;
                    itemEntity.IsSend = string.IsNullOrEmpty(itemEntity.IsSend) ? "N" : itemEntity.IsSend;
                    itemEntity.IsFeOri = string.IsNullOrEmpty(itemEntity.IsFeOri) ? "N" : itemEntity.IsFeOri;
                    itemEntity.IsFeSend = string.IsNullOrEmpty(itemEntity.IsFeSend) ? "N" : itemEntity.IsFeSend;
                    itemEntity.IsChange = string.IsNullOrEmpty(itemEntity.IsChange) ? "N" : itemEntity.IsChange;
                    itemEntity.Deleted = string.IsNullOrEmpty(itemEntity.Deleted) ? "N" : itemEntity.Deleted;
                    itemEntity.DocType = string.IsNullOrEmpty(itemEntity.DocType) ? "Export" : itemEntity.DocType;
                    await _zoneDetailRepository.InsertAsync(itemEntity, autoSave: false);
                }
            }

            // Remove excess existing items if input has fewer items
            for (int i = input.Items.Count; i < existingItems.Count; i++)
            {
                await _zoneDetailRepository.DeleteAsync(existingItems[i], autoSave: false);
            }
        }

        // Save all changes in a single transaction
        await CurrentUnitOfWork.SaveChangesAsync();

        // Load the entity with includes for proper mapping
        var updatedEntity = await _exportVesselRepository.GetAsync(entity.Id);
        return MapToGetOutputDto(updatedEntity);
    }

    public override async Task<ExportVesselDto> GetAsync(Guid id)
    {
        await CheckGetPolicyAsync();

        var entity = await _exportVesselRepository.GetQueryableWithItemsSplitAsync(id);

        if (entity == null)
        {
            throw new UserFriendlyException("LocalVessel not found");
        }

        // Map the items from the navigation property
        var items = entity.Items?.Select(zoneDetail =>
        {
            var itemDto = _vesselMapper.MapZoneDetailToItemDtoWithType(zoneDetail, "LocalVessel");

            // Use navigation property for Tenant
            if (zoneDetail.Tenant != null)
            {
                itemDto.Tenant = _vesselMapper.MapTenantToTenantShortDto(zoneDetail.Tenant);
            }

            // Map DocAttachments if available
            if (zoneDetail.DocAttachment != null && zoneDetail.DocAttachment.Any())
            {
                itemDto.Attachments = zoneDetail.DocAttachment.Select(att => _vesselMapper.MapDocAttachmentToDto(att)).ToList();
            }
            else
            {
                itemDto.Attachments = new List<DocAttachmentSortDto>();
            }

            return itemDto;
        }).ToList() ?? new List<VesselItemDto>();

        return _exportVesselMapper.MapToDtoWithItems(entity, items);
    }

    private IQueryable<ExportVesselEntity> ApplyDynamicQuery(IQueryable<ExportVesselEntity> query, QueryParametersDto parameters)
    {
        // Apply filters if provided
        if (parameters.FilterGroup?.Conditions != null && parameters.FilterGroup.Conditions.Count > 0)
        {
            query = DynamicQueryBuilder<ExportVesselEntity>.ApplyFilters(query, parameters.FilterGroup);
        }

        // Apply sorting
        if (parameters.Sort?.Count > 0)
        {
            query = DynamicQueryBuilder<ExportVesselEntity>.ApplyMultipleSorting(query, parameters.Sort);
        }
        else if (!string.IsNullOrEmpty(parameters.Sorting))
        {
            query = query.OrderBy(parameters.Sorting);
        }
        else
        {
            query = query.OrderByDescending(x => x.CreationTime);
        }

        return query;
    }

    protected override ExportVesselDto MapToGetOutputDto(ExportVesselEntity entity)
    {
        return _exportVesselMapper.MapToDto(entity);
    }

    protected override ExportVesselDto MapToGetListOutputDto(ExportVesselEntity entity)
    {
        return _exportVesselMapper.MapToDto(entity);
    }

    protected override ExportVesselEntity MapToEntity(CreateUpdateExportVesselDto createInput)
    {
        return _exportVesselMapper.MapToEntity(createInput);
    }

    protected override void MapToEntity(CreateUpdateExportVesselDto updateInput, ExportVesselEntity entity)
    {
        _exportVesselMapper.MapToEntity(updateInput, entity);
    }

    public async Task<PagedResultDto<ExportVesselProjectionDto>> FilterListAsync(QueryParametersDto parameters)
    {
        await CheckGetListPolicyAsync();

        // Get queryable and apply filters/sorting
        var entityQuery = await _exportVesselRepository.GetQueryableWithIncludesAsync();
        entityQuery = ApplyDynamicQuery(entityQuery, parameters);

        // Use the projection expression from mapper for optimal performance
        var projectedQuery = entityQuery.Select(ExportVesselMapper.ProjectionExpression);

        // Execute count and data queries efficiently
        var totalCount = await AsyncExecuter.CountAsync(projectedQuery);
        var items = await AsyncExecuter.ToListAsync(
            projectedQuery.Skip(parameters.SkipCount).Take(parameters.MaxResultCount)
        );

        return new PagedResultDto<ExportVesselProjectionDto>
        {
            TotalCount = totalCount,
            Items = items
        };
    }

    public async Task<ExportVesselWithItemsDto> GetWithItemsAsync(Guid id)
    {
        await CheckGetPolicyAsync();

        var vesselWithItems = await _exportVesselRepository.GetQueryableWithItemsSplitAsync(id);

        if (vesselWithItems == null)
        {
            throw new UserFriendlyException("ExportVessel not found");
        }

        return vesselWithItems.MapToWithItemsDto(_exportVesselMapper, _configuration);
    }
}