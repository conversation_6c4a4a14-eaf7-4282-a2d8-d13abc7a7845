using System;
using System.Collections.Generic;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Entities;

namespace Imip.Ekb.BoundedZone.Dtos;

public class VesselItemDto : AuditedEntityDto<Guid>, IHasConcurrencyStamp
{
    public string ConcurrencyStamp { get; set; }
    public int DocEntry { get; set; }
    public int DocNum { get; set; }
    public string? TenantName { get; set; }
    public string? ItemName { get; set; }
    public decimal? ItemQty { get; set; }
    public string? UnitQty { get; set; }
    public string? Cargo { get; set; }
    public string? Shipment { get; set; }
    public string? Remarks { get; set; }
    public string? NoBl { get; set; }
    public DateOnly? DateBl { get; set; }
    public string? AjuNo { get; set; }
    public string? RegNo { get; set; }
    public DateOnly? RegDate { get; set; }
    public string? SppbNo { get; set; }
    public DateOnly? SppbDate { get; set; }
    public string? SppdNo { get; set; }
    public DateOnly? SppdDate { get; set; }
    public decimal? GrossWeight { get; set; }
    public string? UnitWeight { get; set; }
    public Guid? HeaderId { get; set; }
    public string? LetterNo { get; set; }
    public string? DocType { get; set; }
    public string VesselType { get; set; } = string.Empty; // "Import", "Export", "LocalIn", "LocalOut"
    public string? ShippingInstructionNo { get; set; }
    public string? RegType { get; set; }
    public string? Status { get; set; }
    public DateOnly? ShippingInstructionDate { get; set; }
    public DateOnly? LetterDate { get; set; }
    public Guid? TenantId { get; set; }
    public Guid? BcTypeId { get; set; }
    public Guid? BusinessPartnerId { get; set; }
    public Guid? AgentId { get; set; }
    public Guid? MasterExportClassificationId { get; set; }
    public string? Item { get; set; }
    public TenantShortDto? Tenant { get; set; }
    public BcTypeShortDto? BcType { get; set; }
    public AgentShortDto? MasterAgent { get; set; }
    public BusinessPartnerShortDto? BusinessPartner { get; set; }
    public List<DocAttachmentSortDto>? Attachments { get; set; }
    // public VesselHeaderDto? VesselHeader { get; set; }
}

public class TenantShortDto
{
    public int DocEntry { get; set; }
    public Guid Id { get; set; }
    public string? Name { get; set; }
    public string? FullName { get; set; }
    public string? Npwp { get; set; }
    public string? Address { get; set; }
    public string? Nib { get; set; }
    public string? Phone { get; set; }
    public string? NoAndDateNotaris { get; set; }
    public string? DescNotaris { get; set; }
}
public class AgentShortDto
{
    public int DocEntry { get; set; }
    public Guid Id { get; set; }
    public string? Name { get; set; }
    public string? Type { get; set; }
}

public class BcTypeShortDto
{
    public int DocEntry { get; set; }
    public Guid Id { get; set; }
    public string? Type { get; set; }
    public string? TransName { get; set; }
}

public class BusinessPartnerShortDto
{
    public int DocEntry { get; set; }
    public Guid Id { get; set; }
    public string? Name { get; set; }
    public string? Npwp { get; set; }
    public string? Nitku { get; set; }
}

public class DocAttachmentSortDto
{
    public Guid Id { get; set; }
    public string? DocType { get; set; }
    public string? TransType { get; set; }
    public string? Description { get; set; }
    public string? BlobName { get; set; }
    public Guid? ReferenceId { get; set; }
    public string? StreamUrl { get; set; }
    public string? TabName { get; set; }
    public string? FileName { get; set; }
    public string? TypePa { get; set; }
}