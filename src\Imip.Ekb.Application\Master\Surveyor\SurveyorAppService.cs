using System;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using Imip.Ekb.Mapping.Mappers;
using Imip.Ekb.Master.Surveyors.Dtos;
using Imip.Ekb.Models;
using Imip.Ekb.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.Ekb.Master.Surveyors;

[Authorize]
public class SurveyorAppService :
    CrudAppService<Surveyor, SurveyorDto, Guid, PagedAndSortedResultRequestDto, SurveyorCreateUpdateDto, SurveyorCreateUpdateDto>,
    ISurveyorAppService
{
    private readonly ISurveyorRepository _surveyorRepository;
    private readonly SurveyorMapper _mapper;
    private readonly ILogger<SurveyorAppService> _logger;

    public SurveyorAppService(
        ISurveyorRepository surveyorRepository,
        SurveyorMapper mapper,
        ILogger<SurveyorAppService> logger)
        : base(surveyorRepository)
    {
        _surveyorRepository = surveyorRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public override async Task<SurveyorDto> CreateAsync(SurveyorCreateUpdateDto input)
    {
        await CheckCreatePolicyAsync();
        var entity = _mapper.CreateEntityWithId(input, Guid.NewGuid());
        entity.CreatedAt = Clock.Now;
        await _surveyorRepository.InsertAsync(entity, autoSave: true);
        return _mapper.MapToDto(entity);
    }

    public override async Task<SurveyorDto> UpdateAsync(Guid id, SurveyorCreateUpdateDto input)
    {
        await CheckUpdatePolicyAsync();
        var entity = await _surveyorRepository.GetAsync(id);
        var originalCreatedBy = entity.CreatedBy;
        var originalCreatedAt = entity.CreatedAt;
        _mapper.MapToEntity(input, entity);
        entity.CreatedBy = originalCreatedBy;
        entity.CreatedAt = originalCreatedAt;
        entity.UpdatedAt = Clock.Now;
        await _surveyorRepository.UpdateAsync(entity, autoSave: true);
        return _mapper.MapToDto(entity);
    }

    public override async Task DeleteAsync(Guid id)
    {
        await CheckDeletePolicyAsync();
        var entity = await _surveyorRepository.GetAsync(id);
        entity.IsActive = "N";
        entity.UpdatedAt = Clock.Now;
        await _surveyorRepository.UpdateAsync(entity, autoSave: true);
    }

    public override async Task<SurveyorDto> GetAsync(Guid id)
    {
        await CheckGetPolicyAsync();
        var entity = await _surveyorRepository.GetAsync(id);
        return _mapper.MapToDto(entity);
    }

    public override async Task<PagedResultDto<SurveyorDto>> GetListAsync(PagedAndSortedResultRequestDto input)
    {
        await CheckGetListPolicyAsync();
        var queryable = await _surveyorRepository.GetQueryableAsync();
        var sortedQueryable = queryable.OrderBy(input.Sorting.IsNullOrWhiteSpace() ? nameof(Surveyor.DocEntry) : input.Sorting);
        var totalCount = await AsyncExecuter.CountAsync(queryable);
        var entities = await AsyncExecuter.ToListAsync(
            sortedQueryable.PageBy(input.SkipCount, input.MaxResultCount)
        );
        var dtos = entities.Select(_mapper.MapToDto).ToList();
        return new PagedResultDto<SurveyorDto>(totalCount, dtos);
    }

    public virtual async Task<PagedResultDto<SurveyorDto>> FilterListAsync(QueryParametersDto parameters)
    {
        var query = await _surveyorRepository.GetQueryableAsync();
        query = ApplyDynamicQuery(query, parameters);
        var totalCount = await AsyncExecuter.CountAsync(query);
        var items = await AsyncExecuter.ToListAsync(
            query.Skip(parameters.SkipCount).Take(parameters.MaxResultCount)
        );
        var dtos = items.Select(_mapper.MapToDto).ToList();
        return new PagedResultDto<SurveyorDto>
        {
            TotalCount = totalCount,
            Items = dtos
        };
    }

    private IQueryable<Surveyor> ApplyDynamicQuery(IQueryable<Surveyor> query, QueryParametersDto parameters)
    {
        if (parameters.FilterGroup?.Conditions != null && parameters.FilterGroup.Conditions.Count > 0)
        {
            query = DynamicQueryBuilder<Surveyor>.ApplyFilters(query, parameters.FilterGroup);
        }
        if (parameters.Sort?.Count > 0)
        {
            query = DynamicQueryBuilder<Surveyor>.ApplyMultipleSorting(query, parameters.Sort);
        }
        else if (!string.IsNullOrEmpty(parameters.Sorting))
        {
            query = query.OrderBy(parameters.Sorting);
        }
        else
        {
            query = query.OrderByDescending(x => x.CreationTime);
        }
        return query;
    }
}