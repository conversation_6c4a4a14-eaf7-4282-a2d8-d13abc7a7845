using System;
using System.Threading.Tasks;
using Imip.Ekb.Master.ItemClassifications.Dtos;
using Imip.Ekb.Models;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.Ekb.Master.ItemClassifications;

public interface IItemClassificationAppService :
    ICrudAppService<ItemClassificationDto, Guid, PagedAndSortedResultRequestDto, ItemClassificationCreateUpdateDto, ItemClassificationCreateUpdateDto>
{
    Task<PagedResultDto<ItemClassificationDto>> FilterListAsync(QueryParametersDto parameters);
}