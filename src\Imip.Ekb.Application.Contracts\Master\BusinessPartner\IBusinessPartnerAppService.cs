using System;
using System.Threading.Tasks;
using Imip.Ekb.Master.BusinessPartners.Dtos;
using Imip.Ekb.Models;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.Ekb.Master.BusinessPartners;

public interface IBusinessPartnerAppService :
    ICrudAppService<BusinessPartnerDto, Guid, PagedAndSortedResultRequestDto, BusinessPartnerCreateUpdateDto, BusinessPartnerCreateUpdateDto>
{
    Task<PagedResultDto<BusinessPartnerDto>> FilterListAsync(QueryParametersDto parameters);
}