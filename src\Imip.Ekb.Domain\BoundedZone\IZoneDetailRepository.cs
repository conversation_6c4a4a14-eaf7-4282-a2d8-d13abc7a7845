using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories;

namespace Imip.Ekb.BoundedZone;

public interface IZoneDetailRepository : IRepository<ZoneDetail, Guid>
{
    Task<IQueryable<ZoneDetail>> GetQueryableWithIncludesAsync();
    Task<IQueryable<ZoneDetail>> GetQueryableWithVesselHeadersAsync();
    Task<IQueryable<ZoneDetail>> GetQueryableWithImportVesselAsync();
    Task<IQueryable<ZoneDetail>> GetQueryableWithExportVesselAsync();
    Task<IQueryable<ZoneDetail>> GetQueryableWithLocalVesselAsync();
    Task<IQueryable<ZoneDetail>> GetQueryableWithTradingVesselAsync();
    Task<IQueryable<ZoneDetail>> GetOptimizedQueryableAsync();
    Task<ZoneDetail?> GetQueryableWithImportInvoiceAsync(Guid id);
    Task<ZoneDetail?> GetQueryableWithTradingInvoiceAsync(Guid id);
    Task<List<ZoneDetail>> GetByHeaderIdAsync(Guid headerId);
    Task<int> GetOptimizedCountAsync();
    Task<int> GetCountWithTimeoutAsync(int timeoutSeconds = 30);
}