using LocalVesselEntity = Imip.Ekb.BoundedZone.LocalVessels.LocalVessel;
using Imip.Ekb.BoundedZone.Dtos;
using Imip.Ekb.BoundedZone;
using Imip.Ekb.BoundedZone.LocalVessels;
using Imip.Ekb.Mapping.Mappers;
using Imip.Ekb.Models;
using Imip.Ekb.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp;
using Imip.Ekb.BoundedZone.LocalVessel;
using Microsoft.Extensions.Configuration;

namespace Imip.Ekb.Application.BoundedZone.LocalVessel;

[Authorize]
public class LocalVesselAppService :
    CrudAppService<
        LocalVesselEntity,
        LocalVesselDto,
        Guid,
        QueryParametersDto,
        CreateUpdateLocalVesselDto>,
    ILocalVesselAppService
{
    private readonly ILocalVesselRepository _localVesselRepository;
    private readonly IZoneDetailRepository _zoneDetailRepository;
    private readonly LocalVesselMapper _localVesselMapper;
    private readonly VesselMapper _vesselMapper;
    private readonly IConfiguration _configuration;
    private readonly ZoneDetailMapper _zoneDetailMapper;
    private readonly ILogger<LocalVesselAppService> _logger;

    public LocalVesselAppService(
        ILocalVesselRepository localVesselRepository,
        IZoneDetailRepository zoneDetailRepository,
        LocalVesselMapper localVesselMapper,
        VesselMapper vesselMapper,
        IConfiguration configuration,
        ZoneDetailMapper zoneDetailMapper,
        ILogger<LocalVesselAppService> logger)
        : base(localVesselRepository)
    {
        _configuration = configuration;
        _localVesselRepository = localVesselRepository;
        _zoneDetailRepository = zoneDetailRepository;
        _localVesselMapper = localVesselMapper;
        _vesselMapper = vesselMapper;
        _zoneDetailMapper = zoneDetailMapper;
        _logger = logger;
    }

    public override async Task<PagedResultDto<LocalVesselDto>> GetListAsync(QueryParametersDto input)
    {
        await CheckGetListPolicyAsync();

        var query = await _localVesselRepository.GetQueryableWithIncludesAsync();

        // Apply sorting
        if (!string.IsNullOrWhiteSpace(input.Sorting))
        {
            query = query.OrderBy(input.Sorting);
        }
        else
        {
            query = query.OrderByDescending(x => x.CreationTime);
        }

        var totalCount = await AsyncExecuter.CountAsync(query);

        var entities = await AsyncExecuter.ToListAsync(
            query.PageBy(input.SkipCount, input.MaxResultCount)
        );

        var dtos = entities.Select(MapToGetListOutputDto).ToList();

        return new PagedResultDto<LocalVesselDto>(totalCount, dtos);
    }

    // DRY: Extracted mapping logic for ZoneDetail to VesselItemDto
    private VesselItemDto MapZoneDetailToItemDto(ZoneDetail zoneDetail)
    {
        var itemDto = _vesselMapper.MapZoneDetailToItemDtoWithType(zoneDetail, "LocalVessel");

        if (zoneDetail.Tenant != null)
        {
            itemDto.Tenant = _vesselMapper.MapTenantToTenantShortDto(zoneDetail.Tenant);
        }

        itemDto.Attachments = zoneDetail.DocAttachment != null && zoneDetail.DocAttachment.Any()
            ? zoneDetail.DocAttachment.Select(att => _vesselMapper.MapDocAttachmentToDto(att)).ToList()
            : new List<DocAttachmentSortDto>();

        return itemDto;
    }

    private IQueryable<LocalVesselEntity> ApplyDynamicQuery(IQueryable<LocalVesselEntity> query, QueryParametersDto parameters)
    {
        // Apply filters if provided
        if (parameters.FilterGroup?.Conditions != null && parameters.FilterGroup.Conditions.Count > 0)
        {
            query = DynamicQueryBuilder<LocalVesselEntity>.ApplyFilters(query, parameters.FilterGroup);
        }

        // Apply sorting
        if (parameters.Sort?.Count > 0)
        {
            query = DynamicQueryBuilder<LocalVesselEntity>.ApplyMultipleSorting(query, parameters.Sort);
        }
        else if (!string.IsNullOrEmpty(parameters.Sorting))
        {
            query = query.OrderBy(parameters.Sorting);
        }
        else
        {
            query = query.OrderByDescending(x => x.CreationTime);
        }

        return query;
    }


    // Generate next DocNum for a given postDate (YYMM + 4 digit increment, reset every month)
    public async Task<GenerateDocNumDto> GenerateNextDocNumAsync(DateTime postDate)
    {
        var prefix = postDate.ToString("yyMM");
        var queryable = await _localVesselRepository.GetQueryableAsync();
        var maxDocNum = queryable
            .Where(x => x.DocNum.StartsWith(prefix))
            .OrderByDescending(x => x.DocNum)
            .Select(x => x.DocNum)
            .FirstOrDefault();

        int nextIncrement = 1;
        if (!string.IsNullOrEmpty(maxDocNum) && maxDocNum.Length > 4)
        {
            var lastIncrementStr = maxDocNum.Substring(4, 5);
            if (int.TryParse(lastIncrementStr, out var lastIncrement))
            {
                nextIncrement = lastIncrement + 1;
            }
        }
        var docNum = $"{prefix}{nextIncrement:D5}";
        return new GenerateDocNumDto
        {
            DocNum = docNum,
        };
    }

    public override async Task<LocalVesselDto> CreateAsync(CreateUpdateLocalVesselDto input)
    {
        await CheckCreatePolicyAsync();

        var dt = input.PostingDate;
        var docNum = await GenerateNextDocNumAsync(dt.ToDateTime(TimeOnly.MinValue));
        input.DocNum = docNum.DocNum;

        var entity = _localVesselMapper.CreateEntityWithId(input, Guid.NewGuid());

        await _localVesselRepository.InsertAsync(entity, autoSave: false);

        // Set HeaderId for each item after entity is inserted (if needed)
        if (input.Items != null && input.Items.Count != 0)
        {
            foreach (var itemDto in input.Items)
            {
                // If HeaderId is Guid, assign entity.Id
                itemDto.HeaderId = entity.Id;
                itemDto.DocNum = entity.DocEntry;
                itemDto.DocType = EkbConsts.LocalVesselType.Local;

                var itemEntity = _zoneDetailMapper.CreateEntityWithId(itemDto, Guid.NewGuid());
                // Set CreatedBy from authenticated user on detail
                itemEntity.CreatedBy = CurrentUser.UserName ?? "System";
                // Ensure required fields are set
                itemEntity.IsUrgent = string.IsNullOrEmpty(itemEntity.IsUrgent) ? "N" : itemEntity.IsUrgent;
                itemEntity.IsScan = string.IsNullOrEmpty(itemEntity.IsScan) ? "N" : itemEntity.IsScan;
                itemEntity.IsOriginal = string.IsNullOrEmpty(itemEntity.IsOriginal) ? "N" : itemEntity.IsOriginal;
                itemEntity.IsSend = string.IsNullOrEmpty(itemEntity.IsSend) ? "N" : itemEntity.IsSend;
                itemEntity.IsFeOri = string.IsNullOrEmpty(itemEntity.IsFeOri) ? "N" : itemEntity.IsFeOri;
                itemEntity.IsFeSend = string.IsNullOrEmpty(itemEntity.IsFeSend) ? "N" : itemEntity.IsFeSend;
                itemEntity.IsChange = string.IsNullOrEmpty(itemEntity.IsChange) ? "N" : itemEntity.IsChange;
                itemEntity.Deleted = string.IsNullOrEmpty(itemEntity.Deleted) ? "N" : itemEntity.Deleted;
                itemEntity.DocType = string.IsNullOrEmpty(itemEntity.DocType) ? "Local" : itemEntity.DocType;
                await _zoneDetailRepository.InsertAsync(itemEntity, autoSave: false);
            }
        }

        // Save all changes in a single transaction
        await CurrentUnitOfWork.SaveChangesAsync();

        // Load the entity with includes for proper mapping
        var createdEntity = await _localVesselRepository.GetAsync(entity.Id);
        return MapToGetOutputDto(createdEntity);
    }

    public override async Task<LocalVesselDto> UpdateAsync(Guid id, CreateUpdateLocalVesselDto input)
    {
        await CheckUpdatePolicyAsync();

        var entity = await _localVesselRepository.GetAsync(id);
        _localVesselMapper.MapToEntity(input, entity);

        await _localVesselRepository.UpdateAsync(entity, autoSave: false);


        // Handle items update - update existing items and add new ones
        if (input.Items != null)
        {
            // Get existing items
            var existingItems = await _zoneDetailRepository.GetByHeaderIdAsync(id);

            // Process each item in the input
            for (int i = 0; i < input.Items.Count; i++)
            {
                var itemDto = input.Items[i];
                itemDto.HeaderId = id;

                if (i < existingItems.Count)
                {
                    // Update existing item
                    var existingItem = existingItems[i];
                    _zoneDetailMapper.MapToEntity(itemDto, existingItem);
                    await _zoneDetailRepository.UpdateAsync(existingItem, autoSave: false);
                }
                else
                {
                    // If HeaderId is Guid, assign entity.Id
                    itemDto.HeaderId = entity.Id;
                    itemDto.DocNum = entity.DocEntry;
                    itemDto.DocType = EkbConsts.LocalVesselType.Local;

                    var itemEntity = _zoneDetailMapper.CreateEntityWithId(itemDto, Guid.NewGuid());
                    // Set CreatedBy from authenticated user on detail
                    itemEntity.CreatedBy = CurrentUser.UserName ?? "System";
                    // Ensure required fields are set
                    itemEntity.IsUrgent = string.IsNullOrEmpty(itemEntity.IsUrgent) ? "N" : itemEntity.IsUrgent;
                    itemEntity.IsScan = string.IsNullOrEmpty(itemEntity.IsScan) ? "N" : itemEntity.IsScan;
                    itemEntity.IsOriginal = string.IsNullOrEmpty(itemEntity.IsOriginal) ? "N" : itemEntity.IsOriginal;
                    itemEntity.IsSend = string.IsNullOrEmpty(itemEntity.IsSend) ? "N" : itemEntity.IsSend;
                    itemEntity.IsFeOri = string.IsNullOrEmpty(itemEntity.IsFeOri) ? "N" : itemEntity.IsFeOri;
                    itemEntity.IsFeSend = string.IsNullOrEmpty(itemEntity.IsFeSend) ? "N" : itemEntity.IsFeSend;
                    itemEntity.IsChange = string.IsNullOrEmpty(itemEntity.IsChange) ? "N" : itemEntity.IsChange;
                    itemEntity.Deleted = string.IsNullOrEmpty(itemEntity.Deleted) ? "N" : itemEntity.Deleted;
                    itemEntity.DocType = string.IsNullOrEmpty(itemEntity.DocType) ? "Local" : itemEntity.DocType;
                    await _zoneDetailRepository.InsertAsync(itemEntity, autoSave: false);
                }
            }

            // Remove excess existing items if input has fewer items
            for (int i = input.Items.Count; i < existingItems.Count; i++)
            {
                await _zoneDetailRepository.DeleteAsync(existingItems[i], autoSave: false);
            }
        }

        // Save all changes in a single transaction
        await CurrentUnitOfWork.SaveChangesAsync();

        // Load the entity with includes for proper mapping
        var updatedEntity = await _localVesselRepository.GetAsync(entity.Id);
        return MapToGetOutputDto(updatedEntity);
    }



    public override async Task<LocalVesselDto> GetAsync(Guid id)
    {
        await CheckGetPolicyAsync();

        var entity = await _localVesselRepository.GetQueryableWithItemsSplitAsync(id);

        if (entity == null)
        {
            throw new UserFriendlyException("LocalVessel not found");
        }

        // Map the items from the navigation property
        var items = entity.Items?.Select(zoneDetail =>
        {
            var itemDto = _vesselMapper.MapZoneDetailToItemDtoWithType(zoneDetail, "LocalVessel");

            // Use navigation property for Tenant
            if (zoneDetail.Tenant != null)
            {
                itemDto.Tenant = _vesselMapper.MapTenantToTenantShortDto(zoneDetail.Tenant);
            }

            // Map DocAttachments if available
            if (zoneDetail.DocAttachment != null && zoneDetail.DocAttachment.Any())
            {
                itemDto.Attachments = zoneDetail.DocAttachment.Select(att => _vesselMapper.MapDocAttachmentToDto(att)).ToList();
            }
            else
            {
                itemDto.Attachments = new List<DocAttachmentSortDto>();
            }

            return itemDto;
        }).ToList() ?? new List<VesselItemDto>();

        return _localVesselMapper.MapToDtoWithItems(entity, items);
    }

    protected override LocalVesselDto MapToGetOutputDto(LocalVesselEntity entity)
    {
        return _localVesselMapper.MapToDto(entity);
    }

    protected override LocalVesselDto MapToGetListOutputDto(LocalVesselEntity entity)
    {
        return _localVesselMapper.MapToDto(entity);
    }

    protected override LocalVesselEntity MapToEntity(CreateUpdateLocalVesselDto createInput)
    {
        return _localVesselMapper.MapToEntity(createInput);
    }

    protected override void MapToEntity(CreateUpdateLocalVesselDto updateInput, LocalVesselEntity entity)
    {
        _localVesselMapper.MapToEntity(updateInput, entity);
    }

    private static string GetFileStreamUrl(string? path, string baseUrl)
    {
        // Return the URL
        return $"{baseUrl}/api/filestream/stream/ekb/{path}";
    }

    // Update these methods in LocalVesselAppService.cs:

    public async Task<LocalVesselWithItemsDto> GetWithItemsAsync(Guid id)
    {
        await CheckGetPolicyAsync();

        var vesselWithItems = await _localVesselRepository.GetQueryableWithItemsSplitAsync(id);

        if (vesselWithItems == null)
        {
            throw new UserFriendlyException("LocalVessel not found");
        }

        return vesselWithItems.MapToWithItemsDto(_localVesselMapper, _configuration);
    }

    public async Task<PagedResultDto<LocalVesselProjectionDto>> FilterListAsync(QueryParametersDto parameters)
    {
        await CheckGetListPolicyAsync();

        // Get queryable and apply filters/sorting
        var entityQuery = await _localVesselRepository.GetQueryableWithIncludesAsync();
        entityQuery = ApplyDynamicQuery(entityQuery, parameters);

        // Use the projection expression from mapper for optimal performance
        var projectedQuery = entityQuery.Select(LocalVesselMapper.ProjectionExpression);

        // Execute count and data queries efficiently
        var totalCount = await AsyncExecuter.CountAsync(projectedQuery);
        var items = await AsyncExecuter.ToListAsync(
            projectedQuery.Skip(parameters.SkipCount).Take(parameters.MaxResultCount)
        );

        return new PagedResultDto<LocalVesselProjectionDto>
        {
            TotalCount = totalCount,
            Items = items
        };
    }

}