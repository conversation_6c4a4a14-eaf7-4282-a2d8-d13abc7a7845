﻿using Imip.Ekb.Models;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;

namespace Imip.Ekb.Services;
public class DynamicQueryBuilder<T> where T : class
{
    // Expression caching for better performance
    private static readonly ConcurrentDictionary<string, Expression<Func<T, bool>>> _filterExpressionCache = new();
    private static readonly ConcurrentDictionary<string, PropertyInfo> _propertyInfoCache = new();
    private static readonly ConcurrentDictionary<string, MethodInfo> _methodInfoCache = new();

    /// <summary>
    /// Helper class to store information about a collection property and its element property
    /// </summary>
    private class CollectionPropertyInfo : Expression
    {
        public Expression Collection { get; }
        public ParameterExpression ElementParameter { get; }
        public Expression ElementProperty { get; }

        public CollectionPropertyInfo(Expression collection, ParameterExpression elementParameter, Expression elementProperty)
        {
            Collection = collection;
            ElementParameter = elementParameter;
            ElementProperty = elementProperty;
        }

        public override ExpressionType NodeType => ExpressionType.Extension;
        public override Type Type => ElementProperty.Type;
    }
    public static IQueryable<T> ApplyFilters(IQueryable<T> query, FilterGroup filterGroup)
    {
        if (filterGroup?.Conditions == null || !filterGroup.Conditions.Any())
            return query;

        // Generate cache key for this filter group
        var cacheKey = GenerateFilterCacheKey(filterGroup);

        // Try to get cached expression
        var lambda = _filterExpressionCache.GetOrAdd(cacheKey, _ =>
        {
            var parameter = Expression.Parameter(typeof(T), "x");
            var expression = BuildFilterExpression(parameter, filterGroup);

            return expression != null
                ? Expression.Lambda<Func<T, bool>>(expression, parameter)
                : null;
        });

        if (lambda != null)
        {
            query = query.Where(lambda);
        }

        return query;
    }

    /// <summary>
    /// Generate a cache key for a filter group
    /// </summary>
    private static string GenerateFilterCacheKey(FilterGroup filterGroup)
    {
        var keyParts = new List<string>
        {
            typeof(T).FullName ?? typeof(T).Name,
            filterGroup.Operator.ToString()
        };

        foreach (var condition in filterGroup.Conditions.OrderBy(c => c.FieldName))
        {
            keyParts.Add($"{condition.FieldName}:{condition.Operator}:{condition.Value}");
        }

        return string.Join("|", keyParts);
    }

    private static Expression? BuildFilterExpression(ParameterExpression parameter, FilterGroup filterGroup)
    {
        if (!filterGroup.Conditions.Any())
            return null;

        Expression? finalExpression = null;

        foreach (var condition in filterGroup.Conditions)
        {
            var expression = BuildConditionExpression(parameter, condition);

            if (finalExpression == null)
            {
                finalExpression = expression;
            }
            else
            {
                finalExpression = filterGroup.Operator == LogicalOperator.And
                    ? Expression.AndAlso(finalExpression, expression)
                    : Expression.OrElse(finalExpression, expression);
            }
        }

        return finalExpression;
    }

    private static Expression BuildConditionExpression(ParameterExpression parameter, FilterCondition condition)
    {
        try
        {
            if (!Enum.IsDefined(typeof(FilterOperator), condition.Operator))
            {
                throw new ArgumentException($"Invalid filter operator: {condition.Operator}");
            }

            if (string.IsNullOrEmpty(condition.FieldName))
            {
                throw new ArgumentException("Field name cannot be null or empty");
            }

            // Handle dot notation for related entities (e.g., "company.name")
            Expression property;
            try
            {
                if (condition.FieldName.Contains('.'))
                {
                    property = BuildPropertyPathExpression(parameter, condition.FieldName);
                }
                else
                {
                    property = Expression.Property(parameter, condition.FieldName);
                }
            }
            catch (Exception ex)
            {
                throw new ArgumentException($"Error accessing property '{condition.FieldName}': {ex.Message}", ex);
            }

            // Get the property type for conversion
            Type propertyType;
            bool isCollectionProperty = false;

            if (property is CollectionPropertyInfo collectionPropInfo)
            {
                propertyType = collectionPropInfo.ElementProperty.Type;
                isCollectionProperty = true;
            }
            else
            {
                propertyType = property.Type;
            }

            // Special handling for In operator
            if (condition.Operator == FilterOperator.In || condition.Operator == FilterOperator.NotIn)
            {
                return BuildInExpression(property, Expression.Constant(condition.Value));
            }

            // Special handling for Between operator
            if (condition.Operator == FilterOperator.Between || condition.Operator == FilterOperator.NotBetween)
            {
                var betweenExpression = BuildBetweenExpression(property, Expression.Constant(condition.Value));
                return condition.Operator == FilterOperator.Between ? betweenExpression : Expression.Not(betweenExpression);
            }

            // Convert the value to the property type if possible
            object? convertedValue = ConvertValueToPropertyType(condition.Value, propertyType);

            // Ensure the constant expression uses the correct type to avoid nullable/non-nullable mismatches
            Type constantType = propertyType;
            if (convertedValue == null)
            {
                // For null values, always use the property type (which handles nullable types correctly)
                constantType = propertyType;
            }
            else if (convertedValue != null)
            {
                // For non-null values, we need to be careful about nullable vs non-nullable types
                Type underlyingType = Nullable.GetUnderlyingType(propertyType);
                if (underlyingType != null)
                {
                    // Property is nullable, but we have a non-null value
                    // Check if the converted value matches the underlying type
                    if (convertedValue.GetType() == underlyingType)
                    {
                        // Use the property type (nullable) to ensure compatibility
                        constantType = propertyType;
                    }
                    else
                    {
                        // Try to convert to the underlying type first
                        try
                        {
                            convertedValue = ConvertValueToPropertyType(convertedValue, underlyingType);
                            constantType = propertyType;
                        }
                        catch
                        {
                            constantType = propertyType;
                        }
                    }
                }
                else
                {
                    // Property is not nullable, use the property type
                    constantType = propertyType;
                }
            }

            var value = Expression.Constant(convertedValue, constantType);

            return condition.Operator switch
            {
                FilterOperator.Equals => isCollectionProperty
                    ? BuildAnyPredicateExpression((CollectionPropertyInfo)property, value)
                    : Expression.Equal(property, value),
                FilterOperator.NotEquals => isCollectionProperty
                    ? Expression.Not(BuildAnyPredicateExpression((CollectionPropertyInfo)property, value))
                    : Expression.NotEqual(property, value),
                FilterOperator.Contains => BuildStringOperation(property, value, "Contains"),
                FilterOperator.StartsWith => BuildStringOperation(property, value, "StartsWith"),
                FilterOperator.EndsWith => BuildStringOperation(property, value, "EndsWith"),
                FilterOperator.GreaterThan => Expression.GreaterThan(property, value),
                FilterOperator.GreaterThanOrEqual => Expression.GreaterThanOrEqual(property, value),
                FilterOperator.LessThan => Expression.LessThan(property, value),
                FilterOperator.LessThanOrEqual => Expression.LessThanOrEqual(property, value),
                FilterOperator.IsNull => Expression.Equal(property, Expression.Constant(null)),
                FilterOperator.IsNotNull => Expression.NotEqual(property, Expression.Constant(null)),
                FilterOperator.IsEmpty => Expression.Equal(property, Expression.Constant(string.Empty)),
                FilterOperator.IsNotEmpty => Expression.NotEqual(property, Expression.Constant(string.Empty)),
                FilterOperator.IsTrue => Expression.Equal(property, Expression.Constant(true)),
                FilterOperator.IsFalse => Expression.Equal(property, Expression.Constant(false)),
                FilterOperator.IsNullOrEmpty => Expression.Call(typeof(string), "IsNullOrEmpty", null, property),
                FilterOperator.IsNotNullOrEmpty => Expression.Not(Expression.Call(typeof(string), "IsNullOrEmpty", null, property)),
                FilterOperator.IsNullOrWhiteSpace => Expression.Call(typeof(string), "IsNullOrWhiteSpace", null, property),
                FilterOperator.IsNotNullOrWhiteSpace => Expression.Not(Expression.Call(typeof(string), "IsNullOrWhiteSpace", null, property)),
                FilterOperator.IsNumeric => BuildRegexExpression(property, @"^\d+$"),
                FilterOperator.IsAlpha => BuildRegexExpression(property, @"^[a-zA-Z]+$"),
                FilterOperator.IsAlphaNumeric => BuildRegexExpression(property, @"^[a-zA-Z0-9]+$"),
                FilterOperator.IsEmail => BuildRegexExpression(property, @"^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$"),
                FilterOperator.IsUrl => BuildRegexExpression(property, @"^(http|https):\/\/[^ ""]+$"),
                FilterOperator.IsIp => BuildRegexExpression(property, @"^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$"),
                FilterOperator.IsIpv4 => BuildRegexExpression(property, @"^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$"),
                FilterOperator.IsIpv6 => BuildRegexExpression(property, @"^(?:(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|(?:[0-9a-fA-F]{1,4}:){1,7}:|(?:[0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|(?:[0-9a-fA-F]{1,4}:){1,5}(?::[0-9a-fA-F]{1,4}){1,2}|(?:[0-9a-fA-F]{1,4}:){1,4}(?::[0-9a-fA-F]{1,4}){1,3}|(?:[0-9a-fA-F]{1,4}:){1,3}(?::[0-9a-fA-F]{1,4}){1,4}|(?:[0-9a-fA-F]{1,4}:){1,2}(?::[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:(?:(?::[0-9a-fA-F]{1,4}){1,6})|:(?:(?::[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(?::[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(?:ffff(?::0{1,4}){0,1}:){0,1}(?:(?:25[0-5]|(?:2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(?:25[0-5]|(?:2[0-4]|1{0,1}[0-9]){0,1}[0-9])|(?:[0-9a-fA-F]{1,4}:){1,4}:(?:(?:25[0-5]|(?:2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(?:25[0-5]|(?:2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$"),
                FilterOperator.IsGuid => BuildIsGuidExpression(property),
                FilterOperator.IsGuidEmpty => Expression.Equal(property, Expression.Constant(Guid.Empty)),
                FilterOperator.IsGuidNotEmpty => Expression.NotEqual(property, Expression.Constant(Guid.Empty)),
                FilterOperator.IsGuidNull => Expression.Equal(property, Expression.Constant(null)),
                FilterOperator.IsGuidNotNull => Expression.NotEqual(property, Expression.Constant(null)),
                FilterOperator.IsGuidNullOrEmpty => Expression.OrElse(
                    Expression.Equal(property, Expression.Constant(null)),
                    Expression.Equal(property, Expression.Constant(Guid.Empty))),
                FilterOperator.IsGuidNotNullOrEmpty => Expression.AndAlso(
                    Expression.NotEqual(property, Expression.Constant(null)),
                    Expression.NotEqual(property, Expression.Constant(Guid.Empty))),
                FilterOperator.IsGuidNullOrWhiteSpace => Expression.OrElse(
                    Expression.Equal(property, Expression.Constant(null)),
                    Expression.Equal(property, Expression.Constant(Guid.Empty))),
                FilterOperator.IsGuidNotNullOrWhiteSpace => Expression.AndAlso(
                    Expression.NotEqual(property, Expression.Constant(null)),
                    Expression.NotEqual(property, Expression.Constant(Guid.Empty))),
                FilterOperator.IsGuidNumeric => Expression.AndAlso(
                    BuildIsGuidExpression(property),
                    BuildRegexExpression(property, @"^[0-9-]+$")),
                FilterOperator.IsGuidAlpha => Expression.AndAlso(
                    BuildIsGuidExpression(property),
                    BuildRegexExpression(property, @"^[a-fA-F-]+$")),
                FilterOperator.IsGuidAlphaNumeric => Expression.AndAlso(
                    BuildIsGuidExpression(property),
                    BuildRegexExpression(property, @"^[0-9a-fA-F-]+$")),
                _ => throw new NotSupportedException($"Operator {condition.Operator} is not supported.")
            };
        }
        catch (Exception ex)
        {
            throw new ArgumentException($"Error building condition for field '{condition.FieldName}' with operator '{condition.Operator}': {ex.Message}", ex);
        }
    }

    private static MethodCallExpression BuildStringOperation(Expression property, Expression value, string methodName)
    {
        try
        {
            // Handle CollectionPropertyInfo
            if (property is CollectionPropertyInfo collectionInfo)
            {
                // For collection properties, we need to create a predicate that checks if any element's property
                // matches the string operation

                // Get the element type
                Type elementType = GetElementType(collectionInfo.Collection.Type);

                // Ensure element property is a string by converting it if needed
                Expression elementStringProperty = collectionInfo.ElementProperty;
                if (collectionInfo.ElementProperty.Type != typeof(string))
                {
                    var toStringMethod = typeof(object).GetMethod("ToString")
                        ?? throw new InvalidOperationException("Could not find ToString method on object type");
                    elementStringProperty = Expression.Call(collectionInfo.ElementProperty, toStringMethod);
                }

                // Ensure value is a string by converting it if needed
                Expression stringValue = value;
                if (value.Type != typeof(string))
                {
                    // If value is a constant, extract its value and convert to string
                    if (value is ConstantExpression constantExpr && constantExpr.Value != null)
                    {
                        stringValue = Expression.Constant(constantExpr.Value.ToString());
                    }
                    else
                    {
                        var toStringMethod = typeof(object).GetMethod("ToString")
                            ?? throw new InvalidOperationException("Could not find ToString method on object type");
                        stringValue = Expression.Call(value, toStringMethod);
                    }
                }

                // Get the method info for the string method (Contains, StartsWith, EndsWith)
                var stringMethodInfo = typeof(string).GetMethod(methodName, new[] { typeof(string) })
                    ?? throw new InvalidOperationException($"Could not find {methodName}(string) method on string type");

                // Create the string operation expression
                var stringOpExpression = Expression.Call(elementStringProperty, stringMethodInfo, stringValue);

                // Create a lambda for the Any method
                var predicateLambda = Expression.Lambda(stringOpExpression, collectionInfo.ElementParameter);

                // Get the Any method from Enumerable
                var anyMethod = typeof(Enumerable)
                    .GetMethods()
                    .First(m => m.Name == "Any" && m.GetParameters().Length == 2)
                    .MakeGenericMethod(elementType);

                // Create the method call: collection.Any(e => e.Property.Contains/StartsWith/EndsWith(value))
                return Expression.Call(null, anyMethod, collectionInfo.Collection, predicateLambda);
            }

            // Regular property handling
            // Ensure property is a string by converting it if needed
            Expression regularStringProperty = property;
            if (property.Type != typeof(string))
            {
                var toStringMethod = typeof(object).GetMethod("ToString")
                    ?? throw new InvalidOperationException("Could not find ToString method on object type");
                regularStringProperty = Expression.Call(property, toStringMethod);
            }

            // Ensure value is a string by converting it if needed
            Expression regularStringValue = value;
            if (value.Type != typeof(string))
            {
                // If value is a constant, extract its value and convert to string
                if (value is ConstantExpression constantExpr && constantExpr.Value != null)
                {
                    regularStringValue = Expression.Constant(constantExpr.Value.ToString());
                }
                else
                {
                    var toStringMethod = typeof(object).GetMethod("ToString")
                        ?? throw new InvalidOperationException("Could not find ToString method on object type");
                    regularStringValue = Expression.Call(value, toStringMethod);
                }
            }

            // Get the method info for the string method (Contains, StartsWith, EndsWith)
            var regularStringMethodInfo = typeof(string).GetMethod(methodName, new[] { typeof(string) })
                ?? throw new InvalidOperationException($"Could not find {methodName}(string) method on string type");

            // Now that we have string expressions, we can call the string method with case-insensitive comparison
            // For EF Core compatibility, we'll use the overload that takes a string parameter
            // First convert both sides to lowercase for case-insensitive comparison
            var toLowerMethod = typeof(string).GetMethod("ToLower", Type.EmptyTypes)
                ?? throw new InvalidOperationException("Could not find ToLower method on string type");

            var propertyToLower = Expression.Call(regularStringProperty, toLowerMethod);
            var valueToLower = Expression.Call(regularStringValue, toLowerMethod);

            // Call the string method (e.g., propertyToLower.Contains(valueToLower))
            return Expression.Call(propertyToLower, regularStringMethodInfo, valueToLower);
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"Error building string operation '{methodName}': {ex.Message}", ex);
        }
    }

    private static MethodCallExpression BuildInExpression(Expression property, Expression value)
    {
        try
        {
            // Get the actual value from the expression
            object? rawValue = Expression.Lambda(value).Compile().DynamicInvoke();

            // Get the property type
            Type propertyType = property.Type;

            // Create a generic list type matching the property type
            Type listType = typeof(List<>).MakeGenericType(propertyType);

            // Create an instance of the typed list
            var typedList = Activator.CreateInstance(listType);

            // Get the Add method for the list
            var addMethod = listType.GetMethod("Add");
            if (addMethod == null)
                throw new InvalidOperationException("Could not find Add method on list type");

            // Handle different input types
            if (rawValue is IEnumerable<object> enumerable)
            {
                // If it's already an enumerable of objects, convert each value
                foreach (var val in enumerable)
                {
                    var convertedVal = ConvertValueToPropertyType(val, propertyType);
                    if (convertedVal != null)
                    {
                        addMethod.Invoke(typedList, new[] { convertedVal });
                    }
                }
            }
            else if (rawValue is string stringValue)
            {
                // If it's a comma-separated string, split it and convert each value
                var items = stringValue.Split(',', StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries);
                foreach (var item in items)
                {
                    var convertedVal = ConvertValueToPropertyType(item, propertyType);
                    if (convertedVal != null)
                    {
                        addMethod.Invoke(typedList, new[] { convertedVal });
                    }
                }
            }
            else if (rawValue is System.Text.Json.JsonElement jsonElement)
            {
                // Handle JsonElement
                if (jsonElement.ValueKind == System.Text.Json.JsonValueKind.Array)
                {
                    // If it's a JSON array, convert each element
                    foreach (var element in jsonElement.EnumerateArray())
                    {
                        var convertedVal = ConvertValueToPropertyType(element, propertyType);
                        if (convertedVal != null)
                        {
                            addMethod.Invoke(typedList, new[] { convertedVal });
                        }
                    }
                }
                else if (jsonElement.ValueKind == System.Text.Json.JsonValueKind.String)
                {
                    // If it's a JSON string, treat it as comma-separated
                    var stringVal = jsonElement.GetString() ?? string.Empty;
                    var items = stringVal.Split(',', StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries);
                    foreach (var item in items)
                    {
                        var convertedVal = ConvertValueToPropertyType(item, propertyType);
                        if (convertedVal != null)
                        {
                            addMethod.Invoke(typedList, new[] { convertedVal });
                        }
                    }
                }
                else
                {
                    // For other types, try to convert the value
                    var convertedVal = ConvertValueToPropertyType(jsonElement, propertyType);
                    if (convertedVal != null)
                    {
                        addMethod.Invoke(typedList, new[] { convertedVal });
                    }
                }
            }
            else if (rawValue != null)
            {
                // For any other non-null value, try to convert it
                var convertedVal = ConvertValueToPropertyType(rawValue, propertyType);
                if (convertedVal != null)
                {
                    addMethod.Invoke(typedList, new[] { convertedVal });
                }
            }

            // If we have no values, throw an exception
            var countProperty = listType.GetProperty("Count");
            if (countProperty == null)
                throw new InvalidOperationException("Could not find Count property on list type");

            var count = (int)countProperty.GetValue(typedList)!;
            if (count == 0)
            {
                throw new ArgumentException("No values provided for In operator");
            }

            // Get the Contains method from Enumerable that matches our property type
            var containsMethod = typeof(Enumerable)
                .GetMethods()
                .First(m => m.Name == "Contains" && m.GetParameters().Length == 2)
                .MakeGenericMethod(propertyType);

            // Create the method call expression with the properly typed list
            return Expression.Call(null, containsMethod, Expression.Constant(typedList), property);
        }
        catch (Exception ex)
        {
            throw new ArgumentException($"Error processing In operator: {ex.Message}", ex);
        }
    }

    private static BinaryExpression BuildBetweenExpression(Expression property, Expression value)
    {
        try
        {
            // Get the actual value from the expression
            object? rawValue = Expression.Lambda(value).Compile().DynamicInvoke();

            // Create a list to hold our values
            List<object> valuesList = new List<object>();

            // Handle different input types
            if (rawValue is IEnumerable<object> enumerable)
            {
                // If it's already an enumerable of objects, use it directly
                valuesList.AddRange(enumerable);
            }
            else if (rawValue is string stringValue)
            {
                // If it's a comma-separated string, split it and add each value
                var items = stringValue.Split(',', StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries);
                valuesList.AddRange(items);
            }
            else if (rawValue is System.Text.Json.JsonElement jsonElement)
            {
                // Handle JsonElement
                if (jsonElement.ValueKind == System.Text.Json.JsonValueKind.Array)
                {
                    // If it's a JSON array, add each element
                    foreach (var element in jsonElement.EnumerateArray())
                    {
                        if (element.ValueKind == System.Text.Json.JsonValueKind.String)
                        {
                            valuesList.Add(element.GetString() ?? string.Empty);
                        }
                        else
                        {
                            valuesList.Add(element.ToString());
                        }
                    }
                }
                else if (jsonElement.ValueKind == System.Text.Json.JsonValueKind.String)
                {
                    // If it's a JSON string, treat it as comma-separated
                    var stringVal = jsonElement.GetString() ?? string.Empty;
                    var items = stringVal.Split(',', StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries);
                    valuesList.AddRange(items);
                }
            }

            // Ensure we have exactly 2 values for the Between operator
            if (valuesList.Count != 2)
            {
                throw new ArgumentException("Between operator requires exactly 2 values");
            }

            // Convert values to the property type if needed
            var startValue = ConvertValueToPropertyType(valuesList[0], property.Type);
            var endValue = ConvertValueToPropertyType(valuesList[1], property.Type);

            // Create properly typed constant expressions
            var start = Expression.Constant(startValue, property.Type);
            var end = Expression.Constant(endValue, property.Type);

            return Expression.AndAlso(
                Expression.GreaterThanOrEqual(property, start),
                Expression.LessThanOrEqual(property, end)
            );
        }
        catch (Exception ex)
        {
            throw new ArgumentException($"Error processing Between operator: {ex.Message}", ex);
        }
    }

    private static MethodCallExpression BuildRegexExpression(Expression property, string pattern)
    {
        var regexType = typeof(System.Text.RegularExpressions.Regex);
        var isMatchMethod = regexType.GetMethod("IsMatch", new[] { typeof(string), typeof(string) })
            ?? throw new InvalidOperationException("Could not find IsMatch method on Regex type");

        var toStringMethod = typeof(object).GetMethod("ToString")
            ?? throw new InvalidOperationException("Could not find ToString method on object type");

        return Expression.Call(null, isMatchMethod,
            Expression.Call(property, toStringMethod),
            Expression.Constant(pattern));
    }

    private static MethodCallExpression BuildIsGuidExpression(Expression property)
    {
        var guidType = typeof(Guid);
        var parseMethod = guidType.GetMethod("TryParse", new[] { typeof(string), typeof(Guid).MakeByRefType() })
            ?? throw new InvalidOperationException("Could not find TryParse method on Guid type");

        var toStringMethod = typeof(object).GetMethod("ToString")
            ?? throw new InvalidOperationException("Could not find ToString method on object type");

        var stringValue = Expression.Call(property, toStringMethod);
        var guidOutParam = Expression.Parameter(typeof(Guid).MakeByRefType());

        return Expression.Call(null, parseMethod, stringValue, guidOutParam);
    }

    /// <summary>
    /// Builds a property path expression for navigating through related entities
    /// </summary>
    private static Expression BuildPropertyPathExpression(ParameterExpression parameter, string propertyPath)
    {
        // Split the property path by dots
        var properties = propertyPath.Split('.');

        // Start with the parameter expression
        Expression propertyExpression = parameter;

        // Navigate through each property in the path
        for (int i = 0; i < properties.Length; i++)
        {
            string prop = properties[i];

            try
            {
                // Get the property from the current expression
                propertyExpression = Expression.Property(propertyExpression, prop);

                // Check if the property is a collection
                if (i < properties.Length - 1 && IsCollectionType(propertyExpression.Type))
                {
                    // For collections, we need to use the Any method to check if any element matches the condition
                    // This is a special case for filtering on collection properties

                    // Get the element type of the collection
                    Type elementType = GetElementType(propertyExpression.Type);

                    // Create a parameter for the element
                    ParameterExpression elementParam = Expression.Parameter(elementType, "e");

                    // Build the property path for the remaining properties on the element
                    string remainingPath = string.Join(".", properties.Skip(i + 1));
                    Expression elementProperty = BuildPropertyPathExpression(elementParam, remainingPath);

                    // Create a lambda for the Any method
                    LambdaExpression lambda = Expression.Lambda(elementProperty, elementParam);

                    // Get the Any method from Enumerable
                    var anyMethod = typeof(Enumerable)
                        .GetMethods()
                        .First(m => m.Name == "Any" && m.GetParameters().Length == 2)
                        .MakeGenericMethod(elementType);

                    // Create a predicate for the Any method
                    // For equality comparison, we need to create a lambda that compares the property to the value
                    // This will be used later in BuildConditionExpression

                    // Store the original property expression and element parameter for later use
                    // We'll use this in BuildAnyPredicateExpression
                    propertyExpression = new CollectionPropertyInfo(propertyExpression, elementParam, elementProperty);

                    // We've processed all properties in the path
                    break;
                }
            }
            catch (ArgumentException ex)
            {
                throw new ArgumentException($"Error accessing property '{prop}' in path '{propertyPath}': {ex.Message}", ex);
            }
        }

        return propertyExpression;
    }

    /// <summary>
    /// Checks if a type is a collection type
    /// </summary>
    private static bool IsCollectionType(Type type)
    {
        // Check if the type implements IEnumerable<T> (but not string)
        return type != typeof(string) &&
               type.GetInterfaces()
                   .Any(i => i.IsGenericType &&
                        i.GetGenericTypeDefinition() == typeof(IEnumerable<>));
    }

    /// <summary>
    /// Gets the element type of a collection
    /// </summary>
    private static Type GetElementType(Type collectionType)
    {
        // Get the element type from the IEnumerable<T> interface
        foreach (var interfaceType in collectionType.GetInterfaces())
        {
            if (interfaceType.IsGenericType &&
                interfaceType.GetGenericTypeDefinition() == typeof(IEnumerable<>))
            {
                return interfaceType.GetGenericArguments()[0];
            }
        }

        // If it's an array, get the element type directly
        if (collectionType.IsArray)
        {
            return collectionType.GetElementType() ??
                throw new ArgumentException($"Could not determine element type for array type {collectionType.Name}");
        }

        // If it's a generic collection, get the first generic argument
        if (collectionType.IsGenericType)
        {
            return collectionType.GetGenericArguments()[0];
        }

        throw new ArgumentException($"Could not determine element type for collection type {collectionType.Name}");
    }

    /// <summary>
    /// Builds an expression to check if any element in a collection matches a predicate
    /// </summary>
    private static MethodCallExpression BuildAnyPredicateExpression(CollectionPropertyInfo collectionInfo, Expression value)
    {
        try
        {
            // Get the element type of the collection
            Type elementType = GetElementType(collectionInfo.Collection.Type);

            // Convert the value to the element property type if needed
            object? convertedValue = ConvertValueToPropertyType(
                Expression.Lambda(value).Compile().DynamicInvoke(),
                collectionInfo.ElementProperty.Type);

            var typedValue = Expression.Constant(convertedValue, collectionInfo.ElementProperty.Type);

            // Create a predicate that compares the element property to the value
            Expression equalityPredicate = Expression.Equal(collectionInfo.ElementProperty, typedValue);

            // Create a lambda for the predicate
            var predicateLambda = Expression.Lambda(equalityPredicate, collectionInfo.ElementParameter);

            // Get the Any method from Enumerable
            var anyMethod = typeof(Enumerable)
                .GetMethods()
                .First(m => m.Name == "Any" && m.GetParameters().Length == 2)
                .MakeGenericMethod(elementType);

            // Create the method call: collection.Any(e => e.Property == value)
            return Expression.Call(null, anyMethod, collectionInfo.Collection, predicateLambda);
        }
        catch (Exception ex)
        {
            throw new ArgumentException($"Error building Any predicate expression: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// Builds an expression to check if a collection contains a value
    /// </summary>
    private static Expression BuildCollectionContainsExpression(Expression collection, Expression value)
    {
        try
        {
            // Get the element type of the collection
            Type elementType = GetElementType(collection.Type);

            // Convert the value to the element type if needed
            object? convertedValue = ConvertValueToPropertyType(
                Expression.Lambda(value).Compile().DynamicInvoke(),
                elementType);

            var typedValue = Expression.Constant(convertedValue, elementType);

            // Get the Contains method from Enumerable
            var containsMethod = typeof(Enumerable)
                .GetMethods()
                .First(m => m.Name == "Contains" && m.GetParameters().Length == 2)
                .MakeGenericMethod(elementType);

            // Create the method call: Enumerable.Contains(collection, value)
            return Expression.Call(null, containsMethod, collection, typedValue);
        }
        catch (Exception ex)
        {
            throw new ArgumentException($"Error building collection contains expression: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// Converts a value to the property type if possible
    /// </summary>
    private static object? ConvertValueToPropertyType(object? value, Type propertyType)
    {
        if (value == null)
            return null;

        try
        {
            // Handle System.Text.Json.JsonElement
            if (value is System.Text.Json.JsonElement jsonElement)
            {
                // Convert JsonElement to appropriate type based on its value kind
                switch (jsonElement.ValueKind)
                {
                    case System.Text.Json.JsonValueKind.String:
                        value = jsonElement.GetString() ?? string.Empty;
                        break;
                    case System.Text.Json.JsonValueKind.Number:
                        if (jsonElement.TryGetInt32(out int intValue))
                            value = intValue;
                        else if (jsonElement.TryGetInt64(out long longValue))
                            value = longValue;
                        else if (jsonElement.TryGetDouble(out double doubleValue))
                            value = doubleValue;
                        else if (jsonElement.TryGetDecimal(out decimal decimalValue))
                            value = decimalValue;
                        break;
                    case System.Text.Json.JsonValueKind.True:
                        value = true;
                        break;
                    case System.Text.Json.JsonValueKind.False:
                        value = false;
                        break;
                    case System.Text.Json.JsonValueKind.Null:
                        return null;
                    default:
                        value = jsonElement.ToString() ?? string.Empty;
                        break;
                }
            }

            // Handle nullable types
            Type targetType = Nullable.GetUnderlyingType(propertyType) ?? propertyType;
            bool isNullableType = Nullable.GetUnderlyingType(propertyType) != null;

            // If the value is already of the correct type, return it
            if (value.GetType() == targetType || value.GetType() == propertyType)
                return value;

            // Handle special cases
            if (targetType == typeof(Guid))
            {
                if (value is string guidString)
                {
                    if (Guid.TryParse(guidString, out Guid parsedGuid))
                        return parsedGuid;
                }
                else if (value is Guid guidValue)
                {
                    return guidValue;
                }
            }

            // Handle DateTime conversion (both nullable and non-nullable)
            if (targetType == typeof(DateTime))
            {
                if (value is string dateString)
                {
                    // Try parsing as DateTime first
                    if (DateTime.TryParse(dateString, out DateTime parsedDate))
                        return parsedDate;

                    // Try parsing as DateTimeOffset and convert to DateTime
                    if (DateTimeOffset.TryParse(dateString, out DateTimeOffset parsedOffset))
                        return parsedOffset.DateTime;
                }
                else if (value is DateTime dateTimeValue)
                {
                    return dateTimeValue;
                }
                else if (value is DateTimeOffset offsetValue)
                {
                    return offsetValue.DateTime;
                }
            }

            // Handle DateTimeOffset conversion
            if (targetType == typeof(DateTimeOffset))
            {
                if (value is string dateString)
                {
                    if (DateTimeOffset.TryParse(dateString, out DateTimeOffset parsedOffset))
                        return parsedOffset;

                    // Try parsing as DateTime and convert to DateTimeOffset
                    if (DateTime.TryParse(dateString, out DateTime parsedDate))
                        return new DateTimeOffset(parsedDate);
                }
                else if (value is DateTimeOffset offsetValue)
                {
                    return offsetValue;
                }
                else if (value is DateTime dateTimeValue)
                {
                    return new DateTimeOffset(dateTimeValue);
                }
            }

            // Handle enum types
            if (targetType.IsEnum)
            {
                if (value is string stringValue)
                {
                    // Try to parse as enum name first
                    if (Enum.TryParse(targetType, stringValue, true, out object? enumResult))
                        return enumResult;
                }
                else if (value is int intValue)
                {
                    // Try to convert from integer value
                    if (Enum.IsDefined(targetType, intValue))
                        return Enum.ToObject(targetType, intValue);
                }
                else if (value != null)
                {
                    // Try to convert the value to string first, then parse as enum
                    string valueAsString = value.ToString() ?? string.Empty;
                    if (Enum.TryParse(targetType, valueAsString, true, out object? enumResult))
                        return enumResult;

                    // Try to parse as integer if string parsing failed
                    if (int.TryParse(valueAsString, out int intVal) && Enum.IsDefined(targetType, intVal))
                        return Enum.ToObject(targetType, intVal);
                }
            }

            // Try to convert using Convert.ChangeType
            if (targetType.IsPrimitive || targetType == typeof(string) || targetType == typeof(decimal))
                return Convert.ChangeType(value, targetType);
        }
        catch
        {
            // If conversion fails, return the original value
        }

        return value;
    }

    public static IQueryable<T> ApplySorting(IQueryable<T> query, string sortBy, bool sortDesc)
    {
        if (string.IsNullOrEmpty(sortBy))
            return query;

        var parameter = Expression.Parameter(typeof(T), "x");

        // Handle dot notation for related entities
        Expression property;
        if (sortBy.Contains('.'))
        {
            property = BuildPropertyPathExpression(parameter, sortBy);
        }
        else
        {
            property = Expression.Property(parameter, sortBy);
        }

        var lambda = Expression.Lambda(property, parameter);

        var methodName = sortDesc ? "OrderByDescending" : "OrderBy";
        var resultExpression = Expression.Call(
            typeof(Queryable),
            methodName,
            new[] { typeof(T), property.Type },
            query.Expression,
            Expression.Quote(lambda)
        );

        return query.Provider.CreateQuery<T>(resultExpression);
    }

    public static IQueryable<T> ApplyMultipleSorting(IQueryable<T> query, IEnumerable<SortInfo> sortFields)
    {
        if (sortFields == null || !sortFields.Any())
            return query;

        // Convert to list to avoid multiple enumeration
        var sortFieldsList = sortFields.ToList();

        IOrderedQueryable<T>? orderedQuery = null;
        bool isFirst = true;

        foreach (var sort in sortFieldsList)
        {
            if (string.IsNullOrEmpty(sort.Field))
                continue;

            var parameter = Expression.Parameter(typeof(T), "x");

            // Handle dot notation for related entities
            Expression property;
            if (sort.Field.Contains('.'))
            {
                property = BuildPropertyPathExpression(parameter, sort.Field);
            }
            else
            {
                property = Expression.Property(parameter, sort.Field);
            }

            var lambda = Expression.Lambda(property, parameter);

            if (isFirst)
            {
                var methodName = sort.Desc ? "OrderByDescending" : "OrderBy";
                var resultExpression = Expression.Call(
                    typeof(Queryable),
                    methodName,
                    new[] { typeof(T), property.Type },
                    query.Expression,
                    Expression.Quote(lambda)
                );
                orderedQuery = (IOrderedQueryable<T>)query.Provider.CreateQuery<T>(resultExpression);
                isFirst = false;
            }
            else if (orderedQuery != null)
            {
                var methodName = sort.Desc ? "ThenByDescending" : "ThenBy";
                var resultExpression = Expression.Call(
                    typeof(Queryable),
                    methodName,
                    new[] { typeof(T), property.Type },
                    orderedQuery.Expression,
                    Expression.Quote(lambda)
                );
                orderedQuery = (IOrderedQueryable<T>)orderedQuery.Provider.CreateQuery<T>(resultExpression);
            }
        }

        return orderedQuery ?? query;
    }
}
