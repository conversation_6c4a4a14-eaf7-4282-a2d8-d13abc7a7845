﻿using Imip.Ekb.Master.Cargos;
using Imip.Ekb.Master.Cargos.Dtos;
using Riok.Mapperly.Abstractions;
using System;
using System.Collections.Generic;

namespace Imip.Ekb.Mapping.Mappers;

[Mapper]
public partial class CargoMapper : IMapperlyMapper
{
    // Entity to DTO mapping
    [MapProperty(nameof(Cargo.Id), nameof(CargoDto.Id))]
    [MapperIgnoreSource(nameof(Cargo.IsDeleted))]
    [MapperIgnoreSource(nameof(Cargo.DeleterId))]
    [MapperIgnoreSource(nameof(Cargo.DeletionTime))]
    [MapperIgnoreSource(nameof(Cargo.LastModificationTime))]
    [MapperIgnoreSource(nameof(Cargo.LastModifierId))]
    [MapperIgnoreSource(nameof(Cargo.CreationTime))]
    [MapperIgnoreSource(nameof(Cargo.CreatorId))]
    [MapperIgnoreSource(nameof(Cargo.ExtraProperties))]
    [MapperIgnoreSource(nameof(Cargo.ConcurrencyStamp))]
    public partial CargoDto MapToDto(Cargo entity);

    [MapperIgnoreTarget(nameof(Cargo.Id))] // Don't change existing Id
    [MapperIgnoreTarget(nameof(Cargo.CreatedBy))] // Don't overwrite audit fields
    [MapperIgnoreTarget(nameof(Cargo.CreatedAt))]
    public partial void MapToEntity(CargoCreateUpdateDto dto, Cargo entity);

    // Custom mapping methods for complex scenarios
    public Cargo CreateEntityWithId(CargoCreateUpdateDto dto, Guid id)
    {
        // Create entity using reflection since constructor is protected
        var entity = (Cargo)Activator.CreateInstance(typeof(Cargo), true)!;

        // Map properties manually
        MapToEntity(dto, entity);

        return entity;
    }

    // Map list of entities to DTOs
    public partial List<CargoDto> MapToDtoList(List<Cargo> entities);

    // Map IEnumerable for LINQ scenarios
    public partial IEnumerable<CargoDto> MapToDtoEnumerable(IEnumerable<Cargo> entities);

    public static System.Linq.Expressions.Expression<System.Func<Cargo, CargoDto>> ProjectionExpression =>
        entity => new CargoDto
        {
            Id = entity.Id,
            DocEntry = entity.DocEntry,
            Name = entity.Name,
            CreatedBy = entity.CreatedBy,
            CreatedAt = entity.CreatedAt,
            UpdatedAt = entity.UpdatedAt,
            Status = entity.Status.Trim(), // Trim any remaining spaces for safety
            Alias = entity.Alias,
            Flag = entity.Flag,
            GrossWeight = entity.GrossWeight,
            Type = entity.Type,
            LoaQty = entity.LoaQty,

        };
}