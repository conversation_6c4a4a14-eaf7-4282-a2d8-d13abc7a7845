using System;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using Imip.Ekb.Mapping.Mappers;
using Imip.Ekb.Master.PortOfLoadings.Dtos;
using Imip.Ekb.Models;
using Imip.Ekb.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.Ekb.Master.PortOfLoadings;

[Authorize]
public class PortOfLoadingAppService :
    CrudAppService<PortOfLoading, PortOfLoadingDto, Guid, PagedAndSortedResultRequestDto, PortOfLoadingCreateUpdateDto, PortOfLoadingCreateUpdateDto>,
    IPortOfLoadingAppService
{
    private readonly IPortOfLoadingRepository _portOfLoadingRepository;
    private readonly PortOfLoadingMapper _mapper;
    private readonly ILogger<PortOfLoadingAppService> _logger;

    public PortOfLoadingAppService(
        IPortOfLoadingRepository portOfLoadingRepository,
        PortOfLoadingMapper mapper,
        ILogger<PortOfLoadingAppService> logger)
        : base(portOfLoadingRepository)
    {
        _portOfLoadingRepository = portOfLoadingRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public override async Task<PortOfLoadingDto> CreateAsync(PortOfLoadingCreateUpdateDto input)
    {
        await CheckCreatePolicyAsync();
        var entity = _mapper.CreateEntityWithId(input, Guid.NewGuid());
        entity.CreatedAt = Clock.Now;
        await _portOfLoadingRepository.InsertAsync(entity, autoSave: true);
        return _mapper.MapToDto(entity);
    }

    public override async Task<PortOfLoadingDto> UpdateAsync(Guid id, PortOfLoadingCreateUpdateDto input)
    {
        await CheckUpdatePolicyAsync();
        var entity = await _portOfLoadingRepository.GetAsync(id);
        var originalCreatedBy = entity.CreatedBy;
        var originalCreatedAt = entity.CreatedAt;
        _mapper.MapToEntity(input, entity);
        entity.CreatedBy = originalCreatedBy;
        entity.CreatedAt = originalCreatedAt;
        entity.UpdatedAt = Clock.Now;
        await _portOfLoadingRepository.UpdateAsync(entity, autoSave: true);
        return _mapper.MapToDto(entity);
    }

    public override async Task DeleteAsync(Guid id)
    {
        await CheckDeletePolicyAsync();
        var entity = await _portOfLoadingRepository.GetAsync(id);
        entity.Deleted = "Y";
        entity.UpdatedAt = Clock.Now;
        await _portOfLoadingRepository.UpdateAsync(entity, autoSave: true);
    }

    public override async Task<PortOfLoadingDto> GetAsync(Guid id)
    {
        await CheckGetPolicyAsync();
        var entity = await _portOfLoadingRepository.GetAsync(id);
        return _mapper.MapToDto(entity);
    }

    public override async Task<PagedResultDto<PortOfLoadingDto>> GetListAsync(PagedAndSortedResultRequestDto input)
    {
        await CheckGetListPolicyAsync();
        var queryable = await _portOfLoadingRepository.GetQueryableAsync();
        var sortedQueryable = queryable.OrderBy(input.Sorting.IsNullOrWhiteSpace() ? nameof(PortOfLoading.DocEntry) : input.Sorting);
        var totalCount = await AsyncExecuter.CountAsync(queryable);
        var entities = await AsyncExecuter.ToListAsync(
            sortedQueryable.PageBy(input.SkipCount, input.MaxResultCount)
        );
        var dtos = entities.Select(_mapper.MapToDto).ToList();
        return new PagedResultDto<PortOfLoadingDto>(totalCount, dtos);
    }

    public virtual async Task<PagedResultDto<PortOfLoadingDto>> FilterListAsync(QueryParametersDto parameters)
    {
        var query = await _portOfLoadingRepository.GetQueryableAsync();
        query = ApplyDynamicQuery(query, parameters);
        var totalCount = await AsyncExecuter.CountAsync(query);
        var items = await AsyncExecuter.ToListAsync(
            query.Skip(parameters.SkipCount).Take(parameters.MaxResultCount)
        );
        var dtos = items.Select(_mapper.MapToDto).ToList();
        return new PagedResultDto<PortOfLoadingDto>
        {
            TotalCount = totalCount,
            Items = dtos
        };
    }

    private IQueryable<PortOfLoading> ApplyDynamicQuery(IQueryable<PortOfLoading> query, QueryParametersDto parameters)
    {
        if (parameters.FilterGroup?.Conditions != null && parameters.FilterGroup.Conditions.Count > 0)
        {
            query = DynamicQueryBuilder<PortOfLoading>.ApplyFilters(query, parameters.FilterGroup);
        }
        if (parameters.Sort?.Count > 0)
        {
            query = DynamicQueryBuilder<PortOfLoading>.ApplyMultipleSorting(query, parameters.Sort);
        }
        else if (!string.IsNullOrEmpty(parameters.Sorting))
        {
            query = query.OrderBy(parameters.Sorting);
        }
        else
        {
            query = query.OrderByDescending(x => x.CreationTime);
        }
        return query;
    }
}