using System;
using System.ComponentModel.DataAnnotations;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Entities;

namespace Imip.Ekb.Master.BusinessPartners.Dtos;

public class BusinessPartnerCreateUpdateDto : EntityDto<Guid>, IHasConcurrencyStamp
{
    public string ConcurrencyStamp { get; set; }
    [Required]
    [StringLength(255)]
    public string Name { get; set; } = string.Empty;

    [Required]
    [StringLength(255)]
    public string CreatedBy { get; set; } = string.Empty;

    [Required]
    [StringLength(255)]
    public string Status { get; set; } = string.Empty;

    [StringLength(100)]
    public string? Alias { get; set; }

    [StringLength(255)]
    public string? Image { get; set; }

    [StringLength(200)]
    public string? Direction { get; set; }

    [Required]
    [StringLength(200)]
    public string RegionType { get; set; } = string.Empty;

    [StringLength(255)]
    public string? Address { get; set; }

    [StringLength(255)]
    public string? Tenant { get; set; }

    [StringLength(255)]
    public string? Npwp { get; set; }

    [StringLength(255)]
    public string? Nitku { get; set; }
}