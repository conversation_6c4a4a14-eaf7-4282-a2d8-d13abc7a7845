using System;
using System.Threading.Tasks;
using Imip.Ekb.Master.Tradings.Dtos;
using Imip.Ekb.Models;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.Ekb.Master.Tradings;

public interface ITradingAppService :
    ICrudAppService<TradingDto, Guid, PagedAndSortedResultRequestDto, TradingCreateUpdateDto, TradingCreateUpdateDto>
{
    Task<PagedResultDto<TradingDto>> FilterListAsync(QueryParametersDto parameters);
}