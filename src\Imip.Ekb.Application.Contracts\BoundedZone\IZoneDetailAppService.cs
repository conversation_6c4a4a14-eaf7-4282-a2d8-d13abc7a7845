using System;
using System.Threading.Tasks;
using Imip.Ekb.BoundedZone.Dtos;
using Imip.Ekb.Models;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.Ekb.BoundedZone;

public interface IZoneDetailAppService :
    ICrudAppService<ZoneDetailDto, Guid, PagedAndSortedResultRequestDto, CreateUpdateVesselItemDto, CreateUpdateVesselItemDto>
{
    Task<PagedResultDto<ZoneDetailDto>> FilterListAsync(QueryParametersDto queryParametersDto);
    Task<PagedResultDto<ZoneDetailWithVesselHeadersDto>> QueryableWithVesselHeadersDtoAsync(QueryParametersDto parameters);
}