﻿using System;

namespace Imip.Ekb.Attributes;
[AttributeUsage(AttributeTargets.Class, AllowMultiple = true)]
public class AutoMapAttribute : Attribute
{
    public Type[] TargetTypes { get; }
    public MemberList MemberList { get; set; } = MemberList.Destination;

    public AutoMapAttribute(params Type[] targetTypes)
    {
        TargetTypes = targetTypes;
    }
}

[AttributeUsage(AttributeTargets.Class, AllowMultiple = true)]
public class AutoMapFromAttribute : Attribute
{
    public Type[] TargetTypes { get; }

    public AutoMapFromAttribute(params Type[] targetTypes)
    {
        TargetTypes = targetTypes;
    }
}

[AttributeUsage(AttributeTargets.Class, AllowMultiple = true)]
public class AutoMapToAttribute : Attribute
{
    public Type[] TargetTypes { get; }

    public AutoMapToAttribute(params Type[] targetTypes)
    {
        TargetTypes = targetTypes;
    }
}

public enum MemberList
{
    None,
    Source,
    Destination
}