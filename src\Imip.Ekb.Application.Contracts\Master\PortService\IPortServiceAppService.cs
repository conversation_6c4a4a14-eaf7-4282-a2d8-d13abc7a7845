using System;
using System.Threading.Tasks;
using Imip.Ekb.Master.PortServices.Dtos;
using Imip.Ekb.Models;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.Ekb.Master.PortServices;

public interface IPortServiceAppService :
    ICrudAppService<PortServiceDto, Guid, PagedAndSortedResultRequestDto, PortServiceCreateUpdateDto, PortServiceCreateUpdateDto>
{
    Task<PagedResultDto<PortServiceDto>> FilterListAsync(QueryParametersDto parameters);
}