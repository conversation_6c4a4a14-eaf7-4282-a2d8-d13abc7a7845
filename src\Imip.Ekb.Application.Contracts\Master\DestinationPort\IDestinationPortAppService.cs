using System;
using System.Threading.Tasks;
using Imip.Ekb.Master.DestinationPorts.Dtos;
using Imip.Ekb.Models;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

public interface IDestinationPortAppService :
    ICrudAppService<DestinationPortDto, Guid, PagedAndSortedResultRequestDto, DestinationPortCreateUpdateDto, DestinationPortCreateUpdateDto>
{
    Task<PagedResultDto<DestinationPortDto>> FilterListAsync(QueryParametersDto parameters);
}