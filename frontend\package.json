{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "gen-api": "curl -k http://localhost:5000/swagger/v1/swagger.json -o swagger.json && npx @hey-api/openapi-ts -i swagger.json -o src/client -c @hey-api/client-fetch && pnpm openapi-ts", "preview": "vite preview", "openapi-ts": "openapi-ts"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@fontsource/inter": "^5.2.6", "@fontsource/poppins": "^5.2.6", "@fontsource/public-sans": "^5.2.6", "@fontsource/roboto": "^5.2.6", "@handsontable/vue3": "^16.0.1", "@hey-api/client-fetch": "^0.13.1", "@hey-api/openapi-ts": "^0.80.5", "@mdi/js": "^7.4.47", "@tanstack/vue-query": "^5.83.1", "@tsconfig/node20": "^20.1.6", "@typescript-eslint/parser": "^8.39.0", "ant-design-vue": "^4.2.6", "handsontable": "^16.0.1", "openid-client": "^6.6.3", "pinia": "^3.0.3", "vee-validate": "^4.15.1", "vite-plugin-vuetify": "^2.1.2", "vue": "^3.5.18", "vue-router": "^4.5.1", "vue-tabler-icons": "^2.21.0", "vue3-perfect-scrollbar": "^2.0.0", "vuetify": "^3.9.4"}, "devDependencies": {"@eslint/js": "^9.32.0", "@types/node": "^24.2.0", "@vitejs/plugin-vue": "^6.0.1", "@vue/eslint-config-prettier": "^10.2.0", "@vue/tsconfig": "^0.7.0", "eslint": "^9.32.0", "eslint-plugin-vue": "^10.4.0", "prettier": "^3.6.2", "sass": "^1.90.0", "sass-loader": "^16.0.5", "typescript": "~5.8.3", "typescript-eslint": "^8.39.0", "vite": "^7.1.0", "vue-cli-plugin-vuetify": "^2.5.8", "vue-tsc": "^3.0.5"}}