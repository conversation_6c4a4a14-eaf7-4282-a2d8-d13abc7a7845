﻿using Imip.Ekb.BoundedZone;
using Imip.Ekb.Mapping;
using Imip.Ekb.Mapping.Mappers;
using Imip.Ekb.Vessel;
using Microsoft.Extensions.DependencyInjection;
using System.Linq;
using Volo.Abp.Account;
using Volo.Abp.AutoMapper;
using Volo.Abp.FeatureManagement;
using Volo.Abp.Identity;
using Volo.Abp.Modularity;
using Volo.Abp.PermissionManagement;
using Volo.Abp.SettingManagement;
using Volo.Abp.TenantManagement;

namespace Imip.Ekb;

[DependsOn(
    typeof(EkbDomainModule),
    typeof(EkbApplicationContractsModule),
    typeof(AbpPermissionManagementApplicationModule),
    typeof(AbpFeatureManagementApplicationModule),
    typeof(AbpIdentityApplicationModule),
    typeof(AbpAccountApplicationModule),
    typeof(AbpTenantManagementApplicationModule),
    typeof(AbpSettingManagementApplicationModule)
    )]
public class EkbApplicationModule : AbpModule
{
    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        var services = context.Services;

        // Configure AutoMapper (existing functionality)
        Configure<AbpAutoMapperOptions>(options =>
        {
            options.AddMaps<EkbApplicationModule>();
        });

        // Configure MaxMaxResultCount to allow larger result sets
        Volo.Abp.Application.Dtos.LimitedResultRequestDto.MaxMaxResultCount = 50000; // Increase from default 1000 to 50000

        // Register Mapperly mappers
        //services.AddTransient<IMappingContext, MappingContext>();

        //// Replace AutoMapper's IObjectMapper with Mapperly implementation
        //services.Replace(ServiceDescriptor.Transient<IObjectMapper, MapperlyObjectMapper>());

        // Auto-register Mapperly mapper classes
        ConfigureMapperlyMappers(services);

        // Manually register application services that are not auto-registered
        ConfigureApplicationServices(services);
    }

    private void ConfigureApplicationServices(IServiceCollection services)
    {
        // Register BoundedZoneAppService manually since it's in a different namespace
        services.AddTransient<IZoneDetailAppService, BoundedZoneAppService>();
    }

    private void ConfigureMapperlyMappers(IServiceCollection services)
    {
        // Register all Mapperly mapper classes that have the [Mapper] attribute
        var mapperTypes = typeof(EkbApplicationModule).Assembly
             .GetTypes()
             .Where(t => typeof(IMapperlyMapper).IsAssignableFrom(t) && t.IsClass && !t.IsAbstract)
             .ToArray();

        foreach (var mapperType in mapperTypes)
        {
            // Skip BaseEntityMapper as it's abstract and used as base class
            if (mapperType == typeof(BaseEntityMapper))
                continue;

            services.AddTransient(mapperType);
        }
    }
}
